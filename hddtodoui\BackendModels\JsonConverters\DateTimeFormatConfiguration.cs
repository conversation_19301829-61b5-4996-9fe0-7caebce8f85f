using System;
using System.Collections.Generic;
using System.Configuration;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// DateTime格式配置管理类
    /// 支持通过配置文件、环境变量等方式动态配置DateTime格式
    /// </summary>
    public static class DateTimeFormatConfiguration
    {
        private static string? _overrideFormat;

        /// <summary>
        /// 设置格式覆盖（用于测试或特殊场景）
        /// </summary>
        /// <param name="format">要使用的格式</param>
        public static void SetFormatOverride(string format)
        {
            _overrideFormat = format;
        }

        /// <summary>
        /// 清除格式覆盖
        /// </summary>
        public static void ClearFormatOverride()
        {
            _overrideFormat = null;
        }

        /// <summary>
        /// 获取当前应该使用的DateTime格式
        /// 优先级：覆盖格式 > 环境变量 > 配置文件 > 默认格式
        /// </summary>
        /// <returns>当前应该使用的DateTime格式</returns>
        public static string GetCurrentFormat()
        {
            // 1. 检查是否有格式覆盖（用于测试）
            if (!string.IsNullOrEmpty(_overrideFormat))
            {
                return _overrideFormat;
            }

            // 2. 检查环境变量
            var envFormat = Environment.GetEnvironmentVariable("HDDTODO_DATETIME_FORMAT");
            if (!string.IsNullOrEmpty(envFormat))
            {
                return envFormat;
            }

            // 3. 检查配置文件（如果使用传统的app.config）
            try
            {
                var configFormat = ConfigurationManager.AppSettings["DateTimeFormat"];
                if (!string.IsNullOrEmpty(configFormat))
                {
                    return configFormat;
                }
            }
            catch
            {
                // 忽略配置读取错误，使用默认格式
            }

            // 4. 返回默认格式
            return DateTimeFormats.Current;
        }

        /// <summary>
        /// 根据应用模式获取推荐格式
        /// </summary>
        /// <param name="isInternational">是否为国际化模式</param>
        /// <returns>推荐的DateTime格式</returns>
        public static string GetRecommendedFormat(bool isInternational = false)
        {
            return isInternational 
                ? DateTimeFormats.GetInternationalFormat() 
                : DateTimeFormats.Current;
        }

        /// <summary>
        /// 验证格式字符串是否有效
        /// </summary>
        /// <param name="format">要验证的格式字符串</param>
        /// <returns>格式是否有效</returns>
        public static bool IsValidFormat(string format)
        {
            if (string.IsNullOrEmpty(format))
                return false;

            try
            {
                var testDate = DateTime.Now;
                var formatted = testDate.ToString(format);
                var parsed = DateTime.ParseExact(formatted, format, null);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取所有预定义的格式选项
        /// </summary>
        /// <returns>格式选项字典</returns>
        public static Dictionary<string, string> GetFormatOptions()
        {
            return new Dictionary<string, string>
            {
                ["current"] = DateTimeFormats.Current,
                ["international"] = DateTimeFormats.ISO8601WithTimeZone,
                ["utc"] = DateTimeFormats.ISO8601UTC,
                ["simple"] = DateTimeFormats.ISO8601Simple
            };
        }
    }
}
