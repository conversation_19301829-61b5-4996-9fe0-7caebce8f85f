﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\CompletedTaskListsDialog.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "9ECD767654EFB2392CB55C516A191653BCE8C009403C24B0E5F03B3665A7041E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class CompletedTaskListsDialog : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\CompletedTaskListsDialog.xaml line 46
                {
                    this.CompletedTaskListsView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 3: // Controls\CompletedTaskListsDialog.xaml line 97
                {
                    global::Microsoft.UI.Xaml.Controls.Button element3 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element3).Click += this.CloseButton_Click;
                }
                break;
            case 4: // Controls\CompletedTaskListsDialog.xaml line 83
                {
                    this.PreviousPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PreviousPageButton).Click += this.PreviousPageButton_Click;
                }
                break;
            case 5: // Controls\CompletedTaskListsDialog.xaml line 87
                {
                    this.PageInfoTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 6: // Controls\CompletedTaskListsDialog.xaml line 90
                {
                    this.NextPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.NextPageButton).Click += this.NextPageButton_Click;
                }
                break;
            case 8: // Controls\CompletedTaskListsDialog.xaml line 69
                {
                    global::Microsoft.UI.Xaml.Controls.Button element8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element8).Click += this.SetIncompleteButton_Click;
                }
                break;
            case 9: // Controls\CompletedTaskListsDialog.xaml line 33
                {
                    this.FilterTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.FilterTextBox).KeyDown += this.FilterTextBox_KeyDown;
                }
                break;
            case 10: // Controls\CompletedTaskListsDialog.xaml line 39
                {
                    this.FilterButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.FilterButton).Click += this.FilterButton_Click;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

