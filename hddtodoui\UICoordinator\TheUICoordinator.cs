using System;
using System.Threading.Tasks;
using Windows.Graphics;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;

namespace HddtodoUI.UICoordinator;

public class TheUICoordinator
{
    private static readonly Lazy<TheUICoordinator> _lazyInstance =
        new Lazy<TheUICoordinator>(() => new TheUICoordinator(), isThreadSafe: true);

    private IndicatorWindow _indicatorWindow;
    private LoginWindow _loginWindow;
    private MainWindow _mainWindow;
    private TaskDetailsWindow _taskDetailsWindow;
    private TaskStepWindow _taskStepWindow;
    private ITaskStepStore _taskStepStore = StoreFactoryHolder.getTaskStepStore();
    private PointInt32? _taskStepWindowLastPosition;

    private TheUICoordinator()
    {
        // 不持久化窗口位置，启动时无记录
    }

    public static TheUICoordinator Instance => _lazyInstance.Value;

    public TaskDetailsWindow GetTaskDetailsWindow()
    {
        EnsureWindow(ref _taskDetailsWindow, () => new TaskDetailsWindow());
        return _taskDetailsWindow;
    }

    public IndicatorWindow GetIndicatorWindow()
    {
        EnsureWindow(ref _indicatorWindow, () => new IndicatorWindow());
        return _indicatorWindow;
    }

    public MainWindow GetMainWindow()
    {
        EnsureWindow(ref _mainWindow, () => new MainWindow());
        return _mainWindow;
    }

    public LoginWindow GetLoginWindow()
    {
        EnsureWindow(ref _loginWindow, () => new LoginWindow());
        return _loginWindow;
    }

    // 修复：使用局部变量和事件处理来管理窗口生命周期
    private void EnsureWindow<T>(ref T window, Func<T> factory) where T : Window
    {
        // if (window == null || window.AppWindow == null || !window.AppWindow.IsVisible)
        // {
        //     T newWindow = factory();
        //     // 在赋值前订阅事件，避免重复订阅
        //     newWindow.Closed += Window_Closed;
        //     window = newWindow; // 赋值给 ref 参数
        // }

        if (window == null || window.AppWindow == null)
        {
            T newWindow = factory();
            // 在赋值前订阅事件，避免重复订阅
            newWindow.Closed += Window_Closed;
            window = newWindow; // 赋值给 ref 参数
        }
    }

    private void Window_Closed(object sender, WindowEventArgs args)
    {
        if (sender is IndicatorWindow indicator && ReferenceEquals(indicator, _indicatorWindow))
        {
            _indicatorWindow = null;
        }
        else if (sender is MainWindow main && ReferenceEquals(main, _mainWindow))
        {
            _mainWindow = null;
        }
        else if (sender is TaskDetailsWindow details && ReferenceEquals(details, _taskDetailsWindow))
        {
            _taskDetailsWindow = null;
        }
        else if (sender is LoginWindow login && ReferenceEquals(login, _loginWindow))
        {
            _loginWindow = null;
        }
        else if (sender is TaskStepWindow taskStep && ReferenceEquals(taskStep, _taskStepWindow))
        {
            _taskStepWindow = null;
        }
    }


    public void RemoveTaskFromTaskPanelCurrentUncompletedTaskList(long taskId)
    {
        var window = GetMainWindow();
        window.GetMainView().RemoveTaskFormUncompletedTaskPanel(taskId);
    }

    public void RemoveTaskFromTaskPanelCurrentCompletedTaskList(TTask task)
    {
        var window = GetMainWindow();
        window.GetMainView().RemoveTaskFormCompletedTaskPanel(task.TaskID);
    }

    public void RemoveTaskListFromTaskListPanel(string key)
    {
        var window = GetMainWindow();
        window.GetMainView().RemoveUserCategory(key);
    }

    public void AddTaskToTaskPanelCurrentUncompletedTaskList(TTask ttask)
    {
        var window = GetMainWindow();
        window.GetMainView().AddTaskToTaskPanelUnCompletedTaskListLast(ttask);
    }

    public void AddTaskToTaskPanelCurrentCompletedTaskListFirst(TTask ttask)
    {
        var window = GetMainWindow();
        window.GetMainView().AddTaskToTaskPanelCurrentCompletedTaskListFirst(ttask);
    }

    public void UpdateCateogryUncompletedTaskCount(string key, int count)
    {
        var window = GetMainWindow();
        window.GetMainView().UpdateUserCategoryUncompletedTaskCount(key, count);
    }

    public void UpdateSystemCategory()
    {
        var window = GetMainWindow();
        window.GetMainView().InitializeSystemCategoryUncompletedTaskList();
    }

    public bool UpdateUserCategory(String categoryKey, TaskCategoryViewObject updatedCategory)
    {
        var window = GetMainWindow();
        return window.GetMainView().UpdateUserCategory(categoryKey, updatedCategory);
    }

    public TodoTaskViewObject GetTaskFromTaskPanelBottom(long taskId)
    {
        var window = GetMainWindow();
        return window.GetMainView().GetTaskFromTaskPanelCategoryBottom(taskId);
    }

    public TodoTaskViewObject GetTaskFromTaskPanelTop(long taskId)
    {
        var window = GetMainWindow();
        return window.GetMainView().GetTaskFromTaskPanelCategoryTop(taskId);
    }

    public void RefreshTaskPanelUncompletedTaskListView()
    {
        GetMainWindow().GetMainView().ReloadTaskPanelUncompletedTaskListView();
    }

    public void ShowIndicatorWindow()
    {
        var window = GetIndicatorWindow();
        window.AppWindow.Show();
        window.AppWindow.MoveInZOrderAtTop();
    }

    public void HideIndicatorWindow()
    {
        var window = GetIndicatorWindow();
        if (window.AppWindow?.IsVisible == true)
        {
            window.AppWindow.Hide();
        }
    }

    public void ShowTaskDetailsWindow(TodoTaskViewObject ttvo, AppWindow from = null)
    {
        var mainWindow = GetMainWindow();
        var detailsWindow = GetTaskDetailsWindow();

        if (mainWindow.Visible)
            SyncWindowProperties(mainWindow.AppWindow, detailsWindow.AppWindow);

        // 打开任务详情窗口前隐藏伴生 TaskStepWindow
        HideTaskStepWindow();

        detailsWindow.AppWindow.Show();
        detailsWindow.ShowAndActivate(ttvo);
        detailsWindow.FromParent = from;
        if (from != null)
            from.MoveInZOrderBelow(WindowsUtils.GetAppWindowID(detailsWindow));
        detailsWindow.AppWindow.MoveInZOrderAtTop();

        HideMainWindow();
    }

    public void ShowMainWindow()
    {
        try
        {
            var mainWindow = GetMainWindow();
            var detailsWindow = GetTaskDetailsWindow();

            if (detailsWindow.Visible)
                SyncWindowProperties(detailsWindow.AppWindow, mainWindow.AppWindow);

            mainWindow.AppWindow.Show();
            mainWindow.AppWindow.MoveInZOrderAtTop();
            WindowsUtils.RestoreWindow(mainWindow);
            mainWindow.Activate();

            HideTaskDetailsWindow();
        }
        catch (Exception e)
        {
            LogService.Instance.Error(e.StackTrace);
            throw;
        }
    }
    
    

    public void ToggleMainWindow()
    {
        var mainWindow = GetMainWindow();
        if (mainWindow.Visible)
        {
            HideMainWindow();
        }
        else
        {
            ShowMainWindow();
        }
    }

    public void DetailWindowShowFromParentWindow()
    {
        var detailsWindow = GetTaskDetailsWindow();
        detailsWindow.AppWindow.Hide();

        var fromParent = GetTaskDetailsWindow().FromParent;
        if (fromParent != null && fromParent.Presenter != null)
        {
            fromParent.Show();
            fromParent.MoveInZOrderAtTop();
        }
        else
        {
            ShowMainWindow();
        }
    }

    public void HideMainWindow()
    {
        try
        {
            var window = GetMainWindow();
            if (window.AppWindow?.IsVisible == true)
            {
                window.AppWindow.Hide();
            }
        }
        catch (Exception e)
        {
            LogService.Instance.Error(e.StackTrace);
            throw;
        }
    }

    public void HideTaskDetailsWindow()
    {
        var window = GetTaskDetailsWindow();
        if (window.AppWindow?.IsVisible == true)
        {
            window.AskForSaveBeforeSwitchOrClose();
            window.AppWindow.Hide();

            // 任务详情窗口关闭后，尝试恢复 TaskStepWindow（若当前任务有步骤）
            var currentTomato = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask();
            if (currentTomato != null )
            {
                if (currentTomato.IsStarted())
                {
                    var indicator = GetIndicatorWindow();
                    if (indicator != null)
                    {
                        UpdateTaskStepWindow(currentTomato.getTask(), indicator.AppWindow, false);
                    }
                }
               
            }
        }
    }
    
    public void OnTaskListAdded()
    {
        GetTaskDetailsWindow().GetTaskDetailsControl().LoadCategories();
    }

    private void SyncWindowProperties(AppWindow source, AppWindow target)
    {
        if (source == null || target == null) return;

        target.Resize(source.Size);
        target.Move(source.Position);
    }
    
    public void SetTaskCategoryListViewCanotReOrder()
    {
        var window = GetMainWindow();
        window.GetMainView().SetTaskCategoryPanelListViewCanNotReorder();
    }
    
    public void SetTaskCategoryListViewCanReOrder()
    {
        var window = GetMainWindow();
        window.GetMainView().SetTaskCategoryPanelListViewCanReorder();
    }

    public void OnTaskCategoryUnCompleted(string parentKey, TaskCategoryViewObject chldCategory)
    {
        GetMainWindow().GetMainView().TaskCategoryPanelAddChildUserCategoryToParent(parentKey, chldCategory);
    }

    public void OnTaskCategoryCompleted(TaskCategoryViewObject category)
    {
        GetMainWindow().GetMainView().TaskPanelAddCompletedTaskCategoryToCompletedCategoriesView(category);
    }

    public async Task TaskSystemCategorySelected(TaskCategoryViewObject category)
    {
        await GetMainWindow().GetMainView().TaskSystemCategorySelected(category);
    }
    
    public void TaskUserCategorySelected(TaskCategoryViewObject category)
    {
         GetMainWindow().GetMainView().TaskUserCategorySelected(category);
    }
    
    public void TaskCategorySwitchToUserCategory(TaskCategoryViewObject category)
    {
        GetMainWindow().GetMainView().SwitchToUserCategory(category);
    }
    
    // ================= TaskStepWindow Management =================
    public bool IsTaskStepWindowVisible => _taskStepWindow != null && _taskStepWindow.AppWindow?.IsVisible == true;

    public void ToggleTaskStepWindow(TTask task, AppWindow indicatorAppWindow)
    {
        if (!ConfigSettingsUtils.GetTaskStepWindowEnabledConfig()) return;
        if (IsTaskStepWindowVisible)
        {
            HideTaskStepWindow();
        }
        else
        {
            UpdateTaskStepWindow(task, indicatorAppWindow, true);
        }
    }

    public void UpdateTaskStepWindow(TTask task, AppWindow indicatorAppWindow, bool forceShow = false)
    {
        if (!ConfigSettingsUtils.GetTaskStepWindowEnabledConfig())
        {
            HideTaskStepWindow();
            return;
        }
        if (task == null || indicatorAppWindow == null) return;

        var steps = _taskStepStore.GetTaskSteps(task.TaskID, UserInfoHolder.getUserId())?.FindAll(s => !s.DeletedStatus);
        bool hasSteps = steps != null && steps.Count > 0;

        if (hasSteps || forceShow)
        {
            if (_taskStepWindow == null)
            {
                _taskStepWindow = new TaskStepWindow();
                _taskStepWindow.Closed += (_, _) => { _taskStepWindow = null; };
                // 监听位置变化以保存
                _taskStepWindow.AppWindow.Changed += TaskStepWindow_OnAppWindowChanged;
            }

            _taskStepWindow.SetTask(task);

            if (_taskStepWindowLastPosition.HasValue)
            {
                _taskStepWindow.AppWindow.Move(_taskStepWindowLastPosition.Value);
            }
            else
            {
                var myPos = indicatorAppWindow.Position;
                var mySize = indicatorAppWindow.Size;

                // 预期位置：主窗右侧 8 像素
                var desiredPos = new PointInt32(myPos.X + mySize.Width + 8, myPos.Y);
                var stepSize = _taskStepWindow.AppWindow.Size;

                // 获取当前屏幕工作区
                var displayArea = Microsoft.UI.Windowing.DisplayArea.Primary;
                if (displayArea != null)
                {
                    var workArea = displayArea.WorkArea;
                    // 调整 X
                    int workRight = workArea.X + workArea.Width;
                    int workBottom = workArea.Y + workArea.Height;

                    if (desiredPos.X + stepSize.Width > workRight)
                    {
                        desiredPos.X = Math.Max(workRight - stepSize.Width, workArea.X);
                    }
                    if (desiredPos.Y + stepSize.Height > workBottom)
                    {
                        desiredPos.Y = Math.Max(workBottom - stepSize.Height, workArea.Y);
                    }
                    if (desiredPos.X < workArea.X) desiredPos.X = workArea.X;
                    if (desiredPos.Y < workArea.Y) desiredPos.Y = workArea.Y;
                }

                _taskStepWindow.AppWindow.Move(desiredPos);
            }

            if (!_taskStepWindow.AppWindow.IsVisible)
                _taskStepWindow.AppWindow.Show();
        }
        else
        {
            HideTaskStepWindow();
        }
    }

    public void HideTaskStepWindow()
    {
        if (_taskStepWindow != null && _taskStepWindow.AppWindow?.IsVisible == true)
        {
            _taskStepWindow.AppWindow.Hide();
        }
    }

    private void TaskStepWindow_OnAppWindowChanged(AppWindow sender, AppWindowChangedEventArgs args)
    {
        if (args.DidPositionChange)
        {
            _taskStepWindowLastPosition = sender.Position;
        }
    }
    // ============================================================

    public async Task ReloadPlannedProjectPanelAsync()
    {
        var window = GetMainWindow();
        await window.GetMainView().ReloadPlannedProjectsPanelAsync();
    }

    public void RemoveUserTaskCategoryFromPanel(TaskCategoryViewObject category)
    {
        var window = GetMainWindow();
        window.GetMainView().RemoveUserTaskCategoryFromPanel(category);
    }
}