using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.Utilities;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore.cached
{
    public class CachedTaskCategoryStore : ITaskCategoryStore
    {
        private readonly ITaskCategoryStore _underlyingStore;
        private readonly ConcurrentDictionary<string, CacheEntry<TaskCategory>> _categoryCache;
        private readonly ConcurrentDictionary<string, CacheEntry<List<TaskCategoryWithCount>>> _categoriesWithCountCache;
        private readonly TimeSpan _defaultCacheExpiration = TimeSpan.FromMinutes(5);

        public CachedTaskCategoryStore(ITaskCategoryStore underlyingStore)
        {
            _underlyingStore = underlyingStore ?? throw new ArgumentNullException(nameof(underlyingStore));
            _categoryCache = new ConcurrentDictionary<string, CacheEntry<TaskCategory>>();
            _categoriesWithCountCache = new ConcurrentDictionary<string, CacheEntry<List<TaskCategoryWithCount>>>();
        }

        private string GetCategoryCacheKey(string key, long userId) => $"{key}_{userId}";
        private string GetCategoriesWithCountCacheKey(long userId, string parentKey) => $"categories_{userId}_{parentKey ?? "null"}";

        public Task<List<TaskCategory>> GetTopLevelUncompletedWithDueCategoriesAsync(long userId)
        {
           return _underlyingStore.GetTopLevelUncompletedWithDueCategoriesAsync(userId);
        }
        
        public CountResponse GetTopLevelUncompletedWithDueCategoriesCount(long userId)
        {
            return _underlyingStore.GetTopLevelUncompletedWithDueCategoriesCount(userId);
        }

        public Task<List<TaskCategory>> GetUncompletedWithDueCategoriesAsync(long userId)
        {
            return _underlyingStore.GetUncompletedWithDueCategoriesAsync(userId);
        }

        public CountResponse GetUncompletedWithDueCategoriesCount(long userId)
        {
           return _underlyingStore.GetUncompletedWithDueCategoriesCount(userId);
        }

        public List<TaskCategoryWithCount> GetUncompleteTaskCategoriesWithCount(long userId, string parentKey = null)
        {
            // var cacheKey = GetCategoriesWithCountCacheKey(userId, parentKey);
            //
            // if (_categoriesWithCountCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            // {
            //     return cachedEntry.Value;
            // }

            var result = _underlyingStore.GetUncompleteTaskCategoriesWithCount(userId, parentKey);
            
            // 更新缓存
            //_categoriesWithCountCache[cacheKey] = new CacheEntry<List<TaskCategoryWithCount>>(result, _defaultCacheExpiration);
            
            // 同时更新单个分类的缓存
            // foreach (var item in result)
            // {
            //     var categoryCacheKey = GetCategoryCacheKey(item.Category.Key, userId);
            //     _categoryCache[categoryCacheKey] = new CacheEntry<TaskCategory>(item.Category, _defaultCacheExpiration);
            // }

            return result;
        }

        public Task<List<TaskCategoryWithCount>> GetUncompletedWithDueTaskCountAsync(long userId)
        {
            return _underlyingStore.GetUncompletedWithDueTaskCountAsync(userId);
        }

        public TaskCategory GetTaskCategoryByKey(string key, long userId)
        {
            var cacheKey = GetCategoryCacheKey(key, userId);
            
            if (_categoryCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                return cachedEntry.Value;
            }

            var result = _underlyingStore.GetTaskCategoryByKey(key, userId);
            
            if (result != null)
            {
                _categoryCache[cacheKey] = new CacheEntry<TaskCategory>(result, _defaultCacheExpiration);
            }

            return result;
        }

        public async Task<TaskCategory> CreateTaskCategory(string name, long userId, string parentCategoryKey = null, DateTime? dueTime = null, bool isHide = false)
        {
            var result = await _underlyingStore.CreateTaskCategory(name, userId, parentCategoryKey, dueTime, isHide);
            
            if (result != null)
            {
                // 使相关缓存失效
                InvalidateUserCaches(userId);
                
                // 添加新创建的分类到缓存
                var cacheKey = GetCategoryCacheKey(result.Key, userId);
                _categoryCache[cacheKey] = new CacheEntry<TaskCategory>(result, _defaultCacheExpiration);
            }
            
            return result;
        }

        public async Task<TaskCategory> UpdateTaskCategory(string categoryKey, long userId,  UpdateValue<DateTime?> categoryCompleteTime = null, 
            UpdateValue<DateTime?> categoryDueTime = null, string name = null,  bool? isHide = null, int? categoryOrder = null)
        {
            var result = await _underlyingStore.UpdateTaskCategory(categoryKey, userId, categoryCompleteTime,categoryDueTime,name,isHide, categoryOrder);
            
            if (result != null)
            {
                // 使相关缓存失效
                InvalidateUserCaches(userId);
                
                // 更新该分类的缓存
                var cacheKey = GetCategoryCacheKey(categoryKey, userId);
                _categoryCache[cacheKey] = new CacheEntry<TaskCategory>(result, _defaultCacheExpiration);
            }
            
            return result;
        }

        public int GetCategoryUncompleteTaskCount(string categoryKey, long userId)
        {
            return _underlyingStore.GetCategoryUncompleteTaskCount(categoryKey, userId);
        }

        public int GetCategoryCompleteTaskCount(string categoryKey, long userId)
        {
            return _underlyingStore.GetCategoryCompleteTaskCount(categoryKey, userId);
        }

        public Task<RepositionCategoriesResponse> RepositionCategoriesAsync(long userId, List<RepositionCategoryItem> itemsToReposition, string newParentKey = null, string previousParentKey = null)
        {
            // 使相关缓存失效
            InvalidateUserCaches(userId);
            return _underlyingStore.RepositionCategoriesAsync(userId, itemsToReposition, newParentKey,previousParentKey);
        }

        public List<TaskCategory> GetCompletedTaskCategoriesAsync(long userId, string parentKey = null, string keyword = null, int? page = null,
            int? pageSize = null)
        {
            return _underlyingStore.GetCompletedTaskCategoriesAsync(userId, parentKey, keyword, page, pageSize);
        }

        public CountResponse GetCompletedTaskCategoriesCountAsync(long userId, string parentKey = null, string keyword = null)
        {
            return _underlyingStore.GetCompletedTaskCategoriesCountAsync(userId, parentKey, keyword); 
        }

        private void InvalidateUserCaches(long userId)
        {
            // 移除所有该用户的分类列表缓存
            var keysToRemove = new List<string>();
            foreach (var key in _categoriesWithCountCache.Keys)
            {
                if (key.StartsWith($"categories_{userId}_"))
                {
                    keysToRemove.Add(key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                _categoriesWithCountCache.TryRemove(key, out _);
            }
            
            // 移除该用户的单个分类缓存
            keysToRemove.Clear();
            foreach (var key in _categoryCache.Keys)
            {
                if (key.EndsWith($"_{userId}"))
                {
                    keysToRemove.Add(key);
                }
            }
            
            foreach (var key in keysToRemove)
            {
                _categoryCache.TryRemove(key, out _);
            }
        }
    }
}
