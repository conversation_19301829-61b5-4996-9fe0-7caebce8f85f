# TasksPanel 统一ListView实现方案

## 概述
使用ListView分组功能，将TaskItemControl和TaskCategoryItemForPlanControl都显示在同一个ListView中，实现真正的统一滚动体验。

## 实现方案

### 1. 数据模型设计
创建了统一的数据模型 `TaskListItem`：
- 支持两种类型：Task（任务）和SubCategory（子分类）
- 包含分组信息：GroupKey 和 GroupName
- 提供静态工厂方法创建不同类型的项目

### 2. 模板选择器
创建了 `TaskListItemTemplateSelector`：
- 根据项目类型自动选择对应的DataTemplate
- TaskTemplate：使用 TaskItemControl 显示任务
- SubCategoryTemplate：使用 TaskCategoryItemForPlanControl 显示子分类

### 3. ListView配置
- 使用 ItemTemplateSelector 替代固定的 ItemTemplate
- 配置分组显示，支持"任务"和"子分类"两个分组
- 保持原有的拖拽、选择等功能

### 4. 数据管理
- 新增 UnifiedTaskList 集合存储统一的列表项
- LoadSubCategories() 方法加载子分类数据
- UpdateUnifiedTaskList() 方法合并任务和子分类数据并设置分组

## 核心文件

### 1. TaskListItem.cs (新增)
```csharp
public class TaskListItem
{
    public enum ItemType { Task, SubCategory }
    public ItemType Type { get; set; }
    public TodoTaskViewObject Task { get; set; }
    public TaskCategoryWithCount SubCategory { get; set; }
    public string GroupKey { get; set; }
    public string GroupName { get; set; }
    
    public static TaskListItem CreateTaskItem(TodoTaskViewObject task);
    public static TaskListItem CreateSubCategoryItem(TaskCategoryWithCount subCategory);
}
```

### 2. TaskListItemTemplateSelector.cs (新增)
```csharp
public class TaskListItemTemplateSelector : DataTemplateSelector
{
    public DataTemplate TaskTemplate { get; set; }
    public DataTemplate SubCategoryTemplate { get; set; }
    
    protected override DataTemplate SelectTemplateCore(object item);
}
```

### 3. TasksPanel.xaml (修改)
- 添加命名空间引用：selectors, backendStore
- 添加模板选择器资源定义
- 修改ListView使用ItemTemplateSelector
- 更新分组标题显示逻辑

### 4. TasksPanel.xaml.cs (修改)
- 添加 SubCategories 和 UnifiedTaskList 属性
- 修改 CurrentCategory setter 调用子分类加载
- 实现 LoadSubCategories() 和 UpdateUnifiedTaskList() 方法
- 修改拖拽逻辑，确保只有任务可以拖拽
- 更新数据源绑定逻辑

## 分组顺序
按照用户要求：
1. **任务分组** - 显示所有任务项
2. **子分类分组** - 显示所有子分类项

## 功能特点

### ✅ 优势
- **真正的统一滚动**：任务和子分类在同一个ListView中，共享滚动条
- **保持ListView功能**：拖拽、选择、虚拟化等功能完全保留
- **灵活的分组显示**：可以控制分组标题和顺序
- **类型安全**：通过模板选择器确保正确的控件渲染
- **拖拽控制**：只允许任务拖拽，子分类不可拖拽

### ⚠️ 注意事项
- **拖拽逻辑调整**：需要处理TaskListItem而不是直接的TodoTaskViewObject
- **数据同步**：任务变化时需要同时更新UnifiedTaskList
- **性能考虑**：大量数据时需要注意分组和模板选择的性能

## 用户体验
1. 用户选择一个有子分类的分类
2. 任务显示在"任务"分组下
3. 子分类显示在"子分类"分组下
4. 用户可以连续滚动浏览所有内容
5. 点击子分类可以切换到该分类
6. 只有任务支持拖拽重排

## 测试建议
1. 创建有子分类的用户分类进行测试
2. 验证任务和子分类的正确显示
3. 测试拖拽功能（只有任务可拖拽）
4. 验证分组标题显示
5. 测试滚动体验的流畅性

## 状态
- ✅ 核心实现完成
- ✅ 编译检查通过
- ✅ 拖拽逻辑调整完成
- 🔄 等待实际环境测试
