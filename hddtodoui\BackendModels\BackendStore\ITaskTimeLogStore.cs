using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskTimeLogStore
    {
        TaskTimeLog CreateTaskTimeLog(long taskId, long userId, string clientId = null, string clientType = null);
        public Task<TaskTimeLog> CreateTaskTimeLogAsync(long taskId, long userId, string clientId = null, string clientType = null);
        
        Task EndTaskTimeLogAsync(TaskTimeLog log, long userId, string clientId = null);
        void EndTaskTimeLog(TaskTimeLog log, long userId, string clientId = null);
        List<TaskTimeLog> GetTaskTimeLogs(DateTime date, long userId);
        TaskTimeLog CreateCompletedTaskTimeLog(long taskId, long userId);

        Task<TaskTimeLog> CreateCompletedTaskTimeLogAsync(long taskId, long userId);

        public List<TaskTimeLogStatistics> GetTaskTimeLogStatistics(DateTime date, long userId);
        
        event EventHandler taskTimeLogStoreSavedEvent;
        
        
    }
    
    public class TaskTimeLogStatistics
    {
        public long TaskId { get; set; }
        public string TaskTitle { get; set; }
        public bool IsCompleted { get; set; }
        
        public bool isDeleted { get; set; }
        public double TotalDuration { get; set; }

        public string status
        {
            get
            {
                return IsCompleted ? "已完成" : "未完成";
            }
            
        }

    }
}
