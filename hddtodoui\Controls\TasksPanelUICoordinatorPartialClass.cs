using System.Linq;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Controls;

public partial class TasksPanel
{
    public void RemoveUncompletedTask(long taskId)
        {
            Tasks.Select(t => t).Where(t => t.TaskID == taskId).ToList().ForEach(t => Tasks.Remove(t));

            if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
            {
                UpdateGroupedView();
            }
        }

        public void RemoveCompletedTask(long taskId)
        {
            if (cTasks == null) return;
            cTasks.Select(t => t).Where(t => t.TaskID == taskId).ToList().ForEach(t => cTasks.Remove(t));
            RefreshCompletedTasksView();
        }

        public void AddTaskToUncompletedTaskListLast(TTask ttask)
        {
            //如果不是本列表则退出
            if (!CurrentStatusHolder.getCurrentStatusHolder().isTaskInCurrentDisplayList(ttask))
                return;

            //是本列表就弄到最后
            var list = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(ttask.BelongToListKey, UserInfoHolder.getUserId());
            var vo = TodoTaskViewObject.GetFrom(ttask, list);

            // 添加到所有任务列表
            if (_allTasks != null)
            {
                _allTasks.Add(vo);
            }

            // 检查颜色筛选条件
            bool shouldAdd = string.IsNullOrEmpty(_selectedColor) ||
                             (!string.IsNullOrEmpty(vo.Color) && vo.Color.StartsWith(_selectedColor + ":"));

            // 如果符合筛选条件，添加到显示列表
            if (shouldAdd)
            {
                Tasks.Insert(Tasks.Count, vo);
            }

            //如果是特殊列表还要分组
            if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
                UpdateGroupedView();
        }

        public void AddTaskToCompletedTaskListFirst(TTask ttask)
        {
            if (!CurrentStatusHolder.getCurrentStatusHolder().isTaskInCurrentDisplayList(ttask))
                return;

            var list = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(ttask.BelongToListKey, UserInfoHolder.getUserId());
            var vo = TodoTaskViewObject.GetFrom(ttask, list);
            if (cTasks != null)
            {
                cTasks.Insert(0, vo);

                TaskListView.ScrollIntoView(vo);

                RefreshCompletedTasksView();
            }
        }

        public TaskItemControl GetTaskItemControlByTaskID(long taskId)
        {
            var control = TaskListView.ContainerFromItem(Tasks.FirstOrDefault(t => t.TaskID == taskId)) as ListViewItem;
            return control?.ContentTemplateRoot as TaskItemControl;
        }
        
        public void MoveUncompletedTaskToTop(long taskId)
        {
            var from = Tasks.FirstOrDefault(t => t.TaskID == taskId);

            int targetIndex = Tasks.IndexOf(GetTaskFromCategoryTop(taskId));

            Tasks.Remove(from);
            Tasks.Insert(targetIndex, from);

            if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
            {
                UpdateGroupedView();
            }

            TaskListView.ScrollIntoView(from);
        }

        public void MoveUncompletedTaskToBottom(long taskId)
        {
            var from = Tasks.FirstOrDefault(t => t.TaskID == taskId);


            int targetIndex = Tasks.IndexOf(GetTaskFromCategoryBottom(taskId));

            var debug = Tasks[targetIndex];
            LogService.Instance.Info(debug.Title);
            LogService.Instance.Info(targetIndex.ToString());
            Tasks.Remove(from);
            Tasks.Insert(targetIndex, from);

            if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
            {
                UpdateGroupedView();
            }

            TaskListView.ScrollIntoView(from);
        }

        public void AddCompletedTaskCategoryToCompletedCategoriesView(TaskCategoryViewObject taskCategoryViewObject)
        {
            if (CompletedCategoryExpander.IsExpanded)
            {
                if (!CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
                    if (CurrentStatusHolder.getCurrentStatusHolder().getCurrentTaskList().Key ==
                        taskCategoryViewObject.ParentCategoryKey)
                    {
                        CompletedTaskCategories.Insert(0, taskCategoryViewObject);
                        RefreshCompletedCategoriesView();
                    }
                        
            }
           
        }
}