﻿<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="HddtodoUI.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:views="using:HddtodoUI.Views"
    xmlns:models="using:HddtodoUI.Models"
    xmlns:local="using:HddtodoUI.Controls"
    mc:Ignorable="d"
    
    >

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="48"/> <!-- Title Bar -->
            <RowDefinition Height="*"/> <!-- Main Content -->
        </Grid.RowDefinitions>

        <!-- Custom Title Bar -->
        <Grid x:ConnectionId='2' x:Name="AppTitleBar" Height="48" VerticalAlignment="Top" Background="{ThemeResource TitleBarBackgroundBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="120"/>
                <ColumnDefinition Width="50"/>
                <ColumnDefinition Width="*"/> <!-- System buttons width -->
            </Grid.ColumnDefinitions>

            <Image Source="/Assets/HddTodoIcon.png" 
                   Width="16" Height="16" 
                   Margin="8,0,0,0"/>
            
            <TextBlock Text="HddTodo" 
                     Grid.Column="1"
                     Margin="8,0,0,0"
                     VerticalAlignment="Center"
                     HorizontalAlignment="Left"
                     Style="{StaticResource BodyTextStyle}"/>

            <!-- List Icon Button -->
            <Button x:ConnectionId='4' Grid.Column="2"
                    Width="40" Height="40"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,0"
                    Background="Transparent"
                    ToolTipService.ToolTip="任务列表"
                                            >
                <SymbolIcon Symbol="List"/>
            </Button>

            <!-- 测试通知按钮 -->
            <!-- <Button x:Name="TestNotificationButton"  -->
            <!--         Grid.Column="1" -->
            <!--         Content="测试通知级别"  -->
            <!--         VerticalAlignment="Center" -->
            <!--         HorizontalAlignment="Left" -->
            <!--         Margin="20,0,0,0" -->
            <!--         Click="TestNotificationButton_Click"/> -->

            <!-- Search Box -->
            <!-- <AutoSuggestBox x:Name="SearchBox" Grid.Column="1"  -->
            <!--               PlaceholderText="搜索任务"  -->
            <!--               Width="280"  -->
            <!--               Height="32" -->
            <!--               VerticalAlignment="Center" -->
            <!--               HorizontalAlignment="Center" -->
            <!--               Style="{StaticResource SearchBoxStyle}" -->
            <!--               TextChanged="SearchBox_TextChanged" -->
            <!--               SuggestionChosen="SearchBox_SuggestionChosen" -->
            <!--               QuerySubmitted="SearchBox_QuerySubmitted"> -->
            <!--     <AutoSuggestBox.ItemTemplate> -->
            <!--         <DataTemplate x:DataType="models:TodoTaskViewObject"> -->
            <!--             <StackPanel Orientation="Vertical" Spacing="4"> -->
            <!--                 <TextBlock Text="{x:Bind Title}" Style="{StaticResource BodyTextStyle}"/> -->
            <!--                 <TextBlock Text="{x:Bind Category}"  -->
            <!--                          Style="{StaticResource CaptionTextStyle}" -->
            <!--                          Opacity="0.6"/> -->
            <!--             </StackPanel> -->
            <!--         </DataTemplate> -->
            <!--     </AutoSuggestBox.ItemTemplate> -->
            <!-- </AutoSuggestBox> -->
            
       
        </Grid>

        <!-- Main Content -->
        <views:MainView x:ConnectionId='3' x:Name="MainViewControl" Grid.Row="1"/>
    </Grid>
</Window>

