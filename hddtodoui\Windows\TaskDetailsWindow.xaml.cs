using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Windows.Graphics;
using HddtodoUI.Controls;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml.Controls;
using WinRT.Interop;
using Window = Microsoft.UI.Xaml.Window;

namespace HddtodoUI.Windows
{
    public sealed partial class TaskDetailsWindow : Window
    {
        
        public TaskDetailsWindow()
        {
            this.InitializeComponent();

            // Get the window handle
            var _appWindow = WindowsUtils.GetAppWindow(this);

            // Get the presenter
            var _presenter = _appWindow.Presenter as OverlappedPresenter;
            if (_presenter != null)
            {
                _presenter.IsAlwaysOnTop = false;
                _presenter.IsResizable = true;
                _presenter.IsMaximizable = true;
                _presenter.IsMinimizable = true;
            }

            // Set window size
            WindowsUtils.ResizeWindow(this, 1280, 1000);
            WindowsUtils.CenterWindow(this);
           
            this.AppWindow.SetIcon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets/HddTodoIcon.ico"));

            // Setup events
            TaskDetailsControl.BackRequested += TaskDetailsControl_BackRequested;

            _appWindow.Closing += AppWindowOnClosing;
            
        }

        private void AppWindowOnClosing(AppWindow sender, AppWindowClosingEventArgs args)
        {
            GetTaskDetailsControl().AskSaveOrDismissBeforeClose();
            //await AskForSwitchOrClose();

            // 无未保存修改：取消默认关闭，转为隐藏窗口并交由 UICoordinator 处理 TaskStepWindow 恢复
            // args.Cancel = false;
            // TheUICoordinator.Instance.HideTaskDetailsWindow();
        }
        
     


        public void AskForSaveBeforeSwitchOrClose()
        {
              GetTaskDetailsControl().AskSaveOrDismissBeforeClose();
            
        }
        
        public AppWindow FromParent { set; get; } 
        
        private void TaskDetailsControl_BackRequested(object sender, EventArgs e)
        {
            TheUICoordinator.Instance.DetailWindowShowFromParentWindow();
        }

        public void ShowAndActivate(TodoTaskViewObject taskViewObject = null)
        {
            if (taskViewObject != null)
            {
                TaskDetailsControl.CheckModifiedAndLoadTask(taskViewObject);
            }


            this.Activate();
            // Bring window to foreground
            //SetForegroundWindow(_windowHandle);
            this.AppWindow.MoveInZOrderAtTop();
        }

        public TaskDetailsControl GetTaskDetailsControl()
        {
            return TaskDetailsControl;
        }
    }
}