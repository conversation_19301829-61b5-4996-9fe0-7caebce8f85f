using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.DTOs;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class TaskActivityHttpStore : HttpStoreBase, ITaskActivityStore
    {
        public TaskActivityHttpStore(HttpClient httpClient = null) : base(httpClient)
        {
        }

        public Task SendHeartbeatAsync(string userId, HeartbeatRequest request)
        {
            var endpoint = $"{baseUrl}/tasks/active-heartbeat";
            var headers = new Dictionary<string, string> { { "X-User-ID", userId } };
            return SendPostRequestAsync(endpoint, "Send Heartbeat", request, headers);
        }

        public Task<IEnumerable<ActiveTask>> GetActiveTasksAsync(string userId, string excludeClientId)
        {
            var endpoint = $"{baseUrl}/users/me/active-tasks?excludeClientId={excludeClientId}";
            var headers = new Dictionary<string, string> { { "X-User-ID", userId } };
            return SendGetRequestAsync<IEnumerable<ActiveTask>>(endpoint, "Get Active Tasks", headers);
        }
    }
}

