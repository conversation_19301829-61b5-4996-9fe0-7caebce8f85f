using System;
using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期使用 JsonPropertyName(全大写) 或 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，包括大小写/拼写。
// 3. 日期统一使用 yyyy-MM-dd'T'HH:mm:ss 格式；使用统一的JsonConverter。
// 4. 若需特殊序列化逻辑，仅在此文件或 JsonConverters 中写一次。
// 5. 其它文件只引用现有 Converter/注解，禁止重复实现。

namespace HddtodoUI.BackendModels
{
    public class TaskTimeLog
    {
        [JsonPropertyName("id")]
        public long Id { get; set; }
        
        [JsonPropertyName("userId")]
        public long UserId { get; set; }
        
        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }
        
        [JsonPropertyName("date")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime Date { get; set; }

        [JsonPropertyName("startTime")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime StartTime { get; set; }

        [JsonPropertyName("endTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? EndTime { get; set; }
        
        [JsonPropertyName("isCompleted")]
        public bool IsCompleted { get; set; }

        public TimeSpan Duration => IsCompleted ? (EndTime.Value - StartTime) : TimeSpan.Zero;

        public TaskTimeLog(long taskId, long userId)
        {
            UserId = userId;
            TaskId = taskId;
            Date = DateTime.Today;
            StartTime = DateTime.Now;
            IsCompleted = false;
        }

        public void End()
        {
            EndTime = DateTime.Now;
            IsCompleted = true;
        }
    }
}