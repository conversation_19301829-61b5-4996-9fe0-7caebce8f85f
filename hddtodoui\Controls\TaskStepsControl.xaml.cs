using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Windows.ApplicationModel.DataTransfer;
using Windows.System;
using CommunityToolkit.WinUI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Dispatching;
using DispatcherQueuePriority = Microsoft.UI.Dispatching.DispatcherQueuePriority; // Added for DispatcherQueue

namespace HddtodoUI.Controls
{
    public sealed partial class TaskStepsControl : UserControl
    {
        private TodoTaskViewObject _parentTaskViewObject;
        private bool _isProgrammaticChange = false; // Flag to indicate programmatic changes
        private bool _isRefreshingSteps = false; // Flag to prevent re-entrancy in RefreshTaskSteps

        public ObservableCollection<TaskStep> TaskSteps { get; private set; } = 
            new ObservableCollection<TaskStep>();

        private ITaskStepStore _taskStepStore;

        public TaskStepsControl()
        {
            this.InitializeComponent();
            TaskStepsListView.ItemsSource = TaskSteps;
            _taskStepStore = StoreFactoryHolder.getTaskStepStore();
        }

        /// <summary>
        /// 设置父任务，用于添加任务步骤
        /// </summary>
        /// <param name="taskViewObject">父任务视图对象</param>
        public void SetParentTask(TodoTaskViewObject taskViewObject)
        {
            _parentTaskViewObject = taskViewObject;
            RefreshTaskSteps();
        }

        /// <summary>
        /// 刷新任务步骤列表
        /// </summary>
        public void RefreshTaskSteps()
        {
            if (_isRefreshingSteps) return; // Prevent re-entrancy
            if (_parentTaskViewObject == null) return;

            _isRefreshingSteps = true;
            _isProgrammaticChange = true; // Set flag before changing collection
            try
            {
                var steps = _taskStepStore.GetTaskSteps(_parentTaskViewObject.TaskID, UserInfoHolder.getUserId());

                TaskSteps.Clear();
                foreach (var step in steps.Where(s => !s.DeletedStatus))
                {
                    TaskSteps.Add(step);
                }
            }
            finally
            {
                // Schedule _isProgrammaticChange to be reset later, allowing current UI updates to complete
                this.DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Low, () =>
                {
                    _isProgrammaticChange = false;
                });
                _isRefreshingSteps = false; // This can be reset immediately
            }
        }

        /// <summary>
        /// 设置任务步骤列表的启用状态
        /// </summary>
        /// <param name="isEnabled">是否启用</param>
        public void SetEnabled(bool isEnabled)
        {
            NewTaskStepTextBox.IsEnabled = isEnabled;
            AddTaskStepButton.IsEnabled = isEnabled;
            TaskStepsListView.IsEnabled = isEnabled;
        }

        private void AddTaskStepButton_Click(object sender, RoutedEventArgs e)
        {
            AddTaskStep();
        }

        private void NewTaskStepTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                AddTaskStep();
                e.Handled = true;
            }
        }

        private void AddTaskStep()
        {
            if (_parentTaskViewObject == null) return;
            
            string text = NewTaskStepTextBox.Text.Trim();
            if (string.IsNullOrEmpty(text)) return;
            
            // 创建新的任务步骤
            var taskStep = new TaskStep(
                _parentTaskViewObject.TaskID,
                UserInfoHolder.getUserId(),
                text,null
            );
            
            // 设置步骤顺序
            taskStep.StepOrder = TaskSteps.Count > 0 ? 
                TaskSteps.Max(s => s.StepOrder) + 1 : 0;
            
            // 保存任务步骤
            _taskStepStore.saveTaskStepChange(taskStep, UserInfoHolder.getUserId());
            
            // 刷新列表
            RefreshTaskSteps();
            
            // 清空输入框
            NewTaskStepTextBox.Text = null;
        }

        private void StepCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            UpdateStepCompletionStatus(sender as CheckBox, true);
        }

        private void StepCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateStepCompletionStatus(sender as CheckBox, false);
        }

        private void UpdateStepCompletionStatus(CheckBox checkBox, bool isCompleted)
        {
            if (_isProgrammaticChange) return; // Skip if change is programmatic
            if (checkBox == null) return;
            
            // 获取关联的TaskStep
            var taskStep = checkBox.DataContext as TaskStep;
            if (taskStep == null) return;
            
            // 更新完成状态
            taskStep.StepCompleteTime = isCompleted ? DateTime.Now : null;
            
            // 保存更改
            _taskStepStore.saveTaskStepChange(taskStep, UserInfoHolder.getUserId());
        }

        private void DeleteStepButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is long stepId)
            {
                // 获取要删除的步骤
                var step = TaskSteps.FirstOrDefault(s => s.StepId == stepId);
                if (step != null)
                {
                    // 标记为已删除
                    step.DeletedStatus = true;
                    
                    // 保存更改
                    _taskStepStore.saveTaskStepChange(step, UserInfoHolder.getUserId());
                    
                    // 刷新列表
                    RefreshTaskSteps();
                }
            }
        }

        /// <summary>
        /// 编辑步骤标题
        /// </summary>
        private async void EditStepTitle_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuFlyoutItem menuItem && menuItem.Tag is long stepId)
            {
                // 获取要编辑的步骤
                var step = TaskSteps.FirstOrDefault(s => s.StepId == stepId);
                if (step == null) return;

                // 创建编辑对话框
                var dialog = new ContentDialog
                {
                    Title = "编辑步骤标题",
                    PrimaryButtonText = "保存",
                    CloseButtonText = "取消",
                    DefaultButton = ContentDialogButton.Primary,
                    XamlRoot = this.XamlRoot
                };

                // 创建文本框
                var textBox = new TextBox
                {
                    Text = step.Title,
                    AcceptsReturn = false,
                    TextWrapping = TextWrapping.Wrap,
                    MinWidth = 300,
                    MinHeight = 32
                };
                dialog.Content = textBox;

                // 显示对话框并处理结果
                var result = await dialog.ShowAsync();
                if (result == ContentDialogResult.Primary)
                {
                    string newTitle = textBox.Text.Trim();
                    if (!string.IsNullOrEmpty(newTitle))
                    {
                        // 更新标题
                        step.Title = newTitle;
                        
                        // 保存更改
                        _taskStepStore.saveTaskStepChange(step, UserInfoHolder.getUserId());
                        
                        // 刷新列表
                        RefreshTaskSteps();
                    }
                }
            }
        }

        private void CopyStepTitle_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuFlyoutItem menuItem && menuItem.Tag is long stepId)
            {
                var step = TaskSteps.FirstOrDefault(s => s.StepId == stepId);
                if (step != null)
                {
                    ClipboardUtils.CopyToClipboard(step.Title);
                }
            }
        }

        #region 颜色设置方法
        /// <summary>
        /// 设置步骤颜色 - 粉色
        /// </summary>
        private void SetColorPink_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#FFC0CB"); // Pink
        }

        /// <summary>
        /// 设置步骤颜色 - 橙色
        /// </summary>
        private void SetColorOrange_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#FFA500"); // Orange
        }

        /// <summary>
        /// 设置步骤颜色 - 绿色
        /// </summary>
        private void SetColorGreen_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#008000"); // Green
        }

        /// <summary>
        /// 设置步骤颜色 - 蓝色
        /// </summary>
        private void SetColorBlue_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#0000FF"); // Blue
        }

        /// <summary>
        /// 设置步骤颜色 - 紫色
        /// </summary>
        private void SetColorPurple_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#800080"); // Purple
        }

        /// <summary>
        /// 设置步骤颜色 - 棕色
        /// </summary>
        private void SetColorBrown_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#A52A2A"); // Brown
        }

        /// <summary>
        /// 设置步骤颜色 - 金色
        /// </summary>
        private void SetColorGold_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#FFD700"); // Gold
        }

        /// <summary>
        /// 设置步骤颜色 - 黑色
        /// </summary>
        private void SetColorBlack_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#000000"); // Black
        }

        /// <summary>
        /// 设置步骤颜色 - 无颜色
        /// </summary>
        private void SetColorNone_Click(object sender, RoutedEventArgs e)
        {
            SetStepColor(sender, "#FFFFFF"); // White (default)
        }

        /// <summary>
        /// 设置步骤颜色的通用方法
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="colorHex">颜色十六进制值</param>
        private void SetStepColor(object sender, string colorHex)
        {
            if (sender is MenuFlyoutItem menuItem && menuItem.Tag is long stepId)
            {
                // 获取要设置颜色的步骤
                var step = TaskSteps.FirstOrDefault(s => s.StepId == stepId);
                if (step == null) return;

                // 更新颜色
                step.Color = colorHex;
                
                // 保存更改
                _taskStepStore.saveTaskStepChange(step, UserInfoHolder.getUserId());
                
                // 刷新列表
                RefreshTaskSteps();
            }
        }
        #endregion
        
        // 存储拖拽开始前的步骤顺序，用于比较变化
        private List<TaskStep> _originalStepOrder;
        
        /// <summary>
        /// 拖拽开始时记录原始顺序
        /// </summary>
        private void TaskStepsListView_DragItemsStarting(object sender, DragItemsStartingEventArgs e)
        {
            // 保存拖拽开始前的步骤顺序
            _originalStepOrder = TaskSteps.ToList();
        }

        /// <summary>
        /// 拖拽完成后重新排序任务步骤
        /// </summary>
        private async void TaskStepsListView_DragItemsCompleted(object sender, DragItemsCompletedEventArgs args)
        {
            if (args.DropResult == DataPackageOperation.Move && _parentTaskViewObject != null)
            {
                // 准备重新定位的步骤列表
                var itemsToReposition = new List<RepositionStepItem>();
                
                // 获取当前顺序中的所有步骤
                for (int i = 0; i < TaskSteps.Count; i++)
                {
                    itemsToReposition.Add(new RepositionStepItem
                    {
                        StepId = TaskSteps[i].StepId,
                        Position = i + 1 // 位置从1开始计数
                    });
                }
                
                // 调用API重新定位步骤
                try
                {
                    var response = await _taskStepStore.RepositionStepsAsync(
                        _parentTaskViewObject.TaskID,
                        UserInfoHolder.getUserId(),
                        itemsToReposition);
                    
                    if (!response.Success)
                    {
                        // 如果API调用失败，恢复原始顺序
                        TaskSteps.Clear();
                        foreach (var step in _originalStepOrder)
                        {
                            TaskSteps.Add(step);
                        }
                        
                        // 显示错误消息
                        var dialog = new ContentDialog
                        {
                            Title = "排序失败",
                            Content = response.Message ?? "重新排序步骤时出现错误",
                            CloseButtonText = "确定"
                        };
                        
                        // 获取当前窗口的XamlRoot以显示对话框
                        if (this.XamlRoot != null)
                        {
                            dialog.XamlRoot = this.XamlRoot;
                            await dialog.ShowAsync();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常
                    var dialog = new ContentDialog
                    {
                        Title = "排序失败",
                        Content = $"重新排序步骤时出现错误: {ex.Message}",
                        CloseButtonText = "确定"
                    };
                    
                    // 获取当前窗口的XamlRoot以显示对话框
                    if (this.XamlRoot != null)
                    {
                        dialog.XamlRoot = this.XamlRoot;
                        await dialog.ShowAsync();
                    }
                }
            }
        }
        
        // 注意：不再需要在代码中注册和注销事件，因为已经在XAML中声明了事件处理程序
    }
}
