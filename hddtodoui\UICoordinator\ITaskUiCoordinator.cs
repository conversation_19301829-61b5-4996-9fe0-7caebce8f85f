using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.UICoordinator;

public interface ITaskUiCoordinator
{
    public Task StartOrPauseButtonClick(TomatoTaskManager tomatoTaskManager);
    public void TomatoClockTicked();
    public void TomatoClockStatusChanged();
    public void OnTaskAdded(TTask task, TaskCategory belongTaskList);
    public void TaskSwitchComplete(TTask task);
    
    public void TaskSwitchUnComplete(TTask task);

    public void OnTaskModified(TTask task, TodoTaskViewObject previousTaskViewObject);

    public void OnTaskDragMoveToCategory(TodoTaskViewObject taskViewObject, TaskCategory targetList);

    public void OnTaskRemoved(TTask task);
    public  void OnTaskUnRemoved(TTask task);

    public  void MoveTaskToCategoryTop(long taskId);

    public  void MoveTaskToCategoryBottom(long taskId);

}