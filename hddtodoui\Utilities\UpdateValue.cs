namespace HddtodoUI.Utilities;

public abstract class UpdateValue<T>
{
    // 表示不修改
    public static UpdateValue<T> NoChange => new NoChangeValue<T>();
    // 表示设置为某个值（可以是 null）
    public static UpdateValue<T> SetTo(T value) => new SetValue<T>(value);
}

// 表示“不修改”的子类
public class NoChangeValue<T> : UpdateValue<T> { }

// 表示“设置为某个值”的子类
public class SetValue<T> : UpdateValue<T>
{
    public T Value { get; }
    public SetValue(T value) => Value = value;
}