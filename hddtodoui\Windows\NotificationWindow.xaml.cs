using System;
using System.Diagnostics;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Windows.Foundation;
using Windows.Graphics;
using Windows.UI;
using HddtodoUI.Services;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Windows
{
    /// <summary>
    /// 通知级别枚举
    /// </summary>
    public enum NotificationLevel
    {
        Info,
        Warning,
        Danger,
        Success
    }

    public sealed partial class NotificationWindow : Window
    {
        private  DispatcherQueue _dispatcherQueue;
        private  DispatcherTimer _animationTimer;
        private DispatcherQueueTimer _closeTimer;
        private  int _duration;
        private readonly int _animationDuration = 150; // 动画持续时间（毫秒）
        private readonly int _windowWidth = 300;
        private int _windowHeight = 150;
        private bool _isShowing = true;
        private bool _isAnimating = false;
        private double _currentX = 0;
        private AppWindow _appWindow;
        private DisplayArea _displayArea;
        private UIElement _customContent;
        private NotificationLevel _notificationLevel = NotificationLevel.Info;

        /// <summary>
        /// 获取或设置通知级别
        /// </summary>
        public NotificationLevel Level
        {
            get => _notificationLevel;
            set
            {
                _notificationLevel = value;
                UpdateNotificationStyle();
            }
        }

        public int Height => _windowHeight;

        /// <summary>
        /// 构造函数 - 文本内容
        /// </summary>
        public NotificationWindow(string content, string title = null, int duration = 5000)
        {
            InitializeComponent();
            
            // 设置标题和内容
            SetContent(content);
            SetTitle(title);
            
            // 初始化窗口
            InitializeWindow(duration);
        }

        /// <summary>
        /// 构造函数 - 自定义内容
        /// </summary>
        public NotificationWindow(UIElement customContent, string title = null, int duration = 5000)
        {
            InitializeComponent();
            
            // 设置标题和自定义内容
            SetCustomContent(customContent);
            SetTitle(title);
            
            // 初始化窗口
            InitializeWindow(duration);
        }

        /// <summary>
        /// 构造函数 - 文本内容和级别
        /// </summary>
        public NotificationWindow(string content, NotificationLevel level, string title = null, int duration = 5000)
        {
            InitializeComponent();
            
            // 设置内容和标题
            SetContent(content);
            SetTitle(title);
            
            // 设置通知级别
            _notificationLevel = level;
            
            // 初始化窗口
            InitializeWindow(duration);
            
            // 更新通知样式
            UpdateNotificationStyle();
        }

        /// <summary>
        /// 构造函数 - 自定义内容和级别
        /// </summary>
        public NotificationWindow(UIElement customContent, NotificationLevel level, string title = null, int duration = 5000)
        {
            InitializeComponent();
            
            // 设置自定义内容和标题
            SetCustomContent(customContent);
            SetTitle(title);
            
            // 设置通知级别
            _notificationLevel = level;
            
            // 初始化窗口
            InitializeWindow(duration);
            
            // 更新通知样式
            UpdateNotificationStyle();
        }

        /// <summary>
        /// 设置文本内容
        /// </summary>
        private void SetContent(string content)
        {
            ContentTextBlock.Text = content;
            ContentTextBlock.Visibility = Visibility.Visible;
            CustomContentPresenter.Visibility = Visibility.Collapsed;
            _customContent = null;
        }

        /// <summary>
        /// 设置自定义内容
        /// </summary>
        private void SetCustomContent(UIElement content)
        {
            _customContent = content;
            CustomContentPresenter.Content = content;
            ContentTextBlock.Visibility = Visibility.Collapsed;
            CustomContentPresenter.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 设置标题
        /// </summary>
        private void SetTitle(string title)
        {
            if (!string.IsNullOrEmpty(title))
            {
                TitleTextBlock.Text = title;
                TitleTextBlock.Visibility = Visibility.Visible;
            }
            else
            {
                TitleTextBlock.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 更新通知样式
        /// </summary>
        private void UpdateNotificationStyle()
        {
            SolidColorBrush levelBrush;
            
            switch (_notificationLevel)
            {
                case NotificationLevel.Info:
                    levelBrush = new SolidColorBrush(Color.FromArgb(255, 0, 120, 215)); // 蓝色
                    break;
                case NotificationLevel.Warning:
                    levelBrush = new SolidColorBrush(Color.FromArgb(255, 255, 185, 0)); // 黄色
                    break;
                case NotificationLevel.Danger:
                    levelBrush = new SolidColorBrush(Color.FromArgb(255, 232, 17, 35)); // 红色
                    break;
                case NotificationLevel.Success:
                    levelBrush = new SolidColorBrush(Color.FromArgb(255, 0, 255, 0)); // 绿色
                    break;
                default:
                    levelBrush = new SolidColorBrush(Color.FromArgb(255, 0, 120, 215)); // 默认蓝色
                    break;
            }
            
            // 设置级别指示器颜色
            LevelIndicator.Fill = levelBrush;
            
            // 根据级别设置图标
            switch (_notificationLevel)
            {
                case NotificationLevel.Info:
                    LevelIcon.Glyph = "\uE946"; // 信息图标
                    break;
                case NotificationLevel.Warning:
                    LevelIcon.Glyph = "\uE7BA"; // 警告图标
                    break;
                case NotificationLevel.Danger:
                    LevelIcon.Glyph = "\uE783"; // 危险图标
                    break;
                case NotificationLevel.Success:
                    LevelIcon.Glyph = "\uE8A5"; // 成功图标
                    break;
            }
        }

        private void InitializeWindow(int duration)
        {
            WindowsUtils.SetAlwayOnTop(this);
            // 初始化
            _dispatcherQueue = DispatcherQueue.GetForCurrentThread();
            _duration = duration;
            
            // 初始化动画计时器
            _animationTimer = new DispatcherTimer();
            _animationTimer.Interval = TimeSpan.FromMilliseconds(16); // 约60fps
            _animationTimer.Tick += AnimationTimer_Tick;
            
            // 设置窗口样式
            WindowsUtils.NoTitleBar(this);
            WindowsUtils.NoResizeWindow(this);
            
            // 获取显示区域
            _displayArea = DisplayArea.Primary;
            

            WindowsUtils.ResizeWindow(this, _windowWidth, _windowHeight);
            
            // 设置窗口位置
            InitializeWindowPosition();
            
            // 计算窗口大小
            CalculateWindowSize();
            
            // 获取AppWindow实例
            _appWindow = WindowsUtils.GetAppWindow(this);
            
            // 窗口大小变化事件
            this.SizeChanged += NotificationWindow_SizeChanged;
            
            this.Activated += OnActivated;
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            if (_closeTimer != null)
            {
                _closeTimer.Stop();
            }
            Hide();
        }

        private void OnActivated(object sender, WindowActivatedEventArgs args)
        {
            CalculateWindowSize();
            NotificationService.Instance.NotifyHeightChanged(this);
        }

        private void NotificationWindow_SizeChanged(object sender, WindowSizeChangedEventArgs args)
        {
            CalculateWindowSize();
            NotificationService.Instance.NotifyHeightChanged(this);
        }
        
        private void CalculateWindowSize()
        {
            try
            {
               
                var gridHeight = MeasureCustomContentHeight(RootGrid);
                
                // 设置窗口高度（加上边距和其他UI元素的高度，增加额外缓冲）
                _windowHeight = (int)(  gridHeight +   20); // 100为其他边距和缓冲
                
                // 调整窗口大小 - 使用安全的方法调整大小
                SafeResizeWindow(_windowWidth, _windowHeight);
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"计算窗口大小时出错: {ex.Message}");
            }
        }
        
        private void SafeResizeWindow(int width, int height)
        {
            try
            {
                if (_appWindow != null)
                {
                    _appWindow.Resize(new SizeInt32(width, height));
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"调整窗口大小时出错: {ex.Message}");
                // 窗口可能已关闭，忽略错误
            }
        }

       

        private double MeasureCustomContentHeight(UIElement content)
        {
            // 测量自定义内容高度
            content.Measure(new Size(_windowWidth - 30, double.PositiveInfinity));
            return content.DesiredSize.Height;
        }

        private void InitializeWindowPosition()
        {
            if (_displayArea != null)
            {
                // 初始位置在屏幕外右侧
                int screenWidth = _displayArea.WorkArea.Width;
                int screenHeight = _displayArea.WorkArea.Height;
                
                // 获取Y坐标（从NotificationService）
                int yPosition = screenHeight - _windowHeight - NotificationService.Instance.GetNotificationYPosition(this) - 20;
                
                // 初始X坐标（在屏幕外）
                _currentX = screenWidth;
                
                // 移动窗口到初始位置
                SafeMoveWindow((int)_currentX,yPosition);
                //_appWindow.Move(new PointInt32((int)_currentX, yPosition));
            }
        }

        private void StartShowAnimation()
        {
            if (_displayArea == null)
                return;
            
            // 设置初始位置（屏幕右侧外）
            _currentX = _displayArea.WorkArea.Width;
            
            // 移动窗口到初始位置
            SafeMoveWindow((int)_currentX, _displayArea.WorkArea.Height - _windowHeight - NotificationService.Instance.GetNotificationYPosition(this) - 20);
            
            // 开始显示动画
            _isShowing = true;
            _isAnimating = true;
            _animationTimer.Start();
        }

        private void StartHideAnimation()
        {
            _isShowing = false;
            _isAnimating = true;
            _animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, object e)
        {
            try
            {
                if (_displayArea == null)
                    return;
                
                int screenWidth = _displayArea.WorkArea.Width;
                int screenMargin = NotificationService.Instance.GetScreenMargin();
                
                // 目标X位置
                double targetX = _isShowing ? 
                    screenWidth - _windowWidth - screenMargin : // 显示位置
                    screenWidth; // 隐藏位置
                
                // 动画速度
                double animationSpeed = _windowWidth / (_animationDuration / 16.0); // 16ms一帧
                
                // 更新当前X位置
                if (_isShowing)
                {
                    _currentX = Math.Max(_currentX - animationSpeed, targetX);
                }
                else
                {
                    _currentX = Math.Min(_currentX + animationSpeed, targetX);
                }
                
                // 获取当前Y位置
                int yPosition = _displayArea.WorkArea.Height - _windowHeight - NotificationService.Instance.GetNotificationYPosition(this) - 20;
                
                // 移动窗口 - 使用安全的方法
                SafeMoveWindow((int)_currentX, yPosition);
                
                // 检查动画是否完成
                if ((_isShowing && _currentX <= targetX) || (!_isShowing && _currentX >= targetX))
                {
                    _animationTimer.Stop();
                    _isAnimating = false;
                    
                    // 如果是隐藏动画完成，关闭窗口
                    if (!_isShowing)
                    {
                        Close();
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"动画计时器出错: {ex.Message}");
                _animationTimer.Stop();
                _isAnimating = false;
            }
        }

        private void SafeMoveWindow(int x, int y)
        {
            try
            {
                if (_appWindow != null)
                {
                    _appWindow.Move(new PointInt32(x, y));
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"移动窗口时出错: {ex.Message}");
                // 窗口可能已关闭，忽略错误
            }
        }

        /// <summary>
        /// 隐藏通知
        /// </summary>
        public void Hide()
        {
            if (_isAnimating)
                return;
            
            // 开始隐藏动画
            StartHideAnimation();
        }

        public void UpdatePosition()
        {
            try
            {
                if (_displayArea == null)
                    return;
                
                // 获取当前应该在的Y位置
                int yPosition = _displayArea.WorkArea.Height - _windowHeight - NotificationService.Instance.GetNotificationYPosition(this) - 20;
                
                // 获取当前X位置（保持不变）
                int xPosition = (int)_currentX;
                
                // 移动窗口 - 使用安全的方法
                SafeMoveWindow(xPosition, yPosition);
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"更新窗口位置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示通知
        /// </summary>
        public void Show()
        {
            try
            {
                // 显示窗口
                if (_appWindow != null)
                {
                    _appWindow.Show();
                }
                
                // 启动显示动画
                StartShowAnimation();
                
                // 启动自动关闭计时器
                if (_duration > 0)
                {
                    _dispatcherQueue.TryEnqueue(() =>
                    {
                        _closeTimer = _dispatcherQueue.CreateTimer();
                        _closeTimer.Interval = TimeSpan.FromMilliseconds(_duration);
                        _closeTimer.Tick += CloseTimer_Tick;
                        _closeTimer.Start();
                    });
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"显示通知窗口时出错: {ex.Message}");
            }
        }

        private void CloseTimer_Tick(object sender, object e)
        {
            _closeTimer.Stop();
            Hide();
        }
    }
}
