using System.Collections.Generic;
using Windows.Foundation;
using HddtodoUI.Models;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;


namespace HddtodoUI.Controls;

public partial class TaskCategoriesTreeViewPanel
{
    // 添加一个扩展方法来获取所有子节点
    private static IEnumerable<TreeViewNode> GetAllDescendants(TreeViewNode rootNode)
    {
        foreach (var childNode in rootNode.Children)
        {
            yield return childNode;
               
            foreach (var descendent in GetAllDescendants(childNode))
            {
                yield return descendent;
            }
        }
    }
    
     private TreeViewNode FindNodeAtPosition(TreeView treeView, TreeViewNode node, Point position)
        {
            // 获取当前节点的容器（TreeViewItem）
            if (treeView.ContainerFromNode(node) is TreeViewItem treeViewItem)
            {
                // 获取 TreeViewItem 在 TreeView 中的位置和大小
                GeneralTransform transform = treeViewItem.TransformToVisual(treeView);
                Rect bounds = transform.TransformBounds(new Rect(0, 0, treeViewItem.ActualWidth, treeViewItem.ActualHeight));

                // 检查鼠标位置是否在该 TreeViewItem 的边界内
                if (position.X >= bounds.X && position.X <= bounds.X + bounds.Width &&
                    position.Y >= bounds.Y && position.Y <= bounds.Y + bounds.Height)
                {
                    return node;
                }
            }

            // 递归检查子节点
            foreach (var childNode in node.Children)
            {
                TreeViewNode result = FindNodeAtPosition(treeView, childNode, position);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }
        
        private TreeViewNode FindNodeByKey(TreeView treeView, string categoryKey)
        {
            if (string.IsNullOrEmpty(categoryKey))
            {
                return null;
            }

            if (treeView == null) return null;

            foreach (var rootNode in treeView.RootNodes)
            {
                var foundNode = FindNodeRecursively(rootNode, categoryKey);
                if (foundNode != null)
                {
                    return foundNode;
                }
            }
            return null;
        }

        private TreeViewNode FindNodeRecursively(TreeViewNode currentNode, string categoryKey)
        {
            if (currentNode.Content is TaskCategoryViewObject category && category.Key == categoryKey)
            {
                return currentNode;
            }

            foreach (var childNode in currentNode.Children)
            {
                var foundInChild = FindNodeRecursively(childNode, categoryKey);
                if (foundInChild != null)
                {
                    return foundInChild;
                }
            }
            return null;
        }
}