using System;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.BackendModels.TaskFactory
{
    public static class TTaskFactory
    {
        public static TTask CreateTaskInTaskList(string title, TaskCategory currentTaskList)
        {
            TTask thisTask = null;

            thisTask = StoreFactoryHolder.getTaskStore().createTask(title, getTaskPriority(title),
                getTaskDueTime(title), UserInfoHolder.getUserId(), currentTaskList);

            return thisTask;
        }
        
        public static TTask CreateTaskInTaskList(string title, DateTime? dueTime,  TaskCategory currentTaskList)
        {
            return CreateTaskInTaskList(title, dueTime, getTaskPriority(title), currentTaskList);
        }

        public static TTask CreateTaskInTaskList(string title, DateTime? dueTime, TaskPriority priority, TaskCategory currentTaskList)
        {
            
            DateTime? dateTime = null;
            if (dueTime == null)
                dateTime = getTaskDueTime(title);
            else
                dateTime = dueTime;
            
            return  StoreFactoryHolder.getTaskStore().createTask(title, priority, dateTime,
                UserInfoHolder.getUserId(), currentTaskList);
        }
      
        
        private static TaskPriority getTaskPriority(string title)
        {
            if (title.Contains("高优先"))
                return TaskPriority.high;

            if (title.Contains("低优先"))
                return TaskPriority.low;

            return TaskPriority.normal;
        }

        private static DateTime? getTaskDueTime(string title)
        {
            if (title.Contains("今天") || title.Contains("今日"))
                return DateTime.Today;

            if (title.Contains("明天") || title.Contains("明日"))
                return DateTime.Today.AddDays(1);

            return null;
        }
    }
}