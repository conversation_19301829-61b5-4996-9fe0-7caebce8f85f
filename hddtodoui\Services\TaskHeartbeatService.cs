using System;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.DTOs;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;

namespace HddtodoUI.Services
{
    /// <summary>
    /// Service responsible for sending periodic heartbeats to the server for active tasks
    /// to indicate that the client is still active and working on the task.
    /// </summary>
    public static class TaskHeartbeatService
    {
        private static readonly TimeSpan HeartbeatInterval = TimeSpan.FromSeconds(60);
        private static DateTime _lastHeartbeatTime = DateTime.MinValue;
        private static long? _lastTaskId;
        private static bool _isHeartbeatInProgress;

        /// <summary>
        /// Called on each clock tick. Will send a heartbeat if the specified task is active
        /// and enough time has passed since the last heartbeat.
        /// </summary>
        /// <param name="taskId">ID of the currently active task, or null if no task is active</param>
        public static async Task ClockTickedAsync(long? taskId)
        {
            // If no task is active or we're already processing a heartbeat, do nothing
            if (taskId == null || _isHeartbeatInProgress)
                return;

            // Check if enough time has passed since the last heartbeat
            var now = DateTime.UtcNow;
            if ((now - _lastHeartbeatTime) < HeartbeatInterval && _lastTaskId == taskId)
                return;

            // Update tracking variables and send the heartbeat
            _lastHeartbeatTime = now;
            _lastTaskId = taskId;
            _isHeartbeatInProgress = true;

            try
            {
                await SendHeartbeatAsync(taskId.Value);
            }
            catch (Exception ex)
            {
                // Log the error but don't let it bubble up as it's not critical
                Console.WriteLine($"Error sending heartbeat for task {taskId}: {ex.Message}");
            }
            finally
            {
                _isHeartbeatInProgress = false;
            }
        }

        private static async Task SendHeartbeatAsync(long taskId)
        {
            var userId = UserInfoHolder.getUserId();
            var store = StoreFactoryHolder.getTaskActivityStore();
            
            await store.SendHeartbeatAsync(userId.ToString(), new HeartbeatRequest
            {
                TaskId = taskId.ToString(),
                ClientId = ClientInfoProvider.ClientId,
                ClientType = ClientInfoProvider.ClientType
            });
        }
    }
}
