﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.TaskStepsControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:models="using:HddtodoUI.BackendModels"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="300"
    d:DesignWidth="400">

    <UserControl.Resources>
        <converters:StringToBrushConverter x:Key="StringToBrushConverter"/>
        
        <Style x:Key="CustomTaskStepTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>
        
        <Style x:Key="TaskStepItemStyle" TargetType="ListViewItem">
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            <Setter Property="Padding" Value="0" />
            <Setter Property="MinHeight" Value="0" />
            <Setter Property="Margin" Value="0,0" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Task Steps Header -->
        <TextBlock Grid.Row="0"
                   Text="任务步骤"
                   FontWeight="SemiBold"
                   Margin="0,0,0,8" />

        <!-- Task Steps ListView with Drag and Drop -->
        <ListView x:ConnectionId='2' Grid.Row="1" 
                  x:Name="TaskStepsListView"
                  Background="Transparent"
                  CanDragItems="True"
                  CanReorderItems="True"
                  AllowDrop="True"
                  SelectionMode="None" x:FieldModifier="public"
                                                                         
                                                                           >
            <ListView.Header>
                <Grid Padding="0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBox x:ConnectionId='3' Grid.Column="0"
                             x:Name="NewTaskStepTextBox"
                             PlaceholderText="添加任务步骤..."
                                                                  
                             Style="{StaticResource CustomTaskStepTextBoxStyle}"/>

                    <Button x:ConnectionId='4' Grid.Column="1"
                            x:Name="AddTaskStepButton"
                            Content="添加步骤"
                            Style="{StaticResource SecondaryButtonStyle}"
                            Margin="8,0,0,0"
                                                            
                            Visibility="Collapsed"/>
                </Grid>
            </ListView.Header>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem" BasedOn="{StaticResource TaskStepItemStyle}"/>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate                             >
                    <Grid x:ConnectionId='6' Padding="5,8,0,8" Background="Transparent">
                        <Grid.ContextFlyout>
                            <MenuFlyout>
                                <MenuFlyoutItem x:ConnectionId='7' Text="编辑标题" Icon="Edit"                                                   />
                                <MenuFlyoutItem x:ConnectionId='8' Text="复制标题" Icon="Copy"                                                   />
                                <MenuFlyoutSubItem Text="设置颜色">
                                    <MenuFlyoutSubItem.Icon>
                                        <FontIcon Glyph="&#xE790;" />
                                    </MenuFlyoutSubItem.Icon>
                                    <MenuFlyoutItem x:ConnectionId='9' Text="粉色"                                                 >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Pink" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='10' Text="橙色"                                                   >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Orange" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='11' Text="绿色"                                                  >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Green" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='12' Text="蓝色"                                                 >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Blue" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='13' Text="紫色"                                                   >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Purple" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='14' Text="棕色"                                                  >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Brown" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='15' Text="金色"                                                 >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Gold" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='16' Text="黑色"                                                  >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xEA3A;" Foreground="Black" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                    <MenuFlyoutItem x:ConnectionId='17' Text="无颜色"                                                 >
                                        <MenuFlyoutItem.Icon>
                                            <FontIcon Glyph="&#xE711;" />
                                        </MenuFlyoutItem.Icon>
                                    </MenuFlyoutItem>
                                </MenuFlyoutSubItem>
                            </MenuFlyout>
                        </Grid.ContextFlyout>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="25" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="30" />
                            <ColumnDefinition Width="10" />
                        </Grid.ColumnDefinitions>
                        
                      
                        
                        <!-- 完成状态复选框 -->
                        <CheckBox x:ConnectionId='18' Grid.Column="0" 
                                                                                                                                                   
                                                                
                                                                    
                                  VerticalAlignment="Center" />
                        
                        <!-- 步骤标题 -->
                        <TextBlock x:ConnectionId='19' Grid.Column="1" 
                                                                      
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Left"
                                   Margin="8,0,0,0"
                                   TextWrapping="Wrap"
                                                                                                                                                                 />
                        
                        <!-- 删除按钮 -->
                        <Button x:ConnectionId='20' Grid.Column="2"
                                Content="&#xE74D;"
                                FontFamily="Segoe MDL2 Assets"
                                Background="Transparent"
                                BorderThickness="0"
                                Padding="8,4"
                                                              
                                                     
                                VerticalAlignment="Center" />
                        <!-- 颜色指示器 -->
                        <Border x:ConnectionId='21' Grid.Column="3" Width="4"                                                                                             />
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </Grid>
</UserControl>

