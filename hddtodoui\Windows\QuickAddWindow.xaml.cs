using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using System;
using Windows.Graphics;
using WinRT.Interop;
using HddtodoUI.Models;
using Microsoft.UI.Xaml.Controls;
using System.Runtime.InteropServices;
using HddtodoUI.Utilities;

namespace HddtodoUI.Windows
{
    public sealed partial class QuickAddWindow : Window
    {
        private AppWindow _appWindow;
        private OverlappedPresenter _presenter;
        private IntPtr _windowHandle;
        public event EventHandler<TodoTaskViewObject> TaskSaved;

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        public bool IsStartTask { get; set; }
        
        public QuickAddWindow()
        {
            this.InitializeComponent();

            // Get the window handle
            WindowsUtils.SetAlwayOnTop(this);
            WindowsUtils.ResizeWindow(this,750,220);
            WindowsUtils.CenterWindow(this);
            WindowsUtils.NoTitleBar(this);
            WindowsUtils.NoResizeWindow(this);
            
            // Setup events
            QuickAddTaskControl.TaskSaved += QuickAddTaskControl_TaskSaved;
            QuickAddTaskControl.DialogCancelled += QuickAddTaskControl_DialogCancelled;
            this.Activated += QuickAddWindow_Activated;
            
            DragDropMoveWindowHelper dragDropMoveWindowHelper = new DragDropMoveWindowHelper(this);
            
            RootGrid.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            RootGrid.PointerMoved  += dragDropMoveWindowHelper.Border_PointerMoved;
            RootGrid.PointerReleased +=  dragDropMoveWindowHelper.Border_PointerReleased;
        }

        private void QuickAddWindow_Activated(object sender, WindowActivatedEventArgs args)
        {
            if (args.WindowActivationState != WindowActivationState.Deactivated)
            {
                // 强制设置键盘焦点到文本框
                DispatcherQueue.TryEnqueue(Microsoft.UI.Dispatching.DispatcherQueuePriority.Normal, () =>
                {
                    var textBox = QuickAddTaskControl.FindName("TitleTextBox") as TextBox;
                    if (textBox != null)
                    {
                        WindowsUtils.SetWindowFocus(this);
                        textBox.Focus(FocusState.Keyboard);
                        textBox.SelectAll(); // 选中所有文本，方便用户直接输入
                    }
                });
            }
            
            
            
        }

        private void QuickAddTaskControl_TaskSaved(object sender, TodoTaskViewObject taskViewObject)
        {
            IsStartTask = QuickAddTaskControl.IsStartTask;
            TaskSaved?.Invoke(this, taskViewObject);
            Close();
        }

        private void QuickAddTaskControl_DialogCancelled(object sender, EventArgs e)
        {
            Close();
        }

        public void ShowAndActivate()
        {
            QuickAddTaskControl.ClearAndReset();
            this.Activate();
            // 强制将窗口带到前台
            SetForegroundWindow(_windowHandle);
        }
    }
}
