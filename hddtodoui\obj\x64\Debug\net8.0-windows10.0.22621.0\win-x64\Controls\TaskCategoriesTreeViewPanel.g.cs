﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TaskCategoriesTreeViewPanel.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "21D1621905B4628DB7E7F2EACE45A13110B5E09BECC7E528667B3895F35BC263"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskCategoriesTreeViewPanel : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_ContentDialog_IsPrimaryButtonEnabled(global::Microsoft.UI.Xaml.Controls.ContentDialog obj, global::System.Boolean value)
            {
                obj.IsPrimaryButtonEnabled = value;
            }
            public static void Set_Microsoft_UI_Xaml_UIElement_Visibility(global::Microsoft.UI.Xaml.UIElement obj, global::Microsoft.UI.Xaml.Visibility value)
            {
                obj.Visibility = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TreeViewItem_ItemsSource(global::Microsoft.UI.Xaml.Controls.TreeViewItem obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.ItemsSource = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::Microsoft.UI.Xaml.Media.Brush value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::Microsoft.UI.Xaml.Media.Brush) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::Microsoft.UI.Xaml.Media.Brush), targetNullValue);
                }
                obj.Foreground = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskCategoriesTreeViewPanel_obj42_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskCategoriesTreeViewPanel_Bindings
        {
            private global::HddtodoUI.Models.TaskCategoryViewObject dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private global::Microsoft.UI.Xaml.ResourceDictionary localResources;
            private global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement> converterLookupRoot;
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj42;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj43;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj44;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj45;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj46;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj47;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj48;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj49;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj50;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj42ItemsSourceDisabled = false;
            private static bool isobj43VisibilityDisabled = false;
            private static bool isobj44VisibilityDisabled = false;
            private static bool isobj45VisibilityDisabled = false;
            private static bool isobj46VisibilityDisabled = false;
            private static bool isobj47VisibilityDisabled = false;
            private static bool isobj48TextDisabled = false;
            private static bool isobj48VisibilityDisabled = false;
            private static bool isobj48ForegroundDisabled = false;
            private static bool isobj49TextDisabled = false;
            private static bool isobj50TextDisabled = false;

            private TaskCategoriesTreeViewPanel_obj42_BindingsTracking bindingsTracking;

            public TaskCategoriesTreeViewPanel_obj42_Bindings()
            {
                this.bindingsTracking = new TaskCategoriesTreeViewPanel_obj42_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 346 && columnNumber == 39)
                {
                    isobj42ItemsSourceDisabled = true;
                }
                else if (lineNumber == 351 && columnNumber == 53)
                {
                    isobj43VisibilityDisabled = true;
                }
                else if (lineNumber == 353 && columnNumber == 53)
                {
                    isobj44VisibilityDisabled = true;
                }
                else if (lineNumber == 355 && columnNumber == 53)
                {
                    isobj45VisibilityDisabled = true;
                }
                else if (lineNumber == 358 && columnNumber == 53)
                {
                    isobj46VisibilityDisabled = true;
                }
                else if (lineNumber == 361 && columnNumber == 53)
                {
                    isobj47VisibilityDisabled = true;
                }
                else if (lineNumber == 385 && columnNumber == 37)
                {
                    isobj48TextDisabled = true;
                }
                else if (lineNumber == 387 && columnNumber == 37)
                {
                    isobj48VisibilityDisabled = true;
                }
                else if (lineNumber == 388 && columnNumber == 37)
                {
                    isobj48ForegroundDisabled = true;
                }
                else if (lineNumber == 373 && columnNumber == 64)
                {
                    isobj49TextDisabled = true;
                }
                else if (lineNumber == 380 && columnNumber == 48)
                {
                    isobj50TextDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 42: // Controls\TaskCategoriesTreeViewPanel.xaml line 345
                        this.obj42 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeViewItem>(target));
                        break;
                    case 43: // Controls\TaskCategoriesTreeViewPanel.xaml line 350
                        this.obj43 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 44: // Controls\TaskCategoriesTreeViewPanel.xaml line 352
                        this.obj44 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 45: // Controls\TaskCategoriesTreeViewPanel.xaml line 354
                        this.obj45 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 46: // Controls\TaskCategoriesTreeViewPanel.xaml line 356
                        this.obj46 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 47: // Controls\TaskCategoriesTreeViewPanel.xaml line 359
                        this.obj47 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 48: // Controls\TaskCategoriesTreeViewPanel.xaml line 384
                        this.obj48 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 49: // Controls\TaskCategoriesTreeViewPanel.xaml line 373
                        this.obj49 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 50: // Controls\TaskCategoriesTreeViewPanel.xaml line 379
                        this.obj50 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj42.Target as global::Microsoft.UI.Xaml.Controls.TreeViewItem);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskCategoryViewObject>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITaskCategoriesTreeViewPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskCategoryViewObject>(newDataRoot);
                    return true;
                }
                return false;
            }
            public void SetConverterLookupRoot(global::Microsoft.UI.Xaml.FrameworkElement rootElement)
            {
                this.converterLookupRoot = new global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement>(rootElement);
            }

            public global::Microsoft.UI.Xaml.Data.IValueConverter LookupConverter(string key)
            {
                if (this.localResources == null)
                {
                    global::Microsoft.UI.Xaml.FrameworkElement rootElement;
                    this.converterLookupRoot.TryGetTarget(out rootElement);
                    this.localResources = rootElement.Resources;
                    this.converterLookupRoot = null;
                }
                return (global::Microsoft.UI.Xaml.Data.IValueConverter) (this.localResources.ContainsKey(key) ? this.localResources[key] : global::Microsoft.UI.Xaml.Application.Current.Resources[key]);
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TaskCategoryViewObject obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_(obj);
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_Children(obj.Children, phase);
                        this.Update_DueDate(obj.DueDate, phase);
                        this.Update_Name(obj.Name, phase);
                        this.Update_TaskCount(obj.TaskCount, phase);
                    }
                }
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 350
                    if (!isobj43VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj43, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("InboxVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), "addTask", null));
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 352
                    if (!isobj44VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj44, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("InboxVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), "addSubCategory", null));
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 354
                    if (!isobj45VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj45, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("InboxVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), "edit", null));
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 356
                    if (!isobj46VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj46, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("InboxVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), "complete", null));
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 359
                    if (!isobj47VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj47, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("InboxVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), "clearRecycle", null));
                    }
                }
            }
            private void Update_Children(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_Children(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 345
                    if (!isobj42ItemsSourceDisabled)
                    {
                        if ((this.obj42.Target as global::Microsoft.UI.Xaml.Controls.TreeViewItem) != null)
                        {
                            XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TreeViewItem_ItemsSource((this.obj42.Target as global::Microsoft.UI.Xaml.Controls.TreeViewItem), obj, null);
                        }
                    }
                }
            }
            private void Update_DueDate(global::System.Nullable<global::System.DateTime> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 384
                    if (!isobj48TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj48, (global::System.String)this.LookupConverter("DateTimeConverter").Convert(obj, typeof(global::System.String), null, null), null);
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 384
                    if (!isobj48VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj48, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("HasDateVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), null, null));
                    }
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 384
                    if (!isobj48ForegroundDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(this.obj48, (global::Microsoft.UI.Xaml.Media.Brush)this.LookupConverter("DueDateColorConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Media.Brush), null, null), null);
                    }
                }
            }
            private void Update_Name(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 373
                    if (!isobj49TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj49, obj, null);
                    }
                }
            }
            private void Update_TaskCount(global::System.Int64 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 379
                    if (!isobj50TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj50, obj.ToString(), null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskCategoriesTreeViewPanel_obj42_BindingsTracking
            {
                private global::System.WeakReference<TaskCategoriesTreeViewPanel_obj42_Bindings> weakRefToBindingObj; 

                public TaskCategoriesTreeViewPanel_obj42_BindingsTracking(TaskCategoriesTreeViewPanel_obj42_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskCategoriesTreeViewPanel_obj42_Bindings>(obj);
                }

                public TaskCategoriesTreeViewPanel_obj42_Bindings TryGetBindingObject()
                {
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_(null);
                    UpdateChildListeners_Children(null);
                }

                public void PropertyChanged_(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::HddtodoUI.Models.TaskCategoryViewObject obj = sender as global::HddtodoUI.Models.TaskCategoryViewObject;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                            if (obj != null)
                            {
                                bindings.Update_Children(obj.Children, DATA_CHANGED);
                                bindings.Update_DueDate(obj.DueDate, DATA_CHANGED);
                                bindings.Update_Name(obj.Name, DATA_CHANGED);
                                bindings.Update_TaskCount(obj.TaskCount, DATA_CHANGED);
                            }
                        }
                        else
                        {
                            switch (propName)
                            {
                                case "Children":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_Children(obj.Children, DATA_CHANGED);
                                    }
                                    break;
                                }
                                case "DueDate":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_DueDate(obj.DueDate, DATA_CHANGED);
                                    }
                                    break;
                                }
                                case "Name":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_Name(obj.Name, DATA_CHANGED);
                                    }
                                    break;
                                }
                                case "TaskCount":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_TaskCount(obj.TaskCount, DATA_CHANGED);
                                    }
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void UpdateChildListeners_(global::HddtodoUI.Models.TaskCategoryViewObject obj)
                {
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        if (bindings.dataRoot != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)bindings.dataRoot).PropertyChanged -= PropertyChanged_;
                        }
                        if (obj != null)
                        {
                            bindings.dataRoot = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_;
                        }
                    }
                }
                public void PropertyChanged_Children(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_Children(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> cache_Children = null;
                public void UpdateChildListeners_Children(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj)
                {
                    if (obj != cache_Children)
                    {
                        if (cache_Children != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_Children).PropertyChanged -= PropertyChanged_Children;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_Children).CollectionChanged -= CollectionChanged_Children;
                            cache_Children = null;
                        }
                        if (obj != null)
                        {
                            cache_Children = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_Children;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_Children;
                        }
                    }
                }
            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskCategoriesTreeViewPanel_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskCategoriesTreeViewPanel_Bindings
        {
            private global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.ContentDialog obj4;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj4IsPrimaryButtonEnabledDisabled = false;

            private TaskCategoriesTreeViewPanel_obj1_BindingsTracking bindingsTracking;

            public TaskCategoriesTreeViewPanel_obj1_Bindings()
            {
                this.bindingsTracking = new TaskCategoriesTreeViewPanel_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 430 && columnNumber == 24)
                {
                    isobj4IsPrimaryButtonEnabledDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 4: // Controls\TaskCategoriesTreeViewPanel.xaml line 425
                        this.obj4 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITaskCategoriesTreeViewPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_(obj);
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_IsAddListNameValid(obj.IsAddListNameValid, phase);
                    }
                }
            }
            private void Update_IsAddListNameValid(global::System.Boolean obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesTreeViewPanel.xaml line 425
                    if (!isobj4IsPrimaryButtonEnabledDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ContentDialog_IsPrimaryButtonEnabled(this.obj4, obj);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskCategoriesTreeViewPanel_obj1_BindingsTracking
            {
                private global::System.WeakReference<TaskCategoriesTreeViewPanel_obj1_Bindings> weakRefToBindingObj; 

                public TaskCategoriesTreeViewPanel_obj1_BindingsTracking(TaskCategoriesTreeViewPanel_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskCategoriesTreeViewPanel_obj1_Bindings>(obj);
                }

                public TaskCategoriesTreeViewPanel_obj1_Bindings TryGetBindingObject()
                {
                    TaskCategoriesTreeViewPanel_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_(null);
                }

                public void PropertyChanged_(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TaskCategoriesTreeViewPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel obj = sender as global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                            if (obj != null)
                            {
                                bindings.Update_IsAddListNameValid(obj.IsAddListNameValid, DATA_CHANGED);
                            }
                        }
                        else
                        {
                            switch (propName)
                            {
                                case "IsAddListNameValid":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_IsAddListNameValid(obj.IsAddListNameValid, DATA_CHANGED);
                                    }
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void UpdateChildListeners_(global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel obj)
                {
                    TaskCategoriesTreeViewPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        if (bindings.dataRoot != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)bindings.dataRoot).PropertyChanged -= PropertyChanged_;
                        }
                        if (obj != null)
                        {
                            bindings.dataRoot = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TaskCategoriesTreeViewPanel.xaml line 32
                {
                    this.SystemTasksListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.SystemTasksListView).SelectionChanged += this.TasksListView_SelectionChanged;
                }
                break;
            case 3: // Controls\TaskCategoriesTreeViewPanel.xaml line 66
                {
                    global::Microsoft.UI.Xaml.Controls.ScrollViewer element3 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ScrollViewer>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ScrollViewer)element3).Loaded += this.FrameworkElement_OnLoaded;
                    ((global::Microsoft.UI.Xaml.Controls.ScrollViewer)element3).ViewChanged += this.ScrollViewer_OnViewChanged;
                }
                break;
            case 4: // Controls\TaskCategoriesTreeViewPanel.xaml line 425
                {
                    this.AddListDialog = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)this.AddListDialog).PrimaryButtonClick += this.AddListDialog_PrimaryButtonClick;
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)this.AddListDialog).CloseButtonClick += this.AddListDialog_CloseButtonClick;
                }
                break;
            case 5: // Controls\TaskCategoriesTreeViewPanel.xaml line 434
                {
                    this.ListNameTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.ListNameTextBox).TextChanged += this.ListNameTextBox_TextChanged;
                }
                break;
            case 6: // Controls\TaskCategoriesTreeViewPanel.xaml line 439
                {
                    this.ListPriorityComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 7: // Controls\TaskCategoriesTreeViewPanel.xaml line 448
                {
                    this.ListDueDatePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CalendarDatePicker>(target);
                }
                break;
            case 8: // Controls\TaskCategoriesTreeViewPanel.xaml line 409
                {
                    global::Microsoft.UI.Xaml.Controls.Button element8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element8).Click += this.AddListButton_Click;
                }
                break;
            case 9: // Controls\TaskCategoriesTreeViewPanel.xaml line 414
                {
                    this.ManageCompletedListsButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.ManageCompletedListsButton).Click += this.ManageCompletedListsButton_Click;
                }
                break;
            case 10: // Controls\TaskCategoriesTreeViewPanel.xaml line 399
                {
                    this.BottomHint = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            case 11: // Controls\TaskCategoriesTreeViewPanel.xaml line 68
                {
                    this.UserCategoriesTreeView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).SelectionChanged += this.UserCategoriesTreeView_SelectionChanged;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragItemsStarting += this.UserCategoriesTreeView_OnDragItemsStarting;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragItemsCompleted += this.UserCategoriesTreeView_OnDragItemsCompleted;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragOver += this.UserCategoriesTreeView_OnDragOver;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragEnter += this.UserCategoriesTreeView_OnDragEnter;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragStarting += this.UserCategoriesTreeView_OnDragStarting;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).Drop += this.UserCategoriesTreeView_OnDrop;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).DragLeave += this.UserCategoriesTreeView_OnDragLeave;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).Expanding += this.UserCategoriesTreeView_Expanding;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).Collapsed += this.UserCategoriesTreeView_OnCollapsed;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserCategoriesTreeView).ItemInvoked += this.UserCategoriesTreeView_ItemInvoked;
                }
                break;
            case 42: // Controls\TaskCategoriesTreeViewPanel.xaml line 345
                {
                    global::Microsoft.UI.Xaml.Controls.TreeViewItem element42 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeViewItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TreeViewItem)element42).RightTapped += this.UserCategoriesTreeItem_RightTapped;
                }
                break;
            case 43: // Controls\TaskCategoriesTreeViewPanel.xaml line 350
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element43 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element43).Click += this.AddTaskMenuItem_Click;
                }
                break;
            case 44: // Controls\TaskCategoriesTreeViewPanel.xaml line 352
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element44 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element44).Click += this.AddSubCategoryMenuItem_Click;
                }
                break;
            case 45: // Controls\TaskCategoriesTreeViewPanel.xaml line 354
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element45 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element45).Click += this.EditTitleMenuItem_Click;
                }
                break;
            case 46: // Controls\TaskCategoriesTreeViewPanel.xaml line 356
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element46 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element46).Click += this.CompleteThisCategoryMenuItem_Click;
                }
                break;
            case 47: // Controls\TaskCategoriesTreeViewPanel.xaml line 359
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element47 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element47).Click += this.ClearRecycleBinMenuItem_Click;
                }
                break;
            case 51: // Controls\TaskCategoriesTreeViewPanel.xaml line 62
                {
                    this.TopHint = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TaskCategoriesTreeViewPanel.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TaskCategoriesTreeViewPanel_obj1_Bindings bindings = new TaskCategoriesTreeViewPanel_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            case 42: // Controls\TaskCategoriesTreeViewPanel.xaml line 345
                {                    
                    global::Microsoft.UI.Xaml.Controls.TreeViewItem element42 = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)target;
                    TaskCategoriesTreeViewPanel_obj42_Bindings bindings = new TaskCategoriesTreeViewPanel_obj42_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element42.DataContext);
                    bindings.SetConverterLookupRoot(this);
                    element42.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element42, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element42, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

