using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.JsonConverters;


namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskStepStore : HttpStoreBase, ITaskStepStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;

        public HttpTaskStepStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = null,
            Converters =
            {
                new DateTimeJsonConverter(),
            }
        })
        {
        }

        public List<TaskStep> GetTaskSteps(long taskId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{taskId}/steps/{userId}";
            return SendGetRequestAsync<List<TaskStep>>(endpoint, "Getting task steps").Result;
        }

        public TaskStep getTaskStepById(long stepId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/steps/{stepId}/{userId}";
            return SendGetRequestAsync<TaskStep>(endpoint, "Getting task step by ID").Result;
        }

        public void saveTaskStepChange(TaskStep taskStep, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/steps/save/{userId}";
            SendPutRequestAsync(endpoint, "Saving task step change", taskStep).Wait();
        }

        public void dragTaskStep(TaskStep from, TaskStep target, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/steps/drag/{userId}";
            SendPutRequestAsync(endpoint, "Dragging task step", new { FromStepId = from.StepId, TargetStepId = target.StepId }).Wait();
        }

        /// <summary>
        /// Repositions multiple steps within a task.
        /// </summary>
        /// <param name="taskId">The ID of the task containing the steps.</param>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="itemsToReposition">A list of steps to reposition, with their new positions.</param>
        /// <returns>A response object indicating success or failure.</returns>
        public async Task<RepositionStepsResponse> RepositionStepsAsync(long taskId, long userId, List<RepositionStepItem> itemsToReposition)
        {
            var endpoint = $"{BaseUrl}/tasks/{taskId}/steps/reposition/{userId}";

            var requestBody = new RepositionStepsRequest { Items = itemsToReposition };

            return await SendPostRequestAsync<RepositionStepsResponse>(endpoint, "Repositioning steps", requestBody);
        }
    }

    
}
