﻿<?xml version="1.0" encoding="utf-8"?>
<ContentDialog x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TaskRestartDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="设置任务重复"
    PrimaryButtonText="保存"
    SecondaryButtonText="取消"
    DefaultButton="Primary"
    CloseButtonText="删除重复设置"
                                                     >

    <Grid RowSpacing="12" Width="400">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 下次重启时间设置 -->
        <StackPanel Grid.Row="0" Spacing="8">
            <TextBlock Text="下次重启时间" Style="{StaticResource BodyStrongTextBlockStyle}" />
            <DatePicker x:ConnectionId='6' x:Name="RestartDatePicker" HorizontalAlignment="Stretch"                                                              />
            <TimePicker x:ConnectionId='7' x:Name="RestartTimePicker" HorizontalAlignment="Stretch"                                                              />
        </StackPanel>

        <!-- 重复周期类型设置 -->
        <StackPanel Grid.Row="1" Spacing="8">
            <TextBlock Text="重复周期" Style="{StaticResource BodyStrongTextBlockStyle}"/>
            <ComboBox x:ConnectionId='5' x:Name="PeriodTypeComboBox" HorizontalAlignment="Stretch"                                                       >
                <ComboBoxItem Content="一次性" Tag="NONE" IsEnabled="False"/>
                <ComboBoxItem Content="每天" Tag="DAILY"/>
                <ComboBoxItem Content="每周" Tag="WEEKLY"/>
                <ComboBoxItem Content="每月" Tag="MONTHLY"/>
                <ComboBoxItem Content="每年" Tag="YEARLY"/>
                <ComboBoxItem Content="每小时" Tag="HOUR" IsEnabled="False"/>
            </ComboBox>
        </StackPanel>

        <!-- 周期间隔设置 -->
        <StackPanel x:ConnectionId='2' Grid.Row="2" Spacing="8" x:Name="PeriodSpanPanel" Visibility="Collapsed">
            <TextBlock Text="周期间隔" Style="{StaticResource BodyStrongTextBlockStyle}"/>
            <NumberBox x:ConnectionId='4' x:Name="PeriodSpanNumberBox" Minimum="1" Value="1" SpinButtonPlacementMode="Inline" HorizontalAlignment="Stretch"                                                  />
        </StackPanel>

        <!-- 提示信息 -->
        <TextBlock x:ConnectionId='3' Grid.Row="3" x:Name="NextRestartInfoTextBlock" TextWrapping="Wrap" Style="{StaticResource CaptionTextBlockStyle}" Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
    </Grid>
</ContentDialog>

