using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Utilities;

public class ScrollUtils
{
    /// <summary>
    /// 查找控件中的 ScrollViewer
    /// </summary>
    /// <param name="depObj">要查找的控件</param>
    /// <returns>找到的 ScrollViewer 或 null</returns>
    public static ScrollViewer GetScrollViewer(DependencyObject depObj)
    {
        if (depObj is ScrollViewer sv)
            return sv;

        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
        {
            var child = VisualTreeHelper.GetChild(depObj, i);
            var result = GetScrollViewer(child);
            if (result != null)
                return result;
        }
        return null;
    }

    /// <summary>
    /// 保存 ListView 的垂直滚动位置
    /// </summary>
    /// <param name="listView">目标 ListView</param>
    /// <returns>当前的垂直滚动偏移量</returns>
    public static double SaveScrollPosition(ListView listView)
    {
        if (listView == null)
            return 0;

        var scrollViewer = GetScrollViewer(listView);
        return scrollViewer?.VerticalOffset ?? 0;
    }

    /// <summary>
    /// 恢复 ListView 的垂直滚动位置
    /// </summary>
    /// <param name="listView">目标 ListView</param>
    /// <param name="offset">要恢复的滚动偏移量</param>
    /// <param name="disableAnimation">是否禁用滚动动画</param>
    public static void RestoreScrollPosition(ListView listView, double offset, bool disableAnimation = true)
    {
        if (listView == null)
            return;

        var scrollViewer = GetScrollViewer(listView);
        if (scrollViewer != null)
        {
            // 使用 ChangeView 恢复滚动位置
            // 参数: horizontalOffset, verticalOffset, zoomFactor, disableAnimation
            scrollViewer.ChangeView(null, offset, null, disableAnimation);
        }
    }

}