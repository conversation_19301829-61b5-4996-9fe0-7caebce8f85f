using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.BackendStore.HttpStore;
using HddtodoUI.Utilities;

namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskCategoryStore
    {
        /// <summary>
        /// 查询指定用户的所有“未完成顶层任务分类”，且这些分类必须有截止时间（CategoryDueTime 不为 null）。
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>符合条件的顶层未完成任务分类列表</returns>
        Task<List<TaskCategory>> GetTopLevelUncompletedWithDueCategoriesAsync(long userId);
        
        public CountResponse GetTopLevelUncompletedWithDueCategoriesCount(
            long userId);

        public Task<List<TaskCategory>> GetUncompletedWithDueCategoriesAsync(long userId);
        public CountResponse GetUncompletedWithDueCategoriesCount(long userId);

    
        /// <summary>
        /// 获取未完成的分类列表，并统计每个分类下未完成的直接子分类数量和任务数量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="parentKey">父分类的Key，可选。不传时返回顶级分类</param>
        /// <returns>包含分类、子分类数量和任务数量的列表</returns>
        List<TaskCategoryWithCount> GetUncompleteTaskCategoriesWithCount(long userId, string parentKey = null);

        /// <summary>
        /// 获取所有未完成且有截止时间的分类（每项包含分类、子分类数量、任务数量）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>包含分类本身、子分类数量、任务数量的列表</returns>
        Task<List<TaskCategoryWithCount>> GetUncompletedWithDueTaskCountAsync(long userId);

        
        /// <summary>
        /// 根据Key获取分类
        /// </summary>
        /// <param name="key">分类Key</param>
        /// <param name="userId">用户ID</param>
        /// <returns>分类对象</returns>
        TaskCategory GetTaskCategoryByKey(string key, long userId);
        
        /// <summary>
        /// 创建任务分类
        /// </summary>
        /// <param name="name">分类名称</param>
        /// <param name="userId">用户ID</param>
        /// <param name="parentCategoryKey">父分类Key，可选</param>
        /// <param name="dueTime">到期时间，可选</param>
        /// <param name="isHide">是否隐藏，默认为false</param>
        /// <returns>创建的分类对象</returns>
        Task<TaskCategory> CreateTaskCategory(string name, long userId, string parentCategoryKey = null, DateTime? dueTime = null, bool isHide = false);

        /// <summary>
        /// 更新任务分类
        /// </summary>
        /// <param name="categoryKey">要更新的分类Key</param>
        /// <param name="userId">用户ID</param>
        /// <param name="name">新的分类名称</param>
        /// <param name="categoryDueTime">分类截止时间，设为 null 表示清除截止时间</param>
        /// <param name="isHide">是否隐藏</param>
        /// <param name="categoryOrder">分类排序</param>
        /// <returns>更新后的分类对象</returns>
        public Task<TaskCategory> UpdateTaskCategory(string categoryKey, long userId,
            UpdateValue<DateTime?> categoryCompleteTime = null,
            UpdateValue<DateTime?> categoryDueTime = null, string name = null,
            bool? isHide = null, int? categoryOrder = null);
        // /// <summary>
        // /// 创建空分类
        // /// </summary>
        // /// <param name="name">分类名称</param>
        // /// <param name="key">分类Key</param>
        // /// <param name="userId">用户ID</param>
        // /// <param name="parentCategoryKey">父分类Key，可选</param>
        // /// <returns>创建的分类对象</returns>
        // TaskCategory CreateEmptyTaskCategory(string name, string key, long userId, string parentCategoryKey = null);
        
        // /// <summary>
        // /// 获取用户的分类数量
        // /// </summary>
        // /// <param name="userId">用户ID</param>
        // /// <returns>分类数量</returns>
        // long GetTaskCategoryCount(long userId);
        
        // /// <summary>
        // /// 检查指定Key的分类是否存在
        // /// </summary>
        // /// <param name="key">分类Key</param>
        // /// <param name="userId">用户ID</param>
        // /// <returns>是否存在</returns>
        // bool HasTaskCategoryByKey(string key, long userId);
        
        // /// <summary>
        // /// 删除分类
        // /// </summary>
        // /// <param name="taskCategory">要删除的分类</param>
        // /// <param name="userId">用户ID</param>
        // void RemoveTaskCategory(TaskCategory taskCategory, long userId);
        
        // /// <summary>
        // /// 保存分类的更改
        // /// </summary>
        // /// <param name="taskCategory">已修改的分类</param>
        // /// <param name="userId">用户ID</param>
        // /// <returns>更新后的分类对象</returns>
        // TaskCategory SaveTaskCategoryChange(TaskCategory taskCategory, long userId);
        
        // /// <summary>
        // /// 获取分类下未完成任务的数量
        // /// </summary>
        // /// <param name="key">分类Key</param>
        // /// <param name="userId">用户ID</param>
        // /// <returns>未完成任务数量</returns>
        // long GetTaskCategoryUnCompleteTaskCount(string key, long userId);
        
        // /// <summary>
        // /// 获取分类下已完成任务的数量
        // /// </summary>
        // /// <param name="key">分类Key</param>
        // /// <param name="userId">用户ID</param>
        // /// <returns>已完成任务数量</returns>
        // long GetTaskCategoryCompleteTaskCount(string key, long userId);
        /// <summary>
        /// 获取分类中未完成任务的数量
        /// </summary>
        /// <param name="categoryKey">分类的唯一标识</param>
        /// <param name="userId">用户ID</param>
        /// <returns>未完成任务的数量</returns>
        int GetCategoryUncompleteTaskCount(string categoryKey, long userId);
        
        /// <summary>
        /// 获取分类中已完成任务的数量
        /// </summary>
        /// <param name="categoryKey">分类的唯一标识</param>
        /// <param name="userId">用户ID</param>
        /// <returns>已完成任务的数量</returns>
        int GetCategoryCompleteTaskCount(string categoryKey, long userId);

        public Task<RepositionCategoriesResponse> RepositionCategoriesAsync(long userId,
            List<RepositionCategoryItem> itemsToReposition, string newParentKey = null, string previousParentKey = null);

        public List<TaskCategory> GetCompletedTaskCategoriesAsync(long userId, string parentKey = null,
            string keyword = null, int? page = null, int? pageSize = null);

        public CountResponse GetCompletedTaskCategoriesCountAsync(long userId, string parentKey = null,
            string keyword = null);
    }
    
    /// <summary>
    /// 包含分类、子分类数量和任务数量的类
    /// </summary>
    public class TaskCategoryWithCount
    {
        public TaskCategory Category { get; set; }
        public int SubcategoryCount { get; set; }
        public int TaskCount { get; set; }
    }
    
    public class RepositionCategoryItem
    {
        [JsonPropertyName("categoryKey")] public string CategoryKey { get; set; }

        [JsonPropertyName("position")] public int Position { get; set; }
    }

    public class RepositionCategoriesRequest
    {
        [JsonPropertyName("items")] public List<RepositionCategoryItem> Items { get; set; }
    }

    public class RepositionCategoriesResponse
    {
        [JsonPropertyName("success")] public bool Success { get; set; }

        [JsonPropertyName("message")] public string Message { get; set; }
    }
}
