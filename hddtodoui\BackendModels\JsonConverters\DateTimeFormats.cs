using System;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// 统一的DateTime格式常量管理类
    /// 所有DateTime相关的格式定义都在这里，确保一致性
    /// </summary>
    public static class DateTimeFormats
    {
        #region 当前使用的格式

        /// <summary>
        /// 当前统一使用的DateTime格式 - ISO 8601国际化格式
        /// 格式: yyyy-MM-dd'T'HH:mm:ss.fffzzz
        /// 示例: 2025-07-02T09:39:00.123+08:00
        /// </summary>
        public const string Current = "yyyy-MM-dd'T'HH:mm:ss.fffzzz";

        #endregion

        #region 国际化推荐格式

        /// <summary>
        /// ISO 8601 标准格式 - 带时区信息（推荐用于国际化）
        /// 格式: yyyy-MM-dd'T'HH:mm:ss.fffzzz
        /// 示例: 2025-07-02T09:39:00.123+08:00
        /// </summary>
        public const string ISO8601WithTimeZone = "yyyy-MM-dd'T'HH:mm:ss.fffzzz";

        /// <summary>
        /// ISO 8601 UTC格式 - 国际化标准UTC时间
        /// 格式: yyyy-MM-dd'T'HH:mm:ss.fff'Z'
        /// 示例: 2025-07-02T01:39:00.123Z
        /// </summary>
        public const string ISO8601UTC = "yyyy-MM-dd'T'HH:mm:ss.fff'Z'";

        /// <summary>
        /// ISO 8601 简化格式 - 不含毫秒但带时区
        /// 格式: yyyy-MM-dd'T'HH:mm:sszzz
        /// 示例: 2025-07-02T09:39:00+08:00
        /// </summary>
        public const string ISO8601Simple = "yyyy-MM-dd'T'HH:mm:sszzz";

        #endregion

        #region 向后兼容格式数组

        /// <summary>
        /// 支持解析的所有格式（向后兼容）
        /// 按优先级排序，优先尝试解析靠前的格式
        /// </summary>
        public static readonly string[] SupportedFormats = {
            Current,                           // 当前格式
            ISO8601WithTimeZone,              // 原带时区格式
            ISO8601Simple,                    // 简化ISO格式
            ISO8601UTC,                       // UTC格式
            "yyyy-MM-ddTHH:mm:ss.fffZ",      // 原UTC格式
            "yyyy-MM-ddTHH:mm:ss.fff",       // 原本地格式
            "yyyy-MM-ddTHH:mm:ss",           // 基础格式
            "yyyy-MM-dd"                     // 日期格式
        };

        #endregion

        #region 格式切换方法

        /// <summary>
        /// 获取当前使用的格式
        /// 可以通过配置或环境变量来动态切换
        /// </summary>
        /// <returns>当前应该使用的DateTime格式</returns>
        public static string GetCurrentFormat()
        {
            // 使用配置管理类来获取当前格式
            return DateTimeFormatConfiguration.GetCurrentFormat();
        }

        /// <summary>
        /// 获取国际化推荐格式
        /// </summary>
        /// <returns>国际化推荐的DateTime格式</returns>
        public static string GetInternationalFormat()
        {
            return ISO8601WithTimeZone;
        }

        /// <summary>
        /// 根据应用场景获取合适的格式
        /// </summary>
        /// <param name="scenario">应用场景</param>
        /// <returns>对应场景的DateTime格式</returns>
        public static string GetFormatByScenario(DateTimeScenario scenario)
        {
            return scenario switch
            {
                DateTimeScenario.BackendApi => Current,
                DateTimeScenario.International => ISO8601WithTimeZone,
                DateTimeScenario.UTC => ISO8601UTC,
                DateTimeScenario.Simple => ISO8601Simple,
                _ => Current
            };
        }

        #endregion
    }

    /// <summary>
    /// DateTime使用场景枚举
    /// </summary>
    public enum DateTimeScenario
    {
        /// <summary>
        /// 后台API通信
        /// </summary>
        BackendApi,

        /// <summary>
        /// 国际化应用
        /// </summary>
        International,

        /// <summary>
        /// UTC时间
        /// </summary>
        UTC,

        /// <summary>
        /// 简化格式
        /// </summary>
        Simple
    }
}
