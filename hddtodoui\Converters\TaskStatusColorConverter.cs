using System;
using HddtodoUI.Models;
using Microsoft.UI;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Converters
{
    public class TaskStatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is TaskStatus status)
            {
                return status switch
                {
                    TaskStatus.NotStarted => new SolidColorBrush(Colors.DodgerBlue),  // 蓝色
                    TaskStatus.InProgress => new SolidColorBrush(Colors.Green),       // 绿色
                    TaskStatus.Paused => new SolidColorBrush(Colors.Orange),          // 橙色
                    TaskStatus.Completed => new SolidColorBrush(Colors.Green),        // 绿色
                    _ => new SolidColorBrush(Colors.DodgerBlue)                       // 蓝色
                };
            }
            return new SolidColorBrush(Colors.DodgerBlue); // 默认蓝色
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
