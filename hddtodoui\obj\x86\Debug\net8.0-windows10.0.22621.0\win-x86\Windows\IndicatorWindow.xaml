﻿<?xml version="1.0" encoding="utf-8"?>

<Window
    x:Class="HddtodoUI.Windows.IndicatorWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="任务指示器">

    <!-- <Window.SystemBackdrop> -->
    <!--     <DesktopAcrylicBackdrop/> -->
    <!-- </Window.SystemBackdrop> -->
    
    <SplitView x:ConnectionId='2' x:Name="MainSplitView" PaneBackground="{ThemeResource SystemControlBackgroundChromeMediumLowBrush}"
               IsPaneOpen="False" OpenPaneLength="256" CompactPaneLength="48" DisplayMode="Overlay">

        <SplitView.Pane>
            <Grid x:ConnectionId='3' x:Name="InfoRootGrid">
                <Grid.Resources>
                    <Style x:Key="StartButtonStyle" TargetType="Button">
                        <Setter Property="Background" Value="Transparent" />
                        <Setter Property="BorderBrush" Value="Transparent" />
                        <Setter Property="BorderThickness" Value="0" />
                        <Setter Property="Padding" Value="8" />
                        <Setter Property="HorizontalAlignment" Value="Center" />
                        <Setter Property="VerticalAlignment" Value="Center" />
                    </Style>
                </Grid.Resources>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="45" />
                    <ColumnDefinition Width="1" />
                    <ColumnDefinition Width="*" />
                  
                </Grid.ColumnDefinitions>
                
                <Border x:ConnectionId='4' x:Name="PaneMainButtonBorder" Grid.Column="0" VerticalAlignment="Center" MinHeight="80"
                     
                        Background="Transparent">
                    <Button x:ConnectionId='7' x:Name="PaneMainWindowButton"
                            Style="{StaticResource StartButtonStyle}"
                                                            
                            Margin="0,0"
                    >
                        <FontIcon
                            Glyph="&#xE80F;" 
                            FontSize="20"
                            Foreground="Gray"
                        />
                    </Button>
                </Border>

              
                <Rectangle Grid.Column="1" 
                           Fill="{ThemeResource SystemControlBackgroundBaseLowBrush}" 
                           Width="1" 
                           Margin="0,10"
                           VerticalAlignment="Stretch" />
                
                <Border x:ConnectionId='5' x:Name="InfoTitleTextBorder" Grid.Column="2" VerticalAlignment="Center" MinHeight="80"
                      
                                                             
                        Background="Transparent">
                    <TextBlock x:ConnectionId='6' x:Name="InfoTitleTextBlock"
                               Text="请您思考或者选择下一个任务"
                               FontSize="18"
                               VerticalAlignment="Center"
                               HorizontalTextAlignment="Left"
                               TextWrapping="Wrap" Padding="15,0,0,0"/>
                </Border>
            </Grid>
            
            
        </SplitView.Pane>
        
        <Grid x:ConnectionId='8' x:Name="RootGrid">
            <Grid.Resources>
                <Style x:Key="StartButtonStyle" TargetType="Button">
                    <Setter Property="Background" Value="Transparent" />
                    <Setter Property="BorderBrush" Value="Transparent" />
                    <Setter Property="BorderThickness" Value="0" />
                    <Setter Property="Padding" Value="8" />
                    <Setter Property="HorizontalAlignment" Value="Center" />
                    <Setter Property="VerticalAlignment" Value="Center" />
                </Style>
            </Grid.Resources>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="45" />
                <ColumnDefinition Width="1" />
                <ColumnDefinition Width="30" />
                <ColumnDefinition Width="50" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="90" />
               
            </Grid.ColumnDefinitions>
        
            
            <Border x:ConnectionId='9' x:Name="MainButtonBorder" Grid.Column="0" VerticalAlignment="Center" MinHeight="80"
                 
                    Background="Transparent">
                <Button x:ConnectionId='18' x:Name="MainWindowButton"
                        Style="{StaticResource StartButtonStyle}"
                                                        
                        Margin="0,0"
                >
                    <FontIcon
                        Glyph="&#xE80F;" 
                        FontSize="20"
                        Foreground="Gray"
                    />
                </Button>
            </Border>

              
            <Rectangle Grid.Column="1" 
                       Fill="{ThemeResource SystemControlBackgroundBaseLowBrush}" 
                       Width="1" 
                       Margin="0,10"
                       VerticalAlignment="Stretch" />
            
            <Border x:ConnectionId='10' x:Name="TaskCheckBoxBorder" Grid.Column="2" VerticalAlignment="Center" MinHeight="80"
                
                    Background="Transparent">
                <CheckBox x:ConnectionId='17' x:Name="TaskCheckBox"
                          VerticalAlignment="Center"
                          Margin="10,0,0,0" />
            </Border>

            <!-- 第二列：开始按钮 (图标形式) -->
            <Border x:ConnectionId='11' x:Name="StartButtonBorder" Grid.Column="3" VerticalAlignment="Center" MinHeight="80"
                
                    Background="Transparent">
                <Button x:ConnectionId='16' x:Name="StartButton"
                        Style="{StaticResource StartButtonStyle}"
                                                   
                        Margin="0,0"
                        ToolTipService.ToolTip="{Binding Status, Mode=OneWay, Converter={StaticResource TaskStatusTooltipConverter}}">
                    <FontIcon
                        Glyph="{Binding  Status, Mode=OneWay, Converter={StaticResource TaskStatusIconConverter}}"
                        FontSize="24"
                        Foreground="{Binding  Status, Mode=OneWay, Converter={StaticResource TaskStatusColorConverter}}" />
                </Button>
            </Border>

            <!-- 第三列：任务标题 -->
            <Border x:ConnectionId='12' x:Name="TitleTextBorder" Grid.Column="4" VerticalAlignment="Center" MinHeight="80"
                  
                                                     
                    Background="Transparent">
                <TextBlock x:ConnectionId='15' x:Name="TaskTitleTextBlock"
                           Text="任务标题"
                           FontSize="18"
                           VerticalAlignment="Center"
                           HorizontalTextAlignment="Left"
                           TextWrapping="Wrap" />
            </Border>
            <!-- 第四列：倒计时时间 -->
            <Border x:ConnectionId='13' x:Name="CountdownTextBorder" Grid.Column="5" VerticalAlignment="Center" MinHeight="80"
                                                     
                    Background="Transparent">
                <TextBlock x:ConnectionId='14' x:Name="CountdownTextBlock"
                           Text="00:00"
                           VerticalAlignment="Center"
                           FontSize="18"
                           Margin="8,0,8,0"
                           FontFamily="Consolas" />
            </Border>
          
        </Grid>

    </SplitView>
</Window>

