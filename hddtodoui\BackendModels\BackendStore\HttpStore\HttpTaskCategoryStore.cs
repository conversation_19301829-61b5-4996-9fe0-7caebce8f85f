using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.JsonConverters;
using System.Linq; // Added for .Any() method
using HddtodoUI.BackendModels;
using HddtodoUI.Services;
using HddtodoUI.Utilities;

// 定义用于接收count响应的类
namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class CountResponse
    {
        [JsonPropertyName("count")] public int Count { get; set; }
    }

    namespace HddtodoUI.BackendModels.BackendStore.HttpStore
    {
        public class HttpTaskCategoryStore : HttpStoreBase, ITaskCategoryStore
        {
            private string BaseUrl = HttpStoreBase.baseUrl;

            public HttpTaskCategoryStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters =
                {
                    new DateTimeJsonConverter(),
                    new NullableDateTimeJsonConverter()
                }
            })
            {
            }
            
            public async Task<List<TaskCategory>> GetTopLevelUncompletedWithDueCategoriesAsync(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/top-level/uncompleted-with-due/{userId}";
                try
                {
                    var response = await SendGetRequestAsync<List<TaskCategory>>(endpoint, "GetTopLevelUncompletedWithDueCategories");
                    return response ?? new List<TaskCategory>();
                }
                catch (HttpRequestException e)
                {
                    // 可根据需要记录日志或抛出自定义异常
                    LogService.Instance.Error("请求失败", e);
                    return new List<TaskCategory>();
                }
            }
            
            /// <summary>
            /// 获取所有未完成且有截止时间的分类列表（同步版）
            /// </summary>
            public List<TaskCategory> GetUncompletedWithDueCategories(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/uncompleted-with-due/{userId}";
                return SendGetRequestAsync<List<TaskCategory>>(endpoint, "GetUncompletedWithDueCategories").Result ?? new List<TaskCategory>();
            }

            /// <summary>
            /// 获取所有未完成且有截止时间的分类列表（不限制TopLevel）
            /// </summary>
            public async Task<List<TaskCategory>> GetUncompletedWithDueCategoriesAsync(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/uncompleted-with-due/{userId}";
                try
                {
                    var response = await SendGetRequestAsync<List<TaskCategory>>(endpoint, "GetUncompletedWithDueCategories");
                    return response ?? new List<TaskCategory>();
                }
                catch (HttpRequestException e)
                {
                    LogService.Instance.Error("请求所有未完成计划分类列表失败", e);
                    return new List<TaskCategory>();
                }
            }

            /// <summary>
            /// 获取所有未完成且有截止时间的分类数量（不限制TopLevel）
            /// </summary>
            public async Task<long> GetUncompletedWithDueCategoriesCountAsync(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/uncompleted-with-due/count/{userId}";
                try
                {
                    var response = await SendGetRequestAsync<CountResponse>(endpoint, "GetUncompletedWithDueCategoriesCountAsync");
                    return response?.Count ?? 0L;
                }
                catch (HttpRequestException e)
                {
                    LogService.Instance.Error("请求所有未完成计划分类数量失败", e);
                    return 0L;
                }
            }
            
          

            /// <summary>
            /// 获取所有未完成且有截止时间的分类数量（同步版）
            /// </summary>
            public CountResponse GetUncompletedWithDueCategoriesCount(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/uncompleted-with-due/count/{userId}";
                return SendGetRequestAsync<CountResponse>(endpoint, "GetUncompletedWithDueCategoriesCount").Result;
            }

           

            /// <summary>
            /// 统计未完成且有截止时间的顶层分类数量
            /// </summary>
            public async Task<long> GetTopLevelUncompletedWithDueCategoriesCountAsync(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/top-level/uncompleted-with-due/count/{userId}";
                try
                {
                    var response = await SendGetRequestAsync<CountResponse>(endpoint, "GetTopLevelUncompletedWithDueCategoriesCountAsync");
                    return response?.Count ?? 0L;
                }
                catch (HttpRequestException e)
                {
                    LogService.Instance.Error("请求统计未完成计划分类数量失败", e);
                    return 0L;
                }
            }
            
            public CountResponse GetTopLevelUncompletedWithDueCategoriesCount(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/top-level/uncompleted-with-due/count/{userId}";
                return SendGetRequestAsync<CountResponse>(endpoint, "GetTopLevelUncompletedWithDueCategoriesCount").Result;
            }
         

            /// <summary>
            /// 创建任务分类
            /// </summary>
            /// <param name="name">分类名称</param>
            /// <param name="userId">用户ID</param>
            /// <param name="parentCategoryKey">父分类Key，可选</param>
            /// <param name="dueTime">到期时间，可选</param>
            /// <param name="isHide">是否隐藏，默认为false</param>
            /// <returns>创建的分类对象</returns>
            public async Task<TaskCategory> CreateTaskCategory(string name, long userId,
                string parentCategoryKey = null, DateTime? dueTime = null, bool isHide = false)
            {
                var endpoint = $"{BaseUrl}/categories/create/{userId}";

                var requestBody = new
                {
                    name = name,
                    parentCategoryKey = parentCategoryKey,
                    categoryDueTime = dueTime?.ToString("o"),
                    isHide = isHide
                };

                return await SendPostRequestAsync<TaskCategory>(endpoint, "CreateTaskCategory", requestBody);
            }

            /// <summary>
            /// 获取未完成的分类列表，并统计每个分类下未完成的直接子分类数量和任务数量
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <param name="parentKey">父分类的Key，可选。不传时返回顶级分类</param>
            /// <returns>包含分类、子分类数量和任务数量的列表</returns>
            /// <summary>
            /// 更新任务分类
            /// </summary>
            /// <param name="categoryKey">要更新的分类Key</param>
            /// <param name="userId">用户ID</param>
            /// <param name="name">新的分类名称</param>
            /// <param name="isHide">是否隐藏</param>
            /// <param name="categoryOrder">分类排序</param>
            /// <returns>更新后的分类对象</returns>
            public async Task<TaskCategory> UpdateTaskCategory(string categoryKey, long userId,  UpdateValue<DateTime?> categoryCompleteTime = null, 
                UpdateValue<DateTime?> categoryDueTime = null,  string name = null,
                 bool? isHide = null, int? categoryOrder = null)
            {
                var endpoint = $"{BaseUrl}/categories/update/{categoryKey}/{userId}";

                // 创建请求体，只包含非空的属性
                var filteredBody = new Dictionary<string, object>();

                if (name != null)
                    filteredBody["name"] = name;
                
                if (categoryCompleteTime is SetValue<DateTime?> setCompleteTime)
                {
                    filteredBody["categoryCompleteTime"] =JsonDateTimeStringConverter.Convert( setCompleteTime.Value);
                }

                // 处理 categoryDueTime
                if (categoryDueTime is SetValue<DateTime?> setDueTime)
                {
                    filteredBody["categoryDueTime"] = JsonDateTimeStringConverter.Convert( setDueTime.Value);
                }
                
                if (isHide.HasValue)
                    filteredBody["isHide"] = isHide.Value;

                if (categoryOrder.HasValue)
                    filteredBody["categoryOrder"] = categoryOrder.Value;

                return await SendPutRequestAsync<TaskCategory>(endpoint, "UpdateTaskCategory", filteredBody);
            }
            
            


            /// <summary>
            /// 获取已完成分类列表（分页）
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <param name="parentKey">父分类Key，可选</param>
            /// <param name="keyword">分类名称关键词，可选</param>
            /// <param name="page">页码，从0开始，可选</param>
            /// <param name="pageSize">每页条数，可选</param>
            /// <returns>已完成的分类列表</returns>
            public List<TaskCategory> GetCompletedTaskCategoriesAsync(long userId, string parentKey = null, string keyword = null, int? page = null, int? pageSize = null)
            {
                var queryParams = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(parentKey))
                    queryParams.Add("parentKey", parentKey);
                if (!string.IsNullOrEmpty(keyword))
                    queryParams.Add("keyword", keyword);
                if (page.HasValue)
                    queryParams.Add("page", page.Value.ToString());
                if (pageSize.HasValue)
                    queryParams.Add("pageSize", pageSize.Value.ToString());

                var endpoint = $"{BaseUrl}/categories/completed/{userId}";
                
                if (queryParams.Any())
                {
                    var queryString = string.Join("&", queryParams
                        .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));
                    endpoint += $"?{queryString}";
                }

                return SendGetRequestAsync<List<TaskCategory>>(endpoint, "Getting completed task categories").Result;
            }

            /// <summary>
            /// 获取已完成分类数量
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <param name="parentKey">父分类Key，可选</param>
            /// <param name="keyword">分类名称关键词，可选</param>
            /// <returns>已完成分类的数量对象</returns>
            public CountResponse GetCompletedTaskCategoriesCountAsync(long userId, string parentKey = null, string keyword = null)
            {
                var queryParams = new Dictionary<string, string>();
                if (!string.IsNullOrEmpty(parentKey))
                    queryParams.Add("parentKey", parentKey);
                if (!string.IsNullOrEmpty(keyword))
                    queryParams.Add("keyword", keyword);
                
                var endpoint = $"{BaseUrl}/categories/completed-count/{userId}";

                if (queryParams.Any())
                {
                    var queryString = string.Join("&", queryParams
                        .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));
                    endpoint += $"?{queryString}";
                }
                
                return  SendGetRequestAsync<CountResponse>(endpoint, "Getting completed task categories count").Result;
            }


            public List<TaskCategoryWithCount> GetUncompleteTaskCategoriesWithCount(long userId,
                string parentKey = null)
            {
                string endpoint;
                if (string.IsNullOrEmpty(parentKey))
                {
                    endpoint = $"{BaseUrl}/categories/uncompletewithcount/{userId}";
                }
                else
                {
                    endpoint =
                        $"{BaseUrl}/categories/uncompletewithcount/{userId}?parentKey={Uri.EscapeDataString(parentKey)}";
                }

                return SendGetRequestAsync<List<TaskCategoryWithCount>>(endpoint,
                    "Getting uncomplete task categories with counts").Result;
            }

            /// <summary>
            /// 获取所有未完成且有截止时间的分类列表（每项包含分类、子分类数量、任务数量）
            /// </summary>
            /// <param name="userId">用户ID</param>
            /// <returns>包含分类本身、子分类数量、任务数量的列表</returns>
            public async Task<List<TaskCategoryWithCount>> GetUncompletedWithDueTaskCountAsync(long userId)
            {
                var endpoint = $"{BaseUrl}/categories/uncompleted-with-due-taskcount/{userId}";
                try
                {
                    var response = await SendGetRequestAsync<List<TaskCategoryWithCount>>(endpoint, "GetUncompletedWithDueTaskCountAsync");
                    return response ?? new List<TaskCategoryWithCount>();
                }
                catch (HttpRequestException e)
                {
                    LogService.Instance.Error("请求未完成且有截止时间的分类及计数失败", e);
                    return new List<TaskCategoryWithCount>();
                }
            }

            /// <summary>
            /// 根据Key获取分类详情
            /// </summary>
            /// <param name="key">分类Key</param>
            /// <param name="userId">用户ID</param>
            /// <returns>分类对象</returns>
            public TaskCategory GetTaskCategoryByKey(string key, long userId)
            {
                var endpoint = $"{BaseUrl}/categories/detail/{Uri.EscapeDataString(key)}/{userId}";
                return SendGetRequestAllowNullAsync<TaskCategory>(endpoint, "Getting task category by key").Result;
            }

            //     /// <summary>
            //     /// 创建空分类
            //     /// </summary>
            //     /// <param name="name">分类名称</param>
            //     /// <param name="key">分类Key</param>
            //     /// <param name="userId">用户ID</param>
            //     /// <param name="parentCategoryKey">父分类Key，可选</param>
            //     /// <returns>创建的分类对象</returns>
            //     public TaskCategory CreateEmptyTaskCategory(string name, string key, long userId, string parentCategoryKey = null)
            //     {
            //         var category = new TaskCategory(name, key, userId, parentCategoryKey);
            //         var endpoint = $"{BaseUrl}/categories/{userId}";
            //         return SendPostRequestAsync<TaskCategory>(endpoint, "Creating empty task category", category).Result;
            //     }

            //     /// <summary>
            //     /// 获取用户的分类数量
            //     /// </summary>
            //     /// <param name="userId">用户ID</param>
            //     /// <returns>分类数量</returns>
            //     public long GetTaskCategoryCount(long userId)
            //     {
            //         var endpoint = $"{BaseUrl}/categories/count/{userId}";
            //         return SendGetRequestAsync<long>(endpoint, "Getting task category count").Result;
            //     }

            //     /// <summary>
            //     /// 检查指定Key的分类是否存在
            //     /// </summary>
            //     /// <param name="key">分类Key</param>
            //     /// <param name="userId">用户ID</param>
            //     /// <returns>是否存在</returns>
            //     public bool HasTaskCategoryByKey(string key, long userId)
            //     {
            //         var endpoint = $"{BaseUrl}/categories/exists/{Uri.EscapeDataString(key)}/{userId}";
            //         return SendGetRequestAsync<bool>(endpoint, "Checking task category existence").Result;
            //     }

            //     /// <summary>
            //     /// 删除分类
            //     /// </summary>
            //     /// <param name="taskCategory">要删除的分类</param>
            //     /// <param name="userId">用户ID</param>
            //     public void RemoveTaskCategory(TaskCategory taskCategory, long userId)
            //     {
            //         var endpoint = $"{BaseUrl}/categories/{userId}";
            //         SendDeleteRequestAsync(endpoint, "Removing task category", taskCategory).Wait();
            //     }

            //     /// <summary>
            //     /// 保存分类的更改
            //     /// </summary>
            //     /// <param name="taskCategory">已修改的分类</param>
            //     /// <param name="userId">用户ID</param>
            //     /// <returns>更新后的分类对象</returns>
            //     public TaskCategory SaveTaskCategoryChange(TaskCategory taskCategory, long userId)
            //     {
            //         var endpoint = $"{BaseUrl}/categories/{userId}";
            //         return SendPutRequestAsync<TaskCategory>(endpoint, "Saving task category changes", taskCategory).Result;
            //     }

            //     /// <summary>
            //     /// 获取分类下未完成任务的数量
            //     /// </summary>
            //     /// <param name="key">分类Key</param>
            //     /// <param name="userId">用户ID</param>
            //     /// <returns>未完成任务数量</returns>
            //     public long GetTaskCategoryUnCompleteTaskCount(string key, long userId)
            //     {
            //         var endpoint = $"{BaseUrl}/categories/{Uri.EscapeDataString(key)}/uncomplete/count/{userId}";
            //         return SendGetRequestAsync<long>(endpoint, "Getting uncomplete task count for category").Result;
            //     }

            //     /// <summary>
            //     /// 获取分类下已完成任务的数量
            //     /// </summary>
            //     /// <param name="key">分类Key</param>
            //     /// <param name="userId">用户ID</param>
            //     /// <returns>已完成任务数量</returns>
            //     public long GetTaskCategoryCompleteTaskCount(string key, long userId)

            /// <summary>
            /// 获取分类中未完成任务的数量
            /// </summary>
            /// <param name="categoryKey">分类的唯一标识</param>
            /// <param name="userId">用户ID</param>
            /// <returns>未完成任务的数量</returns>
            public int GetCategoryUncompleteTaskCount(string categoryKey, long userId)
            {
                var endpoint = $"{BaseUrl}/categories/{Uri.EscapeDataString(categoryKey)}/uncomplete-count/{userId}";
               return 
                     SendGetRequestAsync<CountResponse>(endpoint, "Getting uncomplete task count for category").Result.Count;
              
            }

            /// <summary>
            /// 获取分类中已完成任务的数量
            /// </summary>
            /// <param name="categoryKey">分类的唯一标识</param>
            /// <param name="userId">用户ID</param>
            /// <returns>已完成任务的数量</returns>
            public int GetCategoryCompleteTaskCount(string categoryKey, long userId)
            {
                var endpoint = $"{BaseUrl}/categories/{Uri.EscapeDataString(categoryKey)}/complete-count/{userId}";
                var response =
                     SendGetRequestAsync<CountResponse>(endpoint, "Getting complete task count for category").Result;
                return response.Count;
            }

            /// <summary>
            /// Repositions categories under a user, optionally to a new parent.
            /// </summary>
            /// <param name="userId">The ID of the user.</param>
            /// <param name="itemsToReposition">A list of categories to reposition, with their new positions.</param>
            /// <param name="newParentKey">Optional. The key of the new parent category. Null or empty if repositioning at root or within the same parent.</param>
            /// <param name="previousParentKey">Optional. The key of the previous parent category.</param>
            /// <returns>A response object indicating success or failure.</returns>
            public async Task<RepositionCategoriesResponse> RepositionCategoriesAsync(long userId,
                List<RepositionCategoryItem> itemsToReposition, string newParentKey = null, string previousParentKey = null)
            {
                var endpoint = $"{BaseUrl}/categories/reposition/{userId}";
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(newParentKey))
                {
                    queryParams.Add($"newParentKey={Uri.EscapeDataString(newParentKey)}");
                }
                if (!string.IsNullOrEmpty(previousParentKey))
                {
                    queryParams.Add($"previousParentKey={Uri.EscapeDataString(previousParentKey)}");
                }
                if (queryParams.Count > 0)
                {
                    endpoint += $"?{string.Join("&", queryParams)}";
                }

                var requestBody = new RepositionCategoriesRequest { Items = itemsToReposition };

                // Assuming SendPostRequestAsync<TResponse>(string endpoint, string operationDescription, TRequestData requestData)
                return await SendPostRequestAsync<RepositionCategoriesResponse>(endpoint, "Repositioning categories",
                    requestBody);
            }
        }
    }
}