using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Data;
using System;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.Models;

namespace HddtodoUI.Converters
{
    /// <summary>
    /// 转换器用于TreeView节点的图标显示
    /// 如果节点没有子项，则显示默认图标
    /// </summary>
    public class TreeNodeGlyphConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is Boolean tcvo && parameter is string mode && mode == "default")
            {
                return tcvo? Visibility.Collapsed : Visibility.Visible;
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
