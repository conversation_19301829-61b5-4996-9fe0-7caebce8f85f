using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Windows.System;
using HddtodoUI.Services;
using HddtodoUI.Utilities;

namespace HddtodoUI.Controls
{
    public sealed partial class CompletedTaskListsDialog : UserControl
    {
        private ObservableCollection<TaskCategoryViewObject> _completedTaskLists;
        private int _currentPage = 0;
        private int _pageSize = 10;
        private long _totalItems = 0;
        private string _filterText = "";

        public event EventHandler DialogClosed;
        public event EventHandler<TaskCategoryViewObject> TaskCategorySetIncomplete;

        public CompletedTaskListsDialog()
        {
            this.InitializeComponent();
            _completedTaskLists = new ObservableCollection<TaskCategoryViewObject>();
            CompletedTaskListsView.ItemsSource = _completedTaskLists;
            LoadCompletedTaskLists();
        }

        private void LoadCompletedTaskLists()
        {
            try
            {
                _completedTaskLists.Clear();
                
                // 获取总数
                _totalItems = StoreFactoryHolder.getTaskCategoryStore().GetCompletedTaskCategoriesAsync(UserInfoHolder.getUserId()).Count;
                
                // 获取当前页的数据
                var taskLists = StoreFactoryHolder.getTaskCategoryStore().GetCompletedTaskCategoriesAsync(
                    UserInfoHolder.getUserId(), null,_filterText,
                    _currentPage, 
                    _pageSize);

          

                // 添加到集合
                foreach (var taskList in taskLists)
                {
                    _completedTaskLists.Add(TaskCategoryViewObject.GetFrom(taskList));
                }

                // 更新分页信息
                UpdatePagingInfo();
            }
            catch (Exception ex)
            {
                // 处理异常
                LogService.Instance.Debug($"加载已完成任务列表出错: {ex.Message}");
            }
        }

        private void UpdatePagingInfo()
        {
            int totalPages = (int)Math.Ceiling((double)_totalItems / _pageSize);
            PageInfoTextBlock.Text = $"第 {_currentPage+1} 页 / 共 {totalPages} 页";
            
            // 更新按钮状态
            PreviousPageButton.IsEnabled = _currentPage > 0;
            NextPageButton.IsEnabled = _currentPage < totalPages-1;
        }

        private void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            _filterText = FilterTextBox.Text.Trim();
            _currentPage = 0; // 重置到第一页
            LoadCompletedTaskLists();
        }

        private void FilterTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                FilterButton_Click(sender, e);
            }
        }

        private void PreviousPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 0)
            {
                _currentPage--;
                LoadCompletedTaskLists();
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            int totalPages = (int)Math.Ceiling((double)_totalItems / _pageSize);
            if (_currentPage < totalPages-1)
            {
                _currentPage++;
                LoadCompletedTaskLists();
            }
        }

        private void SetIncompleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null && button.Tag is TaskCategoryViewObject taskCategory)
            {
                try
                {
                    // 从列表中移除
                    _completedTaskLists.Remove(taskCategory);
                    
                    // 触发事件
                    TaskCategorySetIncomplete?.Invoke(this, taskCategory);
                    
                    // 重新加载数据
                    //LoadCompletedTaskLists();
                    
                    
                }
                catch (Exception ex)
                {
                    LogService.Instance.Debug($"设置任务列表为未完成时出错: {ex.Message}");
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }
    }
}
