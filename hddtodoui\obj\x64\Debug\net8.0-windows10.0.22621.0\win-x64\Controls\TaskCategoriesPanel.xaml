﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TaskCategoriesPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:models="using:HddtodoUI.Models"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter"/>
        <converters:DueDateColorConverter x:Key="DueDateColorConverter"/>
        <converters:InboxVisibilityConverter x:Key="InboxVisibilityConverter"/>
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
    </UserControl.Resources>

    <Grid Background="{ThemeResource SidebarBackgroundBrush}" Padding="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- System Lists -->
            <RowDefinition Height="Auto"/> <!-- Separator -->
            <RowDefinition Height="*"/> <!-- User Lists -->
            <RowDefinition Height="Auto"/> <!-- Separator -->
            <RowDefinition Height="Auto"/> <!-- Add List Button -->
        </Grid.RowDefinitions>

        <!-- System Lists -->
        <ListView x:ConnectionId='2' x:Name="SystemTasksListView" SelectionMode="Single"                                                  >
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ListViewItem Style="{StaticResource TaskListItemStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <FontIcon Grid.Column="0" Glyph="{Binding IconName}" FontSize="18"></FontIcon>
                            <TextBlock Grid.Column="1" Text="{Binding Name}" Style="{StaticResource BodyTextStyle}" TextWrapping="Wrap" />
                            <TextBlock Grid.Column="2" Text="{Binding TaskCount}" Style="{StaticResource CaptionTextStyle}" Margin="12,0,0,0"/>
                        </Grid>
                    </ListViewItem>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <StackPanel Grid.Row="1" Margin="0,10,0,10">
            <FontIcon x:ConnectionId='20' Glyph="&#xE70D;" FontSize="8" Name="TopHint" Visibility="Collapsed"></FontIcon>
        </StackPanel>

        <!-- User Lists -->
        <ScrollViewer x:ConnectionId='3' Grid.Row="2" Name="UserListsScrollViewer"                                                                             VerticalScrollBarVisibility="Hidden">
            <TreeView x:ConnectionId='11' x:Name="UserTasksTreeView" 
                      SelectionMode="Single"                                                        
                                                                                                                                           >
                <TreeView.ItemTemplate>
                    <DataTemplate                                       >
                        <TreeViewItem x:ConnectionId='13' IsExpanded="True">
                            <TreeViewItem.ContextFlyout>
                                <MenuFlyout>
                                    <MenuFlyoutItem x:ConnectionId='14' Text="添加新任务" Icon="Add"                               />
                                    <MenuFlyoutItem x:ConnectionId='15' Text="编辑标题" Icon="Edit"                                
                                                  Visibility="{Binding Name, Converter={StaticResource InboxVisibilityConverter}}" />
                                    <MenuFlyoutItem x:ConnectionId='16' Text="完成该任务列表" Icon="Delete"                                       
                                                  Visibility="{Binding Name, Converter={StaticResource InboxVisibilityConverter}}" />
                                </MenuFlyout>
                            </TreeViewItem.ContextFlyout>
                            <Grid MinHeight="25" >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock x:ConnectionId='18'                      TextWrapping="Wrap"
                                                  Style="{StaticResource BodyTextStyle}"/>
                                        <TextBlock x:ConnectionId='19' Grid.Column="1"
                                                                           
                                                  Style="{StaticResource CaptionTextStyle}"
                                                  Margin="12,0,0,0" Padding="0,0,5,0"/>
                                    </Grid>
                                    <TextBlock x:ConnectionId='17'                                                                      
                                              Style="{StaticResource CaptionTextStyle}" 
                                                                                                                                  
                                                                                                                              
                                              FontSize="11" Margin="0,2,0,4"/>
                                </StackPanel>
                            </Grid>
                        </TreeViewItem>
                    </DataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </ScrollViewer>

        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <FontIcon x:ConnectionId='10' Glyph="&#xE70D;" FontSize="8" Name="BottomHint" Visibility="Collapsed"></FontIcon>
        </StackPanel>

        <!-- Add List Button -->
        <Grid Grid.Row="4" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button x:ConnectionId='8' Content="+ 列表"
                    Style="{StaticResource SecondaryButtonStyle}"
                    HorizontalAlignment="Center"
                                               />
            
            <Button x:ConnectionId='9' Grid.Column="1"
                    x:Name="ManageCompletedListsButton"
                    ToolTipService.ToolTip="管理已完成的任务列表"
                                                            
                    Style="{StaticResource SecondaryButtonStyle}"
                    Margin="8,0,0,0">
                <FontIcon Glyph="&#xE930;" FontSize="16"/>
            </Button>
        </Grid>

        <!-- Add List Dialog -->
        <ContentDialog x:ConnectionId='4' x:Name="AddListDialog"
                      Title="添加新列表"
                      PrimaryButtonText="确定"
                      SecondaryButtonText="取消"
                      DefaultButton="Primary"
                                                                                       
                                                                           
                                                                       >
            <StackPanel Spacing="16">
                <TextBox x:ConnectionId='5' x:Name="ListNameTextBox"
                        Header="列表名称"
                        PlaceholderText="请输入列表名称"
                                                                 />
                
                <ComboBox x:ConnectionId='6' x:Name="ListPriorityComboBox"
                          Header="优先级"
                          PlaceholderText="选择优先级"
                          SelectedIndex="1">
                    <ComboBoxItem Content="低"/>
                    <ComboBoxItem Content="中"/>
                    <ComboBoxItem Content="高"/>
                </ComboBox>
                
                <CalendarDatePicker x:ConnectionId='7' x:Name="ListDueDatePicker"
                                  Header="到期时间"
                                  PlaceholderText="选择到期时间"/>
            </StackPanel>
        </ContentDialog>
    </Grid>
</UserControl>

