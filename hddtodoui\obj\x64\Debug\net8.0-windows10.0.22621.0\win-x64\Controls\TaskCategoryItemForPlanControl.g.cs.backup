﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TaskCategoryItemForPlanControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4FEE4561DB455AAEAF6EED36966A4923F76D65F7185FB6FB15EA747C1EFB0817"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskCategoryItemForPlanControl : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_UIElement_Visibility(global::Microsoft.UI.Xaml.UIElement obj, global::Microsoft.UI.Xaml.Visibility value)
            {
                obj.Visibility = value;
            }
            public static void Set_Microsoft_UI_Xaml_Documents_Run_Text(global::Microsoft.UI.Xaml.Documents.Run obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskCategoryItemForPlanControl_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskCategoryItemForPlanControl_Bindings
        {
            private global::HddtodoUI.Controls.TaskCategoryItemForPlanControl dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private global::Microsoft.UI.Xaml.ResourceDictionary localResources;
            private global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement> converterLookupRoot;

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj5;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj6;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj7;
            private global::Microsoft.UI.Xaml.Documents.Run obj8;
            private global::Microsoft.UI.Xaml.Documents.Run obj9;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj5TextDisabled = false;
            private static bool isobj6TextDisabled = false;
            private static bool isobj7VisibilityDisabled = false;
            private static bool isobj8TextDisabled = false;
            private static bool isobj9TextDisabled = false;

            private TaskCategoryItemForPlanControl_obj1_BindingsTracking bindingsTracking;

            public TaskCategoryItemForPlanControl_obj1_Bindings()
            {
                this.bindingsTracking = new TaskCategoryItemForPlanControl_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 68 && columnNumber == 20)
                {
                    isobj5TextDisabled = true;
                }
                else if (lineNumber == 41 && columnNumber == 12)
                {
                    isobj6TextDisabled = true;
                }
                else if (lineNumber == 62 && columnNumber == 24)
                {
                    isobj7VisibilityDisabled = true;
                }
                else if (lineNumber == 63 && columnNumber == 22)
                {
                    isobj8TextDisabled = true;
                }
                else if (lineNumber == 55 && columnNumber == 22)
                {
                    isobj9TextDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 5: // Controls\TaskCategoryItemForPlanControl.xaml line 67
                        this.obj5 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 6: // Controls\TaskCategoryItemForPlanControl.xaml line 39
                        this.obj6 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 7: // Controls\TaskCategoryItemForPlanControl.xaml line 57
                        this.obj7 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 8: // Controls\TaskCategoryItemForPlanControl.xaml line 63
                        this.obj8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Documents.Run>(target);
                        break;
                    case 9: // Controls\TaskCategoryItemForPlanControl.xaml line 55
                        this.obj9 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Documents.Run>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITaskCategoryItemForPlanControl_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoryItemForPlanControl>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }
            public void SetConverterLookupRoot(global::Microsoft.UI.Xaml.FrameworkElement rootElement)
            {
                this.converterLookupRoot = new global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement>(rootElement);
            }

            public global::Microsoft.UI.Xaml.Data.IValueConverter LookupConverter(string key)
            {
                if (this.localResources == null)
                {
                    global::Microsoft.UI.Xaml.FrameworkElement rootElement;
                    this.converterLookupRoot.TryGetTarget(out rootElement);
                    this.localResources = rootElement.Resources;
                    this.converterLookupRoot = null;
                }
                return (global::Microsoft.UI.Xaml.Data.IValueConverter) (this.localResources.ContainsKey(key) ? this.localResources[key] : global::Microsoft.UI.Xaml.Application.Current.Resources[key]);
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_(obj);
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_Category(obj.Category, phase);
                        this.Update_CategoryCount(obj.CategoryCount, phase);
                        this.Update_TaskCount(obj.TaskCount, phase);
                    }
                }
            }
            private void Update_Category(global::HddtodoUI.BackendModels.TaskCategory obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_Category_CategoryDueTime(obj.CategoryDueTime, phase);
                        this.Update_Category_Name(obj.Name, phase);
                    }
                }
            }
            private void Update_Category_CategoryDueTime(global::System.Nullable<global::System.DateTime> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoryItemForPlanControl.xaml line 67
                    if (!isobj5TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj5, (global::System.String)this.LookupConverter("DateTimeConverter").Convert(obj, typeof(global::System.String), null, null), null);
                    }
                }
            }
            private void Update_Category_Name(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoryItemForPlanControl.xaml line 39
                    if (!isobj6TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj6, obj, null);
                    }
                }
            }
            private void Update_CategoryCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoryItemForPlanControl.xaml line 63
                    if (!isobj8TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Documents_Run_Text(this.obj8, obj.ToString(), null);
                    }
                }
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskCategoryItemForPlanControl.xaml line 57
                    if (!isobj7VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj7, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("IntToVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), null, null));
                    }
                }
            }
            private void Update_TaskCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoryItemForPlanControl.xaml line 55
                    if (!isobj9TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Documents_Run_Text(this.obj9, obj.ToString(), null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskCategoryItemForPlanControl_obj1_BindingsTracking
            {
                private global::System.WeakReference<TaskCategoryItemForPlanControl_obj1_Bindings> weakRefToBindingObj; 

                public TaskCategoryItemForPlanControl_obj1_BindingsTracking(TaskCategoryItemForPlanControl_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskCategoryItemForPlanControl_obj1_Bindings>(obj);
                }

                public TaskCategoryItemForPlanControl_obj1_Bindings TryGetBindingObject()
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_(null);
                }

                public void PropertyChanged_(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj = sender as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                            if (obj != null)
                            {
                                bindings.Update_Category(obj.Category, DATA_CHANGED);
                                bindings.Update_CategoryCount(obj.CategoryCount, DATA_CHANGED);
                                bindings.Update_TaskCount(obj.TaskCount, DATA_CHANGED);
                            }
                        }
                        else
                        {
                            switch (propName)
                            {
                                case "Category":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_Category(obj.Category, DATA_CHANGED);
                                    }
                                    break;
                                }
                                case "CategoryCount":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_CategoryCount(obj.CategoryCount, DATA_CHANGED);
                                    }
                                    break;
                                }
                                case "TaskCount":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_TaskCount(obj.TaskCount, DATA_CHANGED);
                                    }
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void DependencyPropertyChanged_Category(global::Microsoft.UI.Xaml.DependencyObject sender, global::Microsoft.UI.Xaml.DependencyProperty prop)
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj = sender as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl;
                        if (obj != null)
                        {
                            bindings.Update_Category(obj.Category, DATA_CHANGED);
                        }
                    }
                }
                public void DependencyPropertyChanged_CategoryCount(global::Microsoft.UI.Xaml.DependencyObject sender, global::Microsoft.UI.Xaml.DependencyProperty prop)
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj = sender as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl;
                        if (obj != null)
                        {
                            bindings.Update_CategoryCount(obj.CategoryCount, DATA_CHANGED);
                        }
                    }
                }
                public void DependencyPropertyChanged_TaskCount(global::Microsoft.UI.Xaml.DependencyObject sender, global::Microsoft.UI.Xaml.DependencyProperty prop)
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj = sender as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl;
                        if (obj != null)
                        {
                            bindings.Update_TaskCount(obj.TaskCount, DATA_CHANGED);
                        }
                    }
                }
                private long tokenDPC_Category = 0;
                private long tokenDPC_CategoryCount = 0;
                private long tokenDPC_TaskCount = 0;
                public void UpdateChildListeners_(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj)
                {
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        if (bindings.dataRoot != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)bindings.dataRoot).PropertyChanged -= PropertyChanged_;
                            bindings.dataRoot.UnregisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.CategoryProperty, tokenDPC_Category);
                            bindings.dataRoot.UnregisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.CategoryCountProperty, tokenDPC_CategoryCount);
                            bindings.dataRoot.UnregisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.TaskCountProperty, tokenDPC_TaskCount);
                        }
                        if (obj != null)
                        {
                            bindings.dataRoot = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_;
                            tokenDPC_Category = obj.RegisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.CategoryProperty, DependencyPropertyChanged_Category);
                            tokenDPC_CategoryCount = obj.RegisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.CategoryCountProperty, DependencyPropertyChanged_CategoryCount);
                            tokenDPC_TaskCount = obj.RegisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl.TaskCountProperty, DependencyPropertyChanged_TaskCount);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 1: // Controls\TaskCategoryItemForPlanControl.xaml line 3
                {
                    this.root = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.UserControl>(target);
                }
                break;
            case 2: // Controls\TaskCategoryItemForPlanControl.xaml line 25
                {
                    this.RootGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Grid)this.RootGrid).Tapped += this.RootGrid_Tapped;
                }
                break;
            case 3: // Controls\TaskCategoryItemForPlanControl.xaml line 29
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element3 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element3).Click += this.ChangeDueTimeMenuItem_Click;
                }
                break;
            case 4: // Controls\TaskCategoryItemForPlanControl.xaml line 30
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element4 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element4).Click += this.CompleteProjectMenuItem_Click;
                }
                break;
            case 6: // Controls\TaskCategoryItemForPlanControl.xaml line 39
                {
                    this.CategoryNameTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TaskCategoryItemForPlanControl.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TaskCategoryItemForPlanControl_obj1_Bindings bindings = new TaskCategoryItemForPlanControl_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    bindings.SetConverterLookupRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

