using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Linq;
using HddtodoUI.Services;
using HddtodoUI.BackendModels.JsonConverters;


namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpStoreBase
    {
        public static string baseUrl = GetSettingValue("baseurl");
        //public static string baseUrl = "http://192.168.8.66:9300/api";
        
        private static string GetSettingValue(string paramName)
        {
            return String.Format(ConfigurationManager.AppSettings[paramName]);
        }

        protected readonly HttpClient _httpClient;
        protected readonly JsonSerializerOptions _jsonOptions;

        protected HttpStoreBase(HttpClient httpClient = null, JsonSerializerOptions jsonOptions = null)
        {
            _httpClient = httpClient ?? new HttpClient();
            _jsonOptions = jsonOptions ?? new JsonSerializerOptions();
            // 处理循环引用
            _jsonOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            
            // 确保默认选项已设置
            _jsonOptions.PropertyNameCaseInsensitive = true;
            _jsonOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            
            // 确保转换器已添加
            if (!_jsonOptions.Converters.Any(c => c is DateTimeJsonConverter))
            {
                _jsonOptions.Converters.Add(new DateTimeJsonConverter());
            }
            if (!_jsonOptions.Converters.Any(c => c is NullableDateTimeJsonConverter))
            {
                _jsonOptions.Converters.Add(new NullableDateTimeJsonConverter());
            }
        }

        protected async Task<T> SendGetRequestAsync<T>(string endpoint, string operationName, IDictionary<string, string> headers = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds))
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    if (headers != null && headers.Any())
                    {
                        LogService.Instance.Info($"Headers: {JsonSerializer.Serialize(headers)}");
                    }

                    using (var requestMessage = new HttpRequestMessage(HttpMethod.Get, endpoint))
                    {
                        if (headers != null)
                        {
                            foreach (var header in headers)
                            {
                                requestMessage.Headers.Add(header.Key, header.Value);
                            }
                        }

                        var response = await _httpClient.SendAsync(requestMessage, cts.Token).ConfigureAwait(false);
                        response.EnsureSuccessStatusCode();

                        var raw1 = await response.Content.ReadAsStringAsync(cts.Token).ConfigureAwait(false);
                        
                        T result;
                        try
                        {
                            result = JsonSerializer.Deserialize<T>(raw1, _jsonOptions);
                        }
                        catch (JsonException jsonEx)
                        {
                            // 读取原始字符串并记录，便于排查序列化问题
                            var raw = await response.Content.ReadAsStringAsync(cts.Token).ConfigureAwait(false);
                            LogService.Instance.Error($"JSON 反序列化失败，原始内容: {raw}", jsonEx);
                            throw;
                        }
                        stopwatch.Stop();
                        LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                        return result;
                    }
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }
        
        protected async Task<T> SendGetRequestAllowNullAsync<T>(string endpoint, string operationName, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds))
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    var response = await _httpClient.GetAsync(endpoint, cts.Token).ConfigureAwait(false);
                    
                    // 如果响应不是成功状态码（200-299），记录警告并返回null
                    if (!response.IsSuccessStatusCode)
                    {
                        stopwatch.Stop();
                        LogService.Instance.Warn($"{operationName.ToLower()} returned status code {response.StatusCode} (took {stopwatch.ElapsedMilliseconds}ms)");
                        return default(T);
                    }

                    var result = await response.Content.ReadFromJsonAsync<T>(_jsonOptions, cts.Token).ConfigureAwait(false);
                    stopwatch.Stop();
                    LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                    return result;
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                } catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
                
            }
        }

        protected async Task SendPutRequestAsync(string endpoint, string operationName, object content = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds)) // Timeout after specified milliseconds
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    HttpResponseMessage response;
                    
                    if (content != null)
                    {
                        // 将内容序列化为JSON字符串
                        var jsonString = JsonSerializer.Serialize(content, _jsonOptions);
                        // 记录发送的JSON到日志
                        LogService.Instance.Info($"Request JSON: {jsonString}");
                        
                        var jsonContent = new StringContent(
                            jsonString,
                            Encoding.UTF8,
                            "application/json"
                        );
                        response = await _httpClient.PutAsync(endpoint, jsonContent, cts.Token).ConfigureAwait(false);
                    }
                    else
                    {
                        response = await _httpClient.PutAsync(endpoint, null, cts.Token).ConfigureAwait(false);
                    }
                    
                    response.EnsureSuccessStatusCode();
                    stopwatch.Stop();
                    LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }

        protected async Task<T> SendPutRequestAsync<T>(string endpoint, string operationName, object content = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds)) // Timeout after specified milliseconds
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    HttpResponseMessage response;
                    
                    if (content != null)
                    {
                        // 将内容序列化为JSON字符串
                        var jsonString = JsonSerializer.Serialize(content, _jsonOptions);
                        // 记录发送的JSON到日志
                        LogService.Instance.Info($"Request JSON: {jsonString}");
                        
                        var jsonContent = new StringContent(
                            jsonString,
                            Encoding.UTF8,
                            "application/json"
                        );
                        response = await _httpClient.PutAsync(endpoint, jsonContent, cts.Token).ConfigureAwait(false);
                    }
                    else
                    {
                        response = await _httpClient.PutAsync(endpoint, null, cts.Token).ConfigureAwait(false);
                    }
                    
                    response.EnsureSuccessStatusCode();
                    var result = await response.Content.ReadFromJsonAsync<T>(_jsonOptions, cts.Token).ConfigureAwait(false);
                    stopwatch.Stop();
                    LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                    return result;
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }

        protected async Task SendPostRequestAsync(string endpoint, string operationName, object content = null, IDictionary<string, string> headers = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds))
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    if (headers != null && headers.Any())
                    {
                        LogService.Instance.Info($"Headers: {JsonSerializer.Serialize(headers)}");
                    }

                    using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, endpoint))
                    {
                        if (headers != null)
                        {
                            foreach (var header in headers)
                            {
                                requestMessage.Headers.Add(header.Key, header.Value);
                            }
                        }

                        if (content != null)
                        {
                            var jsonString = JsonSerializer.Serialize(content, _jsonOptions);
                            LogService.Instance.Info($"Request JSON: {jsonString}");
                            requestMessage.Content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                        }

                        var response = await _httpClient.SendAsync(requestMessage, cts.Token).ConfigureAwait(false);
                        response.EnsureSuccessStatusCode();
                        stopwatch.Stop();
                        LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                    }
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }

        protected async Task<T> SendPostRequestAsync<T>(string endpoint, string operationName, object content = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds)) // Timeout after specified milliseconds
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    HttpResponseMessage response;
                    
                    if (content != null)
                    {
                        // 记录序列化后的 JSON 内容
                        var jsonString = JsonSerializer.Serialize(content, _jsonOptions);
                        LogService.Instance.Info($"Request JSON: {jsonString}");
                        
                        response = await _httpClient.PostAsJsonAsync(endpoint, content, _jsonOptions, cts.Token).ConfigureAwait(false);
                    }
                    else
                    {
                        response = await _httpClient.PostAsync(endpoint, null, cts.Token).ConfigureAwait(false);
                    }
                    
                    response.EnsureSuccessStatusCode();
                    var result = await response.Content.ReadFromJsonAsync<T>(_jsonOptions, cts.Token).ConfigureAwait(false);
                    stopwatch.Stop();
                    LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                    return result;
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }

        protected async Task SendDeleteRequestAsync(string endpoint, string operationName, object content = null, int timeoutMilliseconds = 5000)
        {
            var stopwatch = Stopwatch.StartNew();
            using (var cts = new CancellationTokenSource(timeoutMilliseconds)) // Timeout after specified milliseconds
            {
                try
                {
                    LogService.Instance.Info($"{operationName}: {endpoint}");
                    HttpResponseMessage response;

                    if (content != null)
                    {
                        var request = new HttpRequestMessage(HttpMethod.Delete, endpoint)
                        {
                            Content = JsonContent.Create(content, options: _jsonOptions)
                        };
                        response = await _httpClient.SendAsync(request, cts.Token).ConfigureAwait(false);
                    }
                    else
                    {
                        response = await _httpClient.DeleteAsync(endpoint, cts.Token).ConfigureAwait(false);
                    }

                    response.EnsureSuccessStatusCode();
                    stopwatch.Stop();
                    LogService.Instance.Info($"Successfully completed {operationName.ToLower()} in {stopwatch.ElapsedMilliseconds}ms");
                }
                catch (OperationCanceledException ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Timeout in {operationName.ToLower()}: Operation canceled after {timeoutMilliseconds}ms (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw new TimeoutException($"The {operationName.ToLower()} operation timed out after {timeoutMilliseconds}ms.", ex);
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    LogService.Instance.Error($"Error in {operationName.ToLower()}: {ex.Message} (took {stopwatch.ElapsedMilliseconds}ms)", ex);
                    throw;
                }
            }
        }
    }
}