using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Windows.Foundation;
using HddtodoUI.Models;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Media;


namespace HddtodoUI.Controls;

public partial class TaskCategoriesTreeViewPanel
{
    public void RemoveUserCategory(string key)
    {
        UserCategories.Select(t => t).Where(t => t.Key == key).ToList().ForEach(t => UserCategories.Remove(t));
    }


    // 在整棵树中递归查找分类
    private TaskCategoryViewObject FindCategoryInTree(string categoryKey, IList<TaskCategoryViewObject> categories = null)
        {
            if (categories == null)
            {
                categories = UserCategories;
            }

            foreach (var category in categories)
            {
                if (category.Key == categoryKey)
                {
                    return category;
                }

                // 递归查找子分类
                var foundInChildren = FindCategoryInTree(categoryKey, category.Children);
                if (foundInChildren != null)
                {
                    return foundInChildren;
                }
            }

            return null;
        }

    public bool UpdateUserCategory(string categoryKey, TaskCategoryViewObject updatedCategory)
    {
        return UpdateCategoryInTree(categoryKey, updatedCategory);
    }
    
    // 在整棵树中递归更新分类
    private bool UpdateCategoryInTree(string categoryKey, TaskCategoryViewObject updatedCategory, IList<TaskCategoryViewObject> categories = null)
        {
            if (categories == null)
            {
                categories = UserCategories;
            }

            for (int i = 0; i < categories.Count; i++)
            {
                if (categories[i].Key == categoryKey)
                {
                    // 更新节点属性，不替换实例，保持子节点展开状态
                    var existing = categories[i];
                    existing.Name = updatedCategory.Name;
                    existing.TaskCount = updatedCategory.TaskCount;
                    existing.SubCategoryCount = updatedCategory.SubCategoryCount;
                    existing.IconName = updatedCategory.IconName;
                    existing.DueDate = updatedCategory.DueDate;
                    existing.Priority = updatedCategory.Priority;
                    existing.CategoryOrder = updatedCategory.CategoryOrder;
                    existing.IsDeleted = updatedCategory.IsDeleted;
                    // 如需更新更多字段，可在此添加
                    return true;
                }

                // 递归查找子分类
                if (UpdateCategoryInTree(categoryKey, updatedCategory, categories[i].Children))
                {
                    return true;
                }
            }

            return false;
        }

    private ObservableCollection<TaskCategoryViewObject> FindParentCollection(TaskCategoryViewObject category)
        {
            if (category == null) return null;
            
            // 先检查根集合
            if (UserCategories.Any(c => c.Key == category.Key))
            {
                return UserCategories;
            }

            // 递归检查子集合
            foreach (var item in UserCategories)
            {
                var found = FindInChildren(item, category);
                if (found != null)
                {
                    return item.Children;
                }
            }

            return null;
        }

    // 递归辅助方法：在子节点中查找分类
    private TaskCategoryViewObject FindInChildren(TaskCategoryViewObject parent, TaskCategoryViewObject target)
        {
            if (parent == null || target == null || parent.Children == null)
            {
                return null;
            }

            // 检查当前节点的子节点中是否包含目标节点
            var found = parent.Children.FirstOrDefault(c => c.Key == target.Key);
            if (found != null)
            {
                return found;
            }

            // 递归检查子节点的子节点
            foreach (var child in parent.Children)
            {
                found = FindInChildren(child, target);
                if (found != null)
                {
                    return found;
                }
            }

            return null;
        }
}