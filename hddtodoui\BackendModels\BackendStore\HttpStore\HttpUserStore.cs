using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using HddtodoUI.BackendModels.JsonConverters;


namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpUserStore : HttpStoreBase, IUserStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;

        public HttpUserStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter()
            }
        })
        {
        }

        public UserInfo getUserInfoBy(string username, string password)
        {
            var endpoint = $"{BaseUrl}/users/login/{username}/{password}";
            return SendGetRequestAsync<UserInfo>(endpoint, "Getting user info by username and password").Result;
        }

        public UserInfo getUserInfoBy(long userId)
        {
            var endpoint = $"{BaseUrl}/users/{userId}";
            return SendGetRequestAsync<UserInfo>(endpoint, "Getting user info by ID").Result;
        }
    }
}
