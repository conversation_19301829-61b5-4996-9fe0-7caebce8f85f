# URL日期格式测试

## 问题描述
用户报告URL中的日期格式不正确：
```
http://localhost:9300/api/tasks/uncomplete/due/count/1?date=2025-07-03T00:00:00
```

## 修复前的问题
- URL参数中使用硬编码格式 `yyyy-MM-ddTHH:mm:ss`
- 与新的ISO8601WithTimeZone格式不一致
- 导致API调用失败

## 修复后的格式
现在URL将使用统一的ISO 8601格式：
```
http://localhost:9300/api/tasks/uncomplete/due/count/1?date=2025-07-03T00%3A00%3A00.000%2B08%3A00
```

解码后的实际日期格式：
```
2025-07-03T00:00:00.000+08:00
```

## 修复的文件

### 1. HttpTaskStore.cs
- `getAllUnCompleteTaskByDueByDate()` - 使用 `JsonDateTimeStringConverter.Convert()`
- `getAllUnCompleteTaskCountsByDueByDate()` - 使用 `JsonDateTimeStringConverter.Convert()`
- `getAllUnCompleteTaskByDueBetweenDate()` - 使用 `JsonDateTimeStringConverter.Convert()`
- `getAllUnCompleteTaskCountsByDueBetweenDate()` - 使用 `JsonDateTimeStringConverter.Convert()`

### 2. HttpTaskTimeLogStore.cs
- `GetTaskTimeLogs()` - 使用 `JsonDateTimeStringConverter.Convert()`
- `GetTaskTimeLogStatistics()` - 使用 `JsonDateTimeStringConverter.Convert()`

## 测试验证

### 测试用例1: 基本日期
```csharp
var testDate = new DateTime(2025, 7, 3, 0, 0, 0, DateTimeKind.Local);
var result = JsonDateTimeStringConverter.Convert(testDate);
// 期望结果: "2025-07-03T00:00:00.000+08:00" (假设东八区)
```

### 测试用例2: URL编码
```csharp
var testDate = new DateTime(2025, 7, 3, 0, 0, 0, DateTimeKind.Local);
var dateString = JsonDateTimeStringConverter.Convert(testDate);
var encodedDate = Uri.EscapeDataString(dateString);
// 期望结果: "2025-07-03T00%3A00%3A00.000%2B08%3A00"
```

### 测试用例3: 完整URL
```csharp
var testDate = new DateTime(2025, 7, 3, 0, 0, 0, DateTimeKind.Local);
var dateString = JsonDateTimeStringConverter.Convert(testDate);
var url = $"http://localhost:9300/api/tasks/uncomplete/due/count/1?date={Uri.EscapeDataString(dateString)}";
// 期望结果: "http://localhost:9300/api/tasks/uncomplete/due/count/1?date=2025-07-03T00%3A00%3A00.000%2B08%3A00"
```

## 优势

1. **统一格式**: 所有API调用使用相同的日期格式
2. **时区支持**: 包含完整的时区信息
3. **URL安全**: 自动进行URL编码
4. **向后兼容**: 后端仍能解析旧格式
5. **单点管理**: 格式变更只需修改一个地方

## 注意事项

1. **URL编码**: 必须使用 `Uri.EscapeDataString()` 对日期字符串进行编码
2. **时区处理**: DateTime.Kind为Unspecified时自动视为本地时间
3. **后端兼容**: 确保后端服务支持ISO 8601格式解析
4. **测试验证**: 建议在不同时区环境下测试

## 后续步骤

1. 测试修复后的API调用
2. 验证后端服务兼容性
3. 监控错误日志
4. 如有问题，可临时使用环境变量切换格式：
   ```bash
   set HDDTODO_DATETIME_FORMAT=yyyy-MM-dd'T'HH:mm:ss
   ```
