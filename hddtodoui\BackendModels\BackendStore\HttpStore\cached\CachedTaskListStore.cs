using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore.cached
{
    public class CachedTaskListStore : ITaskListStore
    {
        private readonly ITaskListStore _underlyingStore;
        private readonly ConcurrentDictionary<string, CacheEntry<TaskList>> _taskListCache;
        private readonly ConcurrentDictionary<long, CacheEntry<List<TaskList>>> _uncompleteTaskListsCache;
        private readonly TimeSpan _defaultCacheExpiration = TimeSpan.FromMinutes(5);

        public CachedTaskListStore(ITaskListStore underlyingStore)
        {
            _underlyingStore = underlyingStore;
            _taskListCache = new ConcurrentDictionary<string, CacheEntry<TaskList>>();
            _uncompleteTaskListsCache = new ConcurrentDictionary<long, CacheEntry<List<TaskList>>>();
        }

        private string GetTaskListCacheKey(string key, long userId) => $"{key}_{userId}";

        public List<TaskListWithCount> getUncompleteTaskListsSourceWithCount(long userId)
        {
            var result = _underlyingStore.getUncompleteTaskListsSourceWithCount(userId);
            
            foreach (var item in result)
            {
                var cacheKey = GetTaskListCacheKey(item.TaskList.Key, userId);
                _taskListCache.AddOrUpdate(cacheKey,
                    new CacheEntry<TaskList>(item.TaskList, _defaultCacheExpiration),
                    (_, __) => new CacheEntry<TaskList>(item.TaskList, _defaultCacheExpiration));
            }

            return result;
        }

        public List<TaskList> getUncompleteTaskListsSource(long userId)
        {
            if (_uncompleteTaskListsCache.TryGetValue(userId, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                return cachedEntry.Value;
            }

            var result = _underlyingStore.getUncompleteTaskListsSource(userId);
            _uncompleteTaskListsCache.AddOrUpdate(userId,
                new CacheEntry<List<TaskList>>(result, _defaultCacheExpiration),
                (_, __) => new CacheEntry<List<TaskList>>(result, _defaultCacheExpiration));
            return result;
        }

        public TaskList getTaskListByKey(string key, long userId)
        {
            var cacheKey = GetTaskListCacheKey(key, userId);
            if (_taskListCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                return cachedEntry.Value;
            }

            var result = _underlyingStore.getTaskListByKey(key, userId);
            _taskListCache.AddOrUpdate(cacheKey,
                new CacheEntry<TaskList>(result, _defaultCacheExpiration),
                (_, __) => new CacheEntry<TaskList>(result, _defaultCacheExpiration));
            return result;
        }

        public TaskList createEmptyTaskList(string listName, string key, long userId)
        {
            var result = _underlyingStore.createEmptyTaskList(listName, key, userId);
            InvalidateUserCaches(userId);
            return result;
        }

        public long getTaskListCount(long userId)
        {
            return _underlyingStore.getTaskListCount(userId);
        }

        public bool hasTaskListByKey(string key, long userId)
        {
            return _underlyingStore.hasTaskListByKey(key, userId);
        }

        public void removeTaskList(TaskList taskList, long userId)
        {
            _underlyingStore.removeTaskList(taskList, userId);
            InvalidateUserCaches(userId);
        }

        public TaskList saveTaskListChange(TaskList taskList, long userId)
        {
           var ret = _underlyingStore.saveTaskListChange(taskList, userId);
            InvalidateUserCaches(userId);
            return ret;
        }

        public void InsertTaskListTo(TaskList from, TaskList target, long userId)
        {
            _underlyingStore.InsertTaskListTo(from, target, userId);
            InvalidateUserCaches(userId);
        }

        public long getTaskListUnCompleteCount(string key, long userId)
        {
            return _underlyingStore.getTaskListUnCompleteCount(key, userId);
        }

        public long getTaskListCompleteCount(string key, long userId)
        {
            return _underlyingStore.getTaskListCompleteCount(key, userId);
        }

        public List<TaskList> GetCompletedTaskListsPaged(long userId, int page, int pageSize)
        {
            return _underlyingStore.GetCompletedTaskListsPaged(userId, page, pageSize);
        }

        public long GetCompletedTaskListCount(long userId)
        {
            return _underlyingStore.GetCompletedTaskListCount(userId);
        }

        public Task<List<TaskList>> GetTaskListsByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false)
        {
            // 这里可以实现缓存逻辑，但为简单起见，直接调用底层存储
            return _underlyingStore.GetTaskListsByParentKeyAsync(userId, parentKey, includeDeleted);
        }

        public Task<List<TaskListWithCount>> GetTaskListsWithCountByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false)
        {
            // 这里可以实现缓存逻辑，但为简单起见，直接调用底层存储
            return _underlyingStore.GetTaskListsWithCountByParentKeyAsync(userId, parentKey, includeDeleted);
        }

        private void InvalidateUserCaches(long userId)
        {
            _uncompleteTaskListsCache.TryRemove(userId, out _);
            var keysToRemove = new List<string>();
            foreach (var key in _taskListCache.Keys)
            {
                if (key.EndsWith($"_{userId}"))
                {
                    keysToRemove.Add(key);
                }
            }
            foreach (var key in keysToRemove)
            {
                _taskListCache.TryRemove(key, out _);
            }
        }
    }

    internal class CacheEntry<T>
    {
        public T Value { get; }
        public DateTime ExpirationTime { get; }

        public bool IsExpired => DateTime.Now >= ExpirationTime;

        public CacheEntry(T value, TimeSpan expirationSpan)
        {
            Value = value;
            ExpirationTime = DateTime.Now.Add(expirationSpan);
        }
    }
}
