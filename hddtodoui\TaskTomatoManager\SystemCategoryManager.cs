using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.Utilities;

namespace HddtodoUI.TaskTomatoManager;

public static class SystemCategoryManager
{
    private static DateTime getTomorrowDateOnly()
    {
        return DateTime.Today.AddDays(1).Date;
    }

    private static DateTime getWeekLaterDateOnly()
    {
        return DateTime.Today.AddDays(7).Date;
    }

    private static DateTime getWeekStartDateOnly()
    {
        return DateTime.Today.AddDays(1).Date;
    }

    public static bool IsDateBelongToTodayDueTask(DateTime? date)
    {
        if (date == null) return false;
        return date < getTomorrowDateOnly();
    }

    public static bool IsDateBelongToWeekDueTask(DateTime? date)
    {
        if (date == null) return false;

        return date > getWeekStartDateOnly() && date < getWeekLaterDateOnly();
    }

    public static long getTodayDueTaskUncompleteTaskCount()
    {
        return StoreFactoryHolder.getTaskStore()
            .getAllUnCompleteTaskCountsByDueByDate(getTomorrowDateOnly(), UserInfoHolder.getUserId());
    }

    public static long getWeekDueTaskUncompleteTaskCount()
    {
        return StoreFactoryHolder.getTaskStore().getAllUnCompleteTaskCountsByDueBetweenDate(getWeekStartDateOnly(),
            getWeekLaterDateOnly(), UserInfoHolder.getUserId());
    }
    
    public static long getImportantUncompleteTaskCount()
    {
        return StoreFactoryHolder.getTaskStore().getAllUnCompleteHighPriorityTasksCount( UserInfoHolder.getUserId());
    }

    public static List<TTask> getTodayDueTaskUncompleteTask()
    {
        return StoreFactoryHolder.getTaskStore()
            .getAllUnCompleteTaskByDueByDate(getTomorrowDateOnly(), UserInfoHolder.getUserId());
    }

    public static List<TTask> getWeekDueTaskUncompleteTask()
    {
        return StoreFactoryHolder.getTaskStore().getAllUnCompleteTaskByDueBetweenDate(getWeekStartDateOnly(),
            getWeekLaterDateOnly(), UserInfoHolder.getUserId());
    }
    
    public static List<TTask> getImportantTaskUncompleteTask()
    {
        return StoreFactoryHolder.getTaskStore().getAllUnCompleteHighPriorityTasks(UserInfoHolder.getUserId());
    }

    public static long getDeletedTasksCount()
    {
        return StoreFactoryHolder.getTaskStore().getDeletedTasksCount(UserInfoHolder.getUserId());
    }

    // -----------------------------------------------------------------
    // "计划中的分类（项目）" 相关方法
    // -----------------------------------------------------------------
    /// <summary>
    /// 获取当前用户所有顶级、未完成且设置了 CategoryDueTime 的 TaskCategory 列表。
    /// </summary>
    /// <summary>
    /// 获取计划中的分类（项目）数量
    /// </summary>
    public static long getPlannedProjectCategoriesCount()
    {
        try
        {
            var result =  StoreFactoryHolder.getTaskCategoryStore()
                .GetUncompletedWithDueCategoriesCount(UserInfoHolder.getUserId());
            return result.Count;
        }
        catch (Exception)
        {
            return 0L;
        }
    }

    public static List<TaskCategoryWithCount> getPlannedProjectCategories()
    {
        try
        {
            var task =  StoreFactoryHolder.getTaskCategoryStore().GetUncompletedWithDueTaskCountAsync(UserInfoHolder.getUserId());
               
            //task.Wait();
            return task.Result ?? new List<TaskCategoryWithCount>();
        }
        catch (Exception)
        {
            return new List<TaskCategoryWithCount>();
        }
    }

    public static List<TTask> getDeletedTasks()
    {
        return StoreFactoryHolder.getTaskStore().getPagedDeletedTasks(UserInfoHolder.getUserId(), 0, 100);
    }

    public static DateTime getTaskTodayDueDate()
    {
        return DateTime.Today.AddMinutes(5);;
    }
    
    public static DateTime getTaskTomorrowDueDate()
    {
        return DateTime.Today.AddDays(1).AddMinutes(5);;
    }
    
    public static DateTime getTask3DaysLaterDueDate()
    {
        return DateTime.Today.AddDays(3).AddMinutes(5);;
    }

    public static bool IsNeedUpdateSystemCategory(TTask task)
    {
        return (DateUtils.IsDateBelongToTodayDueTask(task.TaskDueTime) ||
                DateUtils.IsDateBelongToWeekDueTask(task.TaskDueTime) || task.Priority == TaskPriority.high);
    }
    
    public static bool IsNeedUpdateSystemCategory(TodoTaskViewObject taskvo)
    {
        return (DateUtils.IsDateBelongToTodayDueTask(taskvo.DueDate) ||
                DateUtils.IsDateBelongToWeekDueTask(taskvo.DueDate) || taskvo.Priority == TaskPriority.high);
    }

    public static TaskCategoryViewObject PlannedCategoryCache { get; private set; }

    // /// <summary>
    // /// 异步刷新计划分类数量，并自动赋值到缓存对象
    // /// </summary>
    // public static void  RefreshPlannedCategoryCountAsync()
    // {
    //     if (PlannedCategoryCache != null)
    //     {
    //         PlannedCategoryCache.TaskCount = getPlannedProjectCategoriesCount();
    //         // 建议此处触发 INotifyPropertyChanged 或手动刷新 UI
    //     }
    // }

    public static IEnumerable<TaskCategoryViewObject> makeSystemCategories()
    {
        
        var systemCategories = new List<TaskCategoryViewObject>
        {
            
            new TaskCategoryViewObject
            {
                Name = "今天到期的任务", Key = SpecialTaskListConstants.Today,
                TaskCount = getTodayDueTaskUncompleteTaskCount(), IconName = "\uEE93",
                IsSystemCategory = true
            },
            new TaskCategoryViewObject
            {
                Name = "7天内到期的任务", Key = SpecialTaskListConstants.Week, TaskCount = getWeekDueTaskUncompleteTaskCount(),
                IconName = "\uE787",
                IsSystemCategory = true
            },
            new TaskCategoryViewObject
            {
                Name = "重要的任务", Key = SpecialTaskListConstants.Important, TaskCount = getImportantUncompleteTaskCount(),
                IconName = "\uE734",
                IsSystemCategory = true
            },
             new TaskCategoryViewObject
            {
                Name = "限时完成的项目(分类)", Key = SpecialTaskListConstants.Planned,
                TaskCount = getPlannedProjectCategoriesCount(), 
                IconName = "\uE7B8", // ClipboardList icon
                IsSystemCategory = true
            },
            // new TaskListViewObject
            // {
            //     Name = "回收站", Key = SpecialTaskListConstants.Deleted, TaskCount = getDeletedTasksCount(),
            //     IconName = "\uE74D",
            //     IsSystemList = true
            // },
        };

        return systemCategories;
    }

    
    public static TTask CreateTaskBySystemCategoryName(string title, string listName)
    {
        TTask thisTask = null;
        TaskCategory currentTaskList = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTaskList();
        
        if (SpecialTaskListConstants.Deleted ==
            listName)
            return null;

        if (SpecialTaskListConstants.Today ==
            listName)
            thisTask = TTaskFactory.CreateTaskInTaskList(title, DateTime.Today,
                currentTaskList);
        
        if (SpecialTaskListConstants.Week ==
            listName)
            thisTask = TTaskFactory.CreateTaskInTaskList(title, DateTime.Today.AddDays(3),
                currentTaskList);

        if (SpecialTaskListConstants.Important == listName)
        {
            thisTask = TTaskFactory.CreateTaskInTaskList(title, null, TaskPriority.high,
                currentTaskList);
        }
        
        return thisTask;
    }

    public static List<TodoTaskViewObject> GetSystemCategoryTasks(TaskCategoryViewObject category)
    {
        
        var uncTasks = new List<TodoTaskViewObject>();
        
        switch (category.Key)
        {
            case SpecialTaskListConstants.Today:
                SystemCategoryManager.getTodayDueTaskUncompleteTask()
                    .ForEach(
                        t =>
                        {
                            var list = StoreFactoryHolder.getTaskCategoryStore()
                                .GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                            if ( list != null)
                                uncTasks.Add(TodoTaskViewObject.GetFrom(t, list));
                        }
                    );
                break;
            case SpecialTaskListConstants.Week:
                SystemCategoryManager.getWeekDueTaskUncompleteTask()
                    .ForEach(
                        t =>
                        {
                            var list = StoreFactoryHolder.getTaskCategoryStore()
                                .GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                            if ( list != null)
                                uncTasks.Add(TodoTaskViewObject.GetFrom(t, list));
                        }
                    );
                break;
            case SpecialTaskListConstants.Important:
                SystemCategoryManager.getImportantTaskUncompleteTask()
                    .ForEach(
                        t =>
                        {
                            var list = StoreFactoryHolder.getTaskCategoryStore()
                                .GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                            if ( list != null)
                                uncTasks.Add(TodoTaskViewObject.GetFrom(t, list));
                        }
                    );
                break;
            case SpecialTaskListConstants.Deleted:
                SystemCategoryManager.getDeletedTasks()
                    .ForEach(
                        t =>
                        {
                            var list = StoreFactoryHolder.getTaskCategoryStore()
                                .GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                            if ( list != null)
                                uncTasks.Add(TodoTaskViewObject.GetFrom(t, list));
                        }
                    );
                break;
            case SpecialTaskListConstants.Planned:
                // "计划中的分类" 展示在单独面板，不返回 TodoTask 列表
                break;
    }
        
        return uncTasks;
    }
}