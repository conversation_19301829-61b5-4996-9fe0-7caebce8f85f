using System;
using System.Drawing;
using System.Threading.Tasks;
using Windows.ApplicationModel.DataTransfer;
using Windows.UI.Text;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;
using Microsoft.UI;
using Microsoft.UI.Input;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Documents;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using TaskStatus = HddtodoUI.Models.TaskStatus;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskItemControl : UserControl
    {
        // 依赖属性
        public static readonly DependencyProperty TaskVOProperty =
            DependencyProperty.Register("TaskVO", typeof(TodoTaskViewObject), typeof(TaskItemControl),
                new PropertyMetadata(null, OnTaskPropertyChanged));

        public TaskItemControl()
        {
            this.InitializeComponent();

            // 注册鼠标悬停事件
            this.PointerEntered += TaskItemControl_PointerEntered;
            this.PointerExited += TaskItemControl_PointerExited;

            // 初始化UI状态
            this.Loaded += (s, e) => RefreshUI();
        }

        public TodoTaskViewObject TaskVO
        {
            get { return (TodoTaskViewObject)GetValue(TaskVOProperty); }
            set { SetValue(TaskVOProperty, value); }
        }

        public TomatoTaskManager GetTomatoTaskManager()
        {
            return TaskTomatoManagerFactory.GetTomatoTaskManager(TaskVO.Task);
        }

        // 事件
        public event EventHandler<TodoTaskViewObject> TaskCompleted;
        public event EventHandler<TodoTaskViewObject> TaskUncompleted;
        public event EventHandler<TodoTaskViewObject> TaskStarted;
        public event EventHandler<TodoTaskViewObject> TaskDeleted;
        public event EventHandler<TodoTaskViewObject> TaskEdited;
        public event EventHandler<TodoTaskViewObject> TaskPropertyChanged;
        public event EventHandler<TodoTaskViewObject> TaskTitleClicked;

        private static void OnTaskPropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TaskItemControl control)
            {
                control.RefreshUI();
            }
        }

        public void RefreshTaskViewObject()
        {
            TaskVO = TodoTaskViewObject.GetFrom(GetTomatoTaskManager().getTask(),
                GetTomatoTaskManager().GetBelongToTaskList());
        }

        public void RefreshCheckBox()
        {
            // 暂时移除事件处理器以避免触发事件
            TaskCheckBox.Checked -= TaskCheckBox_Checked;
            TaskCheckBox.Unchecked -= TaskCheckBox_Unchecked;

            // 设置复选框状态
            TaskCheckBox.IsChecked = TaskVO.IsCompleted;

            // 重新添加事件处理器
            TaskCheckBox.Checked += TaskCheckBox_Checked;
            TaskCheckBox.Unchecked += TaskCheckBox_Unchecked;
        }

        /// <summary>
        /// 根据Task属性刷新UI状态
        /// </summary>
        public void RefreshUI()
        {
            if (TaskVO == null)
                return;

            // 更新任务完成状态复选框
            if (TaskCheckBox != null)
            {
                RefreshCheckBox();
            }

            // 更新任务状态
            var status = GetTomatoTaskManager().getTaskStatus();

            TaskStatus uiStatus = TaskStatusExtensions.GetTaskStatus(status);

            TaskVO.Status = uiStatus;


            // 更新任务状态按钮
            if (StartTaskButton != null)
            {
                //使用转换器更新工具提示
                var taskStatusTooltipConverter = Resources["TaskStatusTooltipConverter"] as IValueConverter;
                if (taskStatusTooltipConverter != null)
                {
                    string tooltip =
                        taskStatusTooltipConverter.Convert(uiStatus, typeof(string), null, null) as string;
                    ToolTipService.SetToolTip(StartTaskButton, tooltip ?? uiStatus.ToString());
                }
                else
                {
                    ToolTipService.SetToolTip(StartTaskButton, uiStatus.ToString());
                }

                // 查找按钮中的FontIcon并更新
                var fontIcon = StartTaskButton.Content as FontIcon;
                if (fontIcon != null)
                {
                    // 手动更新FontIcon
                    var taskStatusIconConverter = Resources["TaskStatusIconConverter"] as IValueConverter;
                    var taskStatusColorConverter = Resources["TaskStatusColorConverter"] as IValueConverter;

                    if (taskStatusIconConverter != null)
                    {
                        string glyph =
                            taskStatusIconConverter.Convert(uiStatus, typeof(string), null, null) as string;
                        if (!string.IsNullOrEmpty(glyph))
                        {
                            fontIcon.Glyph = glyph;
                        }
                    }

                    if (taskStatusColorConverter != null)
                    {
                        var color =
                            taskStatusColorConverter.Convert(uiStatus, typeof(SolidColorBrush), null, null) as
                                SolidColorBrush;
                        if (color != null)
                        {
                            fontIcon.Foreground = color;
                        }
                    }
                }
            }

            // 更新任务标题和截止日期
            // if (TaskTitleHyperlink != null)
            // {
            //     TaskTitleHyperlink.Inlines.Clear();
            //     TaskTitleHyperlink.Inlines.Add(new Run() { Text = TaskVO.Title });
            // }

            ColorIndicator.Background = new SolidColorBrush(Colors.Transparent);
            if (!String.IsNullOrEmpty(TaskVO.Color))
            {
                var ret = TaskVO.Color.Split(':');
                if (ret.Length > 1)
                {
                    String color = ret[0];
                    if ( ! string.IsNullOrEmpty(color))
                        ColorIndicator.Background = new SolidColorBrush(ColorUtils.ColorFromName(color));
                }
            }
            
            if (TaskDueDateTextBlock != null)
            {
                if (TaskVO.DueDate.HasValue)
                {
                    TaskDueDateTextBlock.Text = TaskVO.DueDate.Value.ToString("yyyy-MM-dd");
                    TaskDueDateTextBlock.Visibility = Visibility.Visible;

                    // 更新截止日期颜色
                    // 由于我们使用了x:Bind和转换器，这部分可能会自动更新
                }
                else
                {
                    TaskDueDateTextBlock.Visibility = Visibility.Collapsed;
                }
            }

            if (TaskVO.Priority == TaskPriority.high)
            {
                FontIcon dueTimeIcon = new FontIcon
                {
                    Glyph = "\uE735", // E121 ()
                    //FontSize = 20,
                    Foreground = new SolidColorBrush(Colors.Gold),
                    // Margin = new Thickness(0, 0, 4, 0)
                };
                // <FontIcon Glyph="&#xE735;" Foreground="Gold" Visibility="Collapsed" />
                TaskIconBorder.Child = dueTimeIcon;
            }
            else
            {
                TaskIconBorder.Child = null;
            }

            if (TaskVO.IsCompleted)
            {
                TaskTitleTextBlock.TextDecorations = TextDecorations.Strikethrough;
                StartTaskButton.IsEnabled = false;
            }
            else
            {
                TaskTitleTextBlock.TextDecorations = TextDecorations.None;
                StartTaskButton.IsEnabled = true;
            }

            if (TaskVO.DeletedStatus)
            {
                TaskTitleTextBlock.TextDecorations = TextDecorations.Strikethrough;
            }

            if (CurrentStatusHolder.getCurrentStatusHolder().IsCurrentListIsDeletedList())
            {
                RootGrid.ContextFlyout = null;

                //这里要添加一个菜单项，可以复原该task
                var menuFlyout = new MenuFlyout();
                var menuFlyoutItem = new MenuFlyoutItem();
                menuFlyoutItem.Text = "复原";
                menuFlyoutItem.Click += (s, e) =>
                {
                    //TaskUICoordinator.Instance.TaskRecover(TaskVO.Task);
                    StoreFactoryHolder.getTaskStore().unremoveTask(TaskVO.TaskID, UserInfoHolder.getUserId());
                    TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskUnRemoved(TaskVO.Task);
                };
                menuFlyout.Items.Add(menuFlyoutItem);
                RootGrid.ContextFlyout = menuFlyout;

                TaskCheckBox.IsEnabled = false;
                StartTaskButton.IsEnabled = false;
            }
            
            LogService.Instance.Debug($"Item ${TaskVO.Title} TaskVO.DirectSubTaskCount={TaskVO.DirectSubTaskCount}");
            if ( TaskVO.DirectSubTaskCount > 0 )
                SubTaskBadge.Visibility = Visibility.Visible;
            else
                SubTaskBadge.Visibility = Visibility.Collapsed;
            
        }

      
        
        // 鼠标悬停事件处理
        private void TaskItemControl_PointerEntered(object sender, PointerRoutedEventArgs e)
        {
            //背景变为使用Microsoft.UI.Colors.LightGray的0.5透明度
            RootGrid.Background = new SolidColorBrush(
                Colors.LightGray)
            {
                Opacity = 0.2
            };

            this.ProtectedCursor = InputSystemCursor.Create(InputSystemCursorShape.Hand);
        }

        private void TaskItemControl_PointerExited(object sender, PointerRoutedEventArgs e)
        {
            RootGrid.Background = new SolidColorBrush(
                Colors.Transparent);
            this.ProtectedCursor = InputSystemCursor.Create(InputSystemCursorShape.Arrow);
        }

        // 任务完成状态变化事件处理
        private void TaskCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TaskUICoordinatorFactory.Instance(TaskVO.Task).TaskSwitchComplete(TaskVO.Task);
            }
        }

        private void TaskCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TaskUICoordinatorFactory.Instance(TaskVO.Task).TaskSwitchUnComplete(TaskVO.Task);
            }
        }

        // 任务开始按钮点击事件
        private void StartTaskButton_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TaskUICoordinatorFactory.Instance(TaskVO.Task).StartOrPauseButtonClick(GetTomatoTaskManager());
            }
        }

        // 编辑按钮点击事件
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TheUICoordinator.Instance.ShowTaskDetailsWindow(TaskVO);

                //TaskEdited?.Invoke(this, TaskVO);
            }
        }

        // 删除按钮点击事件
        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                StoreFactoryHolder.getTaskStore().removeTask(TaskVO.TaskID, UserInfoHolder.getUserId());
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskRemoved(TaskVO.Task);
                TaskDeleted?.Invoke(this, TaskVO);
            }
        }

        // 设置截止日期
        private async void SetDueDateToday_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                //Task.DueDate = DateTime.Today;
                TTask tt = GetTomatoTaskManager().getTask();
                tt.TaskDueTime = SystemCategoryManager.getTaskTodayDueDate();
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        private async void SetDueDateTomorrow_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.TaskDueTime = SystemCategoryManager.getTaskTomorrowDueDate();
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        private async void SetDueDate3Days_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.TaskDueTime = SystemCategoryManager.getTask3DaysLaterDueDate();
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        private async void SetDueDateUndefined_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.TaskDueTime = null;
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        // 设置优先级
        private async void SetPriorityHigh_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.Priority = TaskPriority.high;
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        private async void SetPriorityMedium_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.Priority = TaskPriority.normal;
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        private async void SetPriorityLow_Click(object sender, RoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                TTask tt = GetTomatoTaskManager().getTask();
                tt.Priority = TaskPriority.low;
                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                });
                TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);
                TaskPropertyChanged?.Invoke(this, TaskVO);
            }
        }

        // 任务标题点击事件处理
        private void TaskTitleHyperlink_Click(Hyperlink sender, HyperlinkClickEventArgs args)
        {
           
        }

        private void SetMoveToTop(object sender, RoutedEventArgs e)
        {
            TTask tt = GetTomatoTaskManager().getTask();

            TaskUICoordinatorFactory.Instance(TaskVO.Task).MoveTaskToCategoryTop(tt.TaskID);
        }

        private void SetMoveToBotton(object sender, RoutedEventArgs e)
        {
            TTask tt = GetTomatoTaskManager().getTask();
            TaskUICoordinatorFactory.Instance(TaskVO.Task).MoveTaskToCategoryBottom(tt.TaskID);
        }

        // 设置颜色
        private void SetColorPink_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("pink");
        }

        private void SetColorOrange_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("orange");
        }

        private void SetColorGreen_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("green");
        }

        private void SetColorBlue_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("blue");
        }

        private void SetColorPurple_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("purple");
        }

        private void SetColorBrown_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("brown");
        }

        private void SetColorGold_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("gold");
        }

        private void SetColorBlack_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("black");
        }

        private void SetColorOlive_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("olive");
        }

        private void SetColorTeal_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("teal");
        }

        private void SetColorTomato_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("tomato");
        }

        private void SetColorNone_Click(object sender, RoutedEventArgs e)
        {
            SetTaskColor("");
        }

        private async void SetTaskColor(string colorName)
        {
            try
            {
                if (TaskVO != null)
                {
                    TTask tt = GetTomatoTaskManager().getTask();
                    tt.Color = colorName + ":" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    await Task.Run( () =>
                    {
                         StoreFactoryHolder.getTaskStore().saveTaskChange(tt, UserInfoHolder.getUserId());
                    });
                    TaskUICoordinatorFactory.Instance(TaskVO.Task).OnTaskModified(tt, TaskVO);

                    // 立即更新颜色指示器
                    if (string.IsNullOrEmpty(colorName))
                    {
                        ColorIndicator.Background = new SolidColorBrush(Colors.Transparent);
                    }
                    else
                    {
                        ColorIndicator.Background = new SolidColorBrush(ColorUtils.ColorFromName(colorName));
                    }

                    TaskPropertyChanged?.Invoke(this, TaskVO);
                }
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
            }
        }

        private void CopyTaskTitleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (TaskVO != null)
                {
                    string taskTitle = TaskVO.Title;
                    ClipboardUtils.CopyToClipboard(taskTitle);
            
                    // 显示通知
                    NotificationService.Instance.ShowNotification($"已复制任务标题: {taskTitle} 到剪贴板", NotificationLevel.Info, "复制成功");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Error(ex.StackTrace);
            }
            
        }

        private void TaskTitleTextBlock_Tapped(object sender, TappedRoutedEventArgs e)
        {
            if (TaskVO != null)
            {
                if (!CurrentStatusHolder.getCurrentStatusHolder().IsCurrentListIsDeletedList())
                {
                    var ret = TaskTomatoManagerFactory.GetTomatoTaskManager(TaskVO.TaskID);
                    TheUICoordinator.Instance.ShowTaskDetailsWindow(TodoTaskViewObject.GetFrom(ret.getTask(), ret.GetBelongToTaskList()));
                }
                    
            }
        }
    }
}