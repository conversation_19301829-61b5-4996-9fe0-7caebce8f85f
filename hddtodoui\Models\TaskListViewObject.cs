using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;

namespace HddtodoUI.Models
{
    public class TaskListViewObject
    {
        public string Key { get; set; }
        public string Name { get; set; }
        public long TaskCount { get; set; }
        public string IconName { get; set; }
        public bool IsSystemList { get; set; }
        public DateTime? DueDate { get; set; }
        public TaskPriority Priority { get; set; }
        
        public DateTime? ListCompleteTime { get; set; }

        public long ListOrder { get; set; }



        public static TaskListViewObject GetFrom(TaskListWithCount listWithCount)
        {
            var category = new TaskListViewObject
            {
                Name = listWithCount.TaskList.Name,
                TaskCount = listWithCount.UncompletedCount,
                IconName = "Inbox",
                IsSystemList = false,
                DueDate = listWithCount.TaskList.ListDueTime,
                Key = listWithCount.TaskList.Key,
                ListOrder = listWithCount.TaskList.ListOrder,
                ListCompleteTime = listWithCount.TaskList.ListCompleteTime,
             
            };
            return category;
        }

        public static TaskListViewObject GetFrom(TaskList list)
        {
            var category = new TaskListViewObject
            {
                Name = list.Name,
                TaskCount = 0,
                IconName = "Inbox",
                IsSystemList = false,
                DueDate = list.ListDueTime,
                Key = list.Key,
                ListOrder = list.ListOrder,
                ListCompleteTime = list.ListCompleteTime,
              
            };
            return category;
        }
        
        public static TaskListViewObject GetFrom(TaskCategoryViewObject list)
        {
            var category = new TaskListViewObject
            {
                Name = list.Name,
                TaskCount = list.TaskCount,
                IconName = "Inbox",
                IsSystemList = list.IsSystemCategory,
                DueDate = list.DueDate,
                Key = list.Key,
                ListOrder = list.CategoryOrder,
                ListCompleteTime = list.CategoryCompleteTime,
              
            };
            return category;
        }
    }


}