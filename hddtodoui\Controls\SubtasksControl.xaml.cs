using System;
using System.Collections.ObjectModel;
using System.Linq;
using Windows.System;
using CommunityToolkit.WinUI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;

namespace HddtodoUI.Controls
{
    public sealed partial class SubtasksControl : UserControl
    {
        private TodoTaskViewObject _parentTaskViewObject;
        
        public ObservableCollection<TodoTaskViewObject> Subtasks { get; private set; } = 
            new ObservableCollection<TodoTaskViewObject>();

        public SubtasksControl()
        {
            this.InitializeComponent();
            SubtasksListView.ItemsSource = Subtasks;
        }

        /// <summary>
        /// 设置父任务，用于添加子任务
        /// </summary>
        /// <param name="taskViewObject">父任务视图对象</param>
        public void SetParentTask(TodoTaskViewObject taskViewObject)
        {
            _parentTaskViewObject = taskViewObject;
            RefreshSubtasks();
        }

        /// <summary>
        /// 获取指定任务ID的子任务控件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>子任务控件</returns>
        public TaskItemControl GetSubTaskItemControlByTaskId(long taskId)
        {
            var control = SubtasksListView.ContainerFromItem(Subtasks.FirstOrDefault(t => t.TaskID == taskId)) as ListViewItem;
            return control?.FindDescendant<TaskItemControl>();
        }

        /// <summary>
        /// 刷新子任务列表
        /// </summary>
        public void RefreshSubtasks()
        {
            if (_parentTaskViewObject == null) return;
            
            var parentIds = _parentTaskViewObject.MakeMySelfAParentTaskIdsString();
            var subtasks = StoreFactoryHolder.getTaskStore()
                .getSubTasksByParentIds(UserInfoHolder.getUserId(), parentIds)
                .Select(t =>
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                    return TodoTaskViewObject.GetFrom(t, list);
                });
            
            Subtasks.Clear();
            foreach (var todoTaskViewObject in subtasks)
            {
                Subtasks.Add(todoTaskViewObject);
            }
        }

        /// <summary>
        /// 设置子任务列表的启用状态
        /// </summary>
        /// <param name="isEnabled">是否启用</param>
        public void SetEnabled(bool isEnabled)
        {
            NewSubtaskTextBox.IsEnabled = isEnabled;
            AddSubtaskButton.IsEnabled = isEnabled;
        }

        private void AddSubtaskButton_Click(object sender, RoutedEventArgs e)
        {
            AddSubtask();
        }

        private void NewSubtaskTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                AddSubtask();
                e.Handled = true;
            }
        }

        private void AddSubtask()
        {
            if (_parentTaskViewObject == null) return;
            
            string text = NewSubtaskTextBox.Text.Trim();
            if (string.IsNullOrEmpty(text)) return;
            
            var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(_parentTaskViewObject.Category, UserInfoHolder.getUserId());
            var parentTaskIds = _parentTaskViewObject.MakeMySelfAParentTaskIdsString();
            
            var task = StoreFactoryHolder.getTaskStore().createTask(text, TaskPriority.normal, UserInfoHolder.getUserId(), list, parentTaskIds);
            RefreshSubtasks();
            
            TaskUICoordinatorFactory.Instance(task).OnTaskAdded(task, list);
            NewSubtaskTextBox.Text = null;
        }
    }
}
