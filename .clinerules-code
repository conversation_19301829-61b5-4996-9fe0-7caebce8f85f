每次回答请再第一句话告知我您是否已经读取了这个文件（要告知读取了那个文件名）


这是一个任务管理软件， 目前的模式是： 任务有任务类别，任务类别有2种，一种是系统计算出来的， 比如：今天到期的任务，7天内到期的任务， 重要的任务，回收站等， 另外一种是用户自定义的，
用户自定义的任务类别可以添加，可以修改，可以删除，系统计算的任务无法添加修改删除。  任务可以自建任务类别组织自己的任务，也可以查看完成的任务。

写代码之前要记住的：
1、项目根目录在D:\netProject\newhddtodoui\hddtodoui文件夹,所有修改或者添加的代码都要在这个文件夹里面，所有的终端命令在这个文件夹下运行
2、修改代码前确认不要修改无关的代码
3、这是一个winui3项目，不要把wpf等其他项目的属性、方法、事件等复制到winui3这个项目中

代码写完后
1、代码是否都修改成功
2、代码是否完整
3、所有需要using的有没有using
4、所修改或者新建的代码的属性是否存在并且正确的设置，
5、所有修改或者添加的方法是否存在并且已经实现
6、所有引用的类和实体名字是否存在，并且已经引用
5、界面上的元素前景色和背景色不要相同，否则就看不到内容导致判断错误
6、如果用户要求现在网上搜寻一下，就一定要按照用户的要求在网上搜索一下方案先
7、以上请一个一个告诉我是否核查成功


相关知识：
    1、在 WinUI 3 中，Window.Resources 不再被支持。我们应该使用 Grid.Resources 来定义资源
    2、无法从“HddtodoUI.MainWindow”转换为“Microsoft.UI.Xaml.FrameworkElement”,这是 WinUI 3 中的一个已知问题。问题的根源是 Window 类不是 FrameworkElement，所以不能直接在 Window 中使用 x:Bind 的转换器。解决方案是将 UI 逻辑移到一个 UserControl 中。
    3、在UserControl中,转换器（IValueConverter）应该定义在 UserControl.Resources 而不是 Grid.Resources ,UserControl 有特殊的资源查找范围规则
    4、有 对话框  有需要时要记得 设置  XamlRoot
    5、在 WinUI 3 中，AppWindow.Move 方法的参数是一个 Windows.Graphics.PointInt32 类型的值
    6、在 WinUI 3 中，AppWindow.Resize 方法的参数是一个 Windows.Graphics.SizeInt32 类型的值
    7、在 WinUI 3 中， Window 不是 FrameworkElement 的子类

效率要求
    您必须
        以尽可能少的 API 调用完成每项任务
        在一次响应中提供完整的解决方案
        将所有相关更改集中在一起
        避免不必要的工具调用或验证
        将多个编辑合并为单个操作
        尽可能缓存和重复使用信息
    切勿
        拆分可在一次调用中完成的任务
        调用多余的工具
        在没有必要的情况下逐步实现功能
        要求不必要的用户交互
        验证不需要验证的步骤
        对相关操作进行单独调用
    在任何操作之前
        规划完整的解决方案
        确定所有需要的更改
        合并所有相关操作
        验证每个工具调用的必要性
        确保最大的实施效率
    API 调用优化
    核心原则
        始终以尽可能少的 API 调用解决任务
        绝不拆分可在一次调用中完成的任务
        始终将相关更改批量放在一起
        绝不调用多余或不必要的工具
一一回答是否遵守以上效率要求


