﻿"restore":{"projectUniqueName":"D:\\netProject\\newhddtodoui\\hddtodoUI\\HddtodoUI.csproj","projectName":"HddtodoUI","projectPath":"D:\\netProject\\newhddtodoui\\hddtodoUI\\HddtodoUI.csproj","outputPath":"D:\\netProject\\newhddtodoui\\hddtodoUI\\obj\\","projectStyle":"PackageReference","fallbackFolders":["d:\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0-windows10.0.22621.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0-windows10.0.22621":{"targetAlias":"net8.0-windows10.0.22621.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0-windows10.0.22621":{"targetAlias":"net8.0-windows10.0.22621.0","dependencies":{"CommunityToolkit.WinUI.Controls.Primitives":{"target":"Package","version":"[8.2.250402, )"},"CommunityToolkit.WinUI.Controls.Sizers":{"target":"Package","version":"[8.2.250402, )"},"CommunityToolkit.WinUI.Extensions":{"target":"Package","version":"[8.2.250402, )"},"Microsoft.Windows.SDK.BuildTools":{"target":"Package","version":"[10.0.26100.1742, )"},"Microsoft.WindowsAppSDK":{"target":"Package","version":"[1.7.250401001, )"},"NLog":{"target":"Package","version":"[5.4.0, )"},"System.Configuration.ConfigurationManager":{"target":"Package","version":"[10.0.0-preview.3.25171.5, )"},"WindowsInput":{"target":"Package","version":"[6.4.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Runtime.win-arm64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.AspNetCore.App.Runtime.win-x64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.AspNetCore.App.Runtime.win-x86","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.NETCore.App.Runtime.win-arm64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.NETCore.App.Runtime.win-x64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.NETCore.App.Runtime.win-x86","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.Windows.SDK.NET.Ref","version":"[10.0.26100.57, 10.0.26100.57]"},{"name":"Microsoft.WindowsDesktop.App.Runtime.win-arm64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.WindowsDesktop.App.Runtime.win-x64","version":"[8.0.8, 8.0.8]"},{"name":"Microsoft.WindowsDesktop.App.Runtime.win-x86","version":"[8.0.8, 8.0.8]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.Windows.SDK.NET.Ref":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}"runtimes":{"win-arm64":{"#import":[]},"win-x64":{"#import":[]},"win-x86":{"#import":[]}}