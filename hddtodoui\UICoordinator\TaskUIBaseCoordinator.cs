using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;

namespace HddtodoUI.UICoordinator;

public class TaskUIBaseCoordinator
{
    public void TomatoClockTicked()
    {
        //LogService.Instance.Debug("tomato manager clock ticked");
        var statusChanged = getCurrentTomatoTaskManager().clockTicked();
        //LogService.Instance.Debug("indicator clock ticked");
        TheUICoordinator.Instance.GetIndicatorWindow().TomatoClockTicked();
        //LogService.Instance.Debug("detail window clock ticked");
        TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().TomatoTicked();

        if (statusChanged)
        {
            TomatoClockStatusChanged();
        }
    }

    public void TomatoClockStatusChanged()
    {
        TheUICoordinator.Instance.GetIndicatorWindow()?.RefreshTaskUI(getCurrentTomatoTaskManager());
        TheUICoordinator.Instance.GetTaskDetailsWindow()?.GetTaskDetailsControl()?.UpdateTimerControls();
        TheUICoordinator.Instance.GetMainWindow()?.GetMainView()?.GetTaskPanelTaskItemControlByTaskID(getCurrentTomatoTaskManager().getTaskID())?.RefreshUI();
        
        NotificationService.Instance.ShowNotification("任务状态已变更", NotificationLevel.Info, "任务状态已变更");
        SoundUtility.PlayNotificationSound();
    }
    
    protected TomatoTaskManager getCurrentTomatoTaskManager()
    {
        return CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask();
    }
    
   

    protected bool IsCurrentTomatoTask(long taskId)
    {
        if (CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask() != null)
            if (CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask().getTaskID() == taskId)
                return true;

        return false;
    }

    protected bool IsCurrentTaskDetailWindowTask(long taskId)
    {
        if (TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().getMyTaskTomatoManager() !=
            null)
            if (TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().getMyTaskTomatoManager()
                    .getTaskID() ==
                taskId)
                return true;
        return false;
    }
    
  
    


}