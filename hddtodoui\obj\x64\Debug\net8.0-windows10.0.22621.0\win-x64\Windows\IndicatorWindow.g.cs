﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Windows\IndicatorWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "1E3D13D3B45CEED758758DC4B84C7FD94DC5639DCEEC621238F91A4EFD59150F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Windows
{
    partial class IndicatorWindow : 
        global::Microsoft.UI.Xaml.Window, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Windows\IndicatorWindow.xaml line 16
                {
                    this.MainSplitView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.SplitView>(target);
                }
                break;
            case 3: // Windows\IndicatorWindow.xaml line 20
                {
                    this.InfoRootGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 4: // Windows\IndicatorWindow.xaml line 38
                {
                    this.PaneMainButtonBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 5: // Windows\IndicatorWindow.xaml line 61
                {
                    this.InfoTitleTextBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Border)this.InfoTitleTextBorder).Tapped += this.InfoTitleTextBorder_OnTapped;
                }
                break;
            case 6: // Windows\IndicatorWindow.xaml line 65
                {
                    this.InfoTitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 7: // Windows\IndicatorWindow.xaml line 41
                {
                    this.PaneMainWindowButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PaneMainWindowButton).Click += this.MainWindowButton_OnClick;
                }
                break;
            case 8: // Windows\IndicatorWindow.xaml line 77
                {
                    this.RootGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 9: // Windows\IndicatorWindow.xaml line 100
                {
                    this.MainButtonBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 10: // Windows\IndicatorWindow.xaml line 123
                {
                    this.TaskCheckBoxBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 11: // Windows\IndicatorWindow.xaml line 132
                {
                    this.StartButtonBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 12: // Windows\IndicatorWindow.xaml line 148
                {
                    this.TitleTextBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Border)this.TitleTextBorder).Tapped += this.TitleTextBorder_OnTapped;
                }
                break;
            case 13: // Windows\IndicatorWindow.xaml line 160
                {
                    this.CountdownTextBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Border)this.CountdownTextBorder).Tapped += this.TitleTextBorder_OnTapped;
                }
                break;
            case 14: // Windows\IndicatorWindow.xaml line 163
                {
                    this.CountdownTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 15: // Windows\IndicatorWindow.xaml line 152
                {
                    this.TaskTitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 16: // Windows\IndicatorWindow.xaml line 135
                {
                    this.StartButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.StartButton).Click += this.StartButton_OnClick;
                }
                break;
            case 17: // Windows\IndicatorWindow.xaml line 126
                {
                    this.TaskCheckBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CheckBox>(target);
                }
                break;
            case 18: // Windows\IndicatorWindow.xaml line 103
                {
                    this.MainWindowButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.MainWindowButton).Click += this.MainWindowButton_OnClick;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

