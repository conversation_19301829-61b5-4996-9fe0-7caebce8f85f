using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using System;
using Windows.Graphics;
using WinRT.Interop;
using System.Runtime.InteropServices;

namespace HddtodoUI.Windows
{
    public sealed partial class TaskSearchWindow : Window
    {
        private AppWindow _appWindow;
        private OverlappedPresenter _presenter;
        private IntPtr _windowHandle;

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        public TaskSearchWindow()
        {
            this.InitializeComponent();

            // 获取窗口句柄
            _windowHandle = WindowNative.GetWindowHandle(this);
            var windowId = Win32Interop.GetWindowIdFromWindow(_windowHandle);
            _appWindow = AppWindow.GetFromWindowId(windowId);
            
            // 获取 presenter 并设置窗口始终在顶部
            // _presenter = _appWindow.Presenter as OverlappedPresenter;
            // if (_presenter != null)
            // {
            //     _presenter.IsAlwaysOnTop = true;
            // }
            _appWindow.MoveInZOrderAtTop();

            // 设置窗口大小
            _appWindow.Resize(new SizeInt32(800, 200));

            // 居中显示窗口
            var displayArea = DisplayArea.Primary;
            if (displayArea != null)
            {
                var centerX = (displayArea.WorkArea.Width - 800) / 2;
                var centerY = (displayArea.WorkArea.Height - 200) / 2;
                _appWindow.Move(new PointInt32(centerX, centerY));
            }

            // 设置事件
            TaskSearchControl.DialogClosed += TaskSearchControl_DialogClosed;
            this.Activated += TaskSearchWindow_Activated;
        }

        private void TaskSearchWindow_Activated(object sender, WindowActivatedEventArgs args)
        {
            if (args.WindowActivationState != WindowActivationState.Deactivated)
            {
                // 强制设置键盘焦点到搜索文本框
                DispatcherQueue.TryEnqueue(Microsoft.UI.Dispatching.DispatcherQueuePriority.Normal, () =>
                {
                    var textBox = TaskSearchControl.FindName("SearchTextBox") as Microsoft.UI.Xaml.Controls.TextBox;
                    if (textBox != null)
                    {
                        textBox.Focus(FocusState.Keyboard);
                    }
                });
            }
        }

        private void TaskSearchControl_DialogClosed(object sender, EventArgs e)
        {
            Close();
        }

        public void ShowAndActivate()
        {
            this.Activate();
            // 强制将窗口带到前台
            SetForegroundWindow(_windowHandle);
        }
    }
}
