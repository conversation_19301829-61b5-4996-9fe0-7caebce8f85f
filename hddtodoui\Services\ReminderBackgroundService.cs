using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Media;
using System.Threading;
using System.Threading.Tasks;
using Windows.Media.Core;
using Windows.Media.Playback;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Windows;
using Microsoft.UI.Dispatching;
using HddtodoUI.Utilities; // Added for SoundUtility
using Timer = System.Timers.Timer;

namespace HddtodoUI.Services
{
    /// <summary>
    /// 提醒后台服务，定期检查即将到来的提醒
    /// </summary>
    public class ReminderBackgroundService
    {
        private static ReminderBackgroundService _instance;
        private readonly System.Timers.Timer _timer;
        private readonly DispatcherQueue _dispatcherQueue;
        private bool _isRunning = false;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // 每分钟检查一次
        private readonly HashSet<long> _processedReminderIds = new HashSet<long>(); // 用于跟踪已处理的提醒ID
        /// <summary>
        /// 获取提醒后台服务的单例实例
        /// </summary>
        public static ReminderBackgroundService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ReminderBackgroundService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        private ReminderBackgroundService()
        {
            _dispatcherQueue = DispatcherQueue.GetForCurrentThread();
            _timer = new Timer();
        }

        /// <summary>
        /// 启动后台提醒服务
        /// </summary>
        public void Start()
        {
            if (_isRunning)
                return;

            _isRunning = true;
            _timer.Interval = 60000;
            _timer.Elapsed += Timer_Tick;
            _timer.Start();
            
         
            // 立即执行一次检查
            //CheckReminders();
            
            LogService.Instance.Info("提醒后台服务已启动");
        }

        /// <summary>
        /// 停止后台提醒服务
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
                return;

            _timer.Stop();
            _timer.Elapsed -= Timer_Tick;
            _isRunning = false;
            
            LogService.Instance.Info("提醒后台服务已停止");
        }

        /// <summary>
        /// 定时器触发事件处理
        /// </summary>
        private void Timer_Tick(object sender, object e)
        {
            // 先清理过期的提醒
            //CleanupExpiredReminders();
            
            // 然后检查新的提醒
            CheckReminders();
        }

        /// <summary>
        /// 检查即将到来的提醒
        /// </summary>
        private void CheckReminders()
        {
            try
            {
                // 确保用户已登录
                if (UserInfoHolder.getUserInfo() == null)
                    return;

                long userId = UserInfoHolder.getUserId();
                ITaskReminderStore reminderStore = StoreFactoryHolder.getTaskReminderStore();
                List<TaskReminder> upcomingReminders = reminderStore.GetUpcomingReminders(userId);

                if (upcomingReminders != null && upcomingReminders.Count > 0)
                {
                    LogService.Instance.Info($"找到 {upcomingReminders.Count} 个即将到来的提醒");
                    
                    foreach (var reminder in upcomingReminders)
                    {
                        // 检查是否已经处理过这个提醒
                        if (!_processedReminderIds.Contains(reminder.ReminderId))
                        {
                            LogService.Instance.Info($"处理提醒 ID: {reminder.ReminderId}, 任务 ID: {reminder.TaskId}, 提醒时间: {reminder.RemindTime}");
                            
                            // 获取任务信息
                            var task = StoreFactoryHolder.getTaskStore().getTaskById(reminder.TaskId, userId);
                            if (task != null)
                            {
                                // 发送通知
                                ShowReminderNotification(task, reminder);
                                
                                // 标记为已处理
                                _processedReminderIds.Add(reminder.ReminderId);
                                
                                // 如果是重复提醒，更新下次提醒时间
                                if (reminder.RepeatType != RemindRepeatType.None)
                                {
                                    UpdateNextReminderTime(reminder, userId);
                                }
                                else
                                {
                                    reminderStore.DeleteReminder(UserInfoHolder.getUserId(), reminder.ReminderId);
                                }
                            }
                            else
                            {
                                LogService.Instance.Info($"无法找到任务 ID: {reminder.TaskId}");
                            }
                        }
                        else
                        {
                            LogService.Instance.Info($"提醒 ID: {reminder.ReminderId} 已经处理过");
                        }
                    }
                }
                else
                {
                    LogService.Instance.Info("没有找到即将到来的提醒");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Info($"检查提醒时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示提醒通知
        /// </summary>
        private void ShowReminderNotification(TTask task, TaskReminder reminder)
        {
            _dispatcherQueue.TryEnqueue(() =>
            {
                try
                {
                    // 获取任务所属列表
                    var taskList = StoreFactoryHolder.getTaskListStore().getTaskListByKey(task.BelongToListKey, UserInfoHolder.getUserId());
                    string listName = taskList != null ? taskList.Name : "未知列表";
                    
                    // 构建通知内容
                    string title = "任务提醒";
                    string content = $"任务: {task.Title}\n";
                    
                  
                    // 添加列表名称
                    content += $"列表: {listName}\n";
                    content += $"本应提醒时间: {reminder.NextRemindTime}\n";
                    // 添加提醒时间
                    content += $"实际提醒时间: {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}\n";
                    
                    // 添加重复类型信息
                    if (reminder.RepeatType != RemindRepeatType.None)
                    {
                        string repeatTypeStr = reminder.GetRemindCycleDisplayInfo();
                      
                        content += $"重复: {repeatTypeStr}\n";
                        
                        // 添加下次提醒时间
                        if (reminder.NextRemindTime.HasValue)
                        {
                            content += $"下次提醒: {reminder.CaculateNextRemindTime().Value.ToString("yyyy-MM-dd HH:mm:ss")}";
                        }
                    }
                    
                    // 显示通知
                    NotificationService.Instance.ShowNotification(content, NotificationLevel.Info, title, 10000);
                    
                    // 播放提醒声音
                    SoundUtility.PlayNotificationSound();
                    
                    LogService.Instance.Info($"已显示提醒通知: {title} - {content}");
                }
                catch (Exception ex)
                {
                    LogService.Instance.Info($"显示提醒通知时出错: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 更新下次提醒时间
        /// </summary>
        private void UpdateNextReminderTime(TaskReminder reminder, long userId)
        {
            try
            {
                DateTime nextRemindTime = reminder.CaculateNextRemindTime().Value;
                
                LogService.Instance.Info($"更新提醒 ID: {reminder.ReminderId} 的下次提醒时间为: {nextRemindTime}");
                
                // 更新提醒的下次提醒时间
                reminder.NextRemindTime = nextRemindTime;
                
                // 保存到数据库
                bool result = StoreFactoryHolder.getTaskReminderStore().UpdateReminder(userId, reminder.ReminderId, reminder);
                
                if (result)
                {
                    LogService.Instance.Info($"成功更新提醒 ID: {reminder.ReminderId} 的下次提醒时间");
                }
                else
                {
                    LogService.Instance.Info($"更新提醒 ID: {reminder.ReminderId} 的下次提醒时间失败");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Info($"更新下次提醒时间时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除已处理的提醒ID列表
        /// </summary>
        public void ClearProcessedReminders()
        {
            _processedReminderIds.Clear();
            LogService.Instance.Info("已清除所有已处理的提醒ID");
        }
        
    
        /// <summary>
        /// 手动触发提醒检查（用于测试）
        /// </summary>
        public void TestCheckReminders()
        {
            CheckReminders();
        }
    }
}
