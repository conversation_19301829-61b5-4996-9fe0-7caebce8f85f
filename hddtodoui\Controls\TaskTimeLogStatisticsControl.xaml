<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.TaskTimeLogStatisticsControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20" >
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 日期选择器 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" 
                    x:Name="PreviousDayButton" 
                    Content="前一天" 
                    Click="PreviousDayButton_Click"/>
            
            <CalendarDatePicker Grid.Column="1" 
                               x:Name="DatePicker" 
                               HorizontalAlignment="Center"
                               DateChanged="DatePicker_DateChanged" />
            
            <Button Grid.Column="2" 
                    x:Name="NextDayButton" 
                    Content="后一天" 
                    Click="NextDayButton_Click"
                    IsEnabled="{x:Bind IsNextDayButtonEnable, Mode=OneWay}"
                   
                    />
        </Grid>

        <!-- 统计数据列表 -->
        <ListView Grid.Row="1" 
                  x:Name="StatisticsListView" 
                  Margin="0,0,0,20"
                  BorderThickness="1"
                  BorderBrush="{ThemeResource SystemControlForegroundBaseMediumLowBrush}">
            <ListView.HeaderTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="100"/>
                         
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="任务标题" Margin="5" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="总时长（分钟）" Margin="5" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="状态" Margin="5" FontWeight="Bold" HorizontalAlignment="Center"/>
                      
                    </Grid>
                </DataTemplate>
            </ListView.HeaderTemplate>
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="100"/>
                          
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="{Binding TaskTitle}" Margin="5"/>
                        <TextBlock Grid.Column="1" Text="{Binding TotalDuration}" Margin="5" HorizontalAlignment="Center"/>
                        <TextBlock Grid.Column="2" Text="{Binding status}" Margin="5" HorizontalAlignment="Center"/>
                      
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- 统计信息区域 -->
        <Grid Grid.Row="2" Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 任务完成情况统计 -->
            <Grid Grid.Row="0" Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="已完成任务" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock Text="{x:Bind CompletedTasksCount, Mode=OneWay}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{ThemeResource SystemAccentColor}"
                               HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="未完成任务" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                    <TextBlock Text="{x:Bind UncompletedTasksCount, Mode=OneWay}" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{ThemeResource SystemControlForegroundAccentBrush}"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
            
            <!-- 总工作时长 -->
            <TextBlock Grid.Row="1" 
                       Text="{x:Bind TotalWorkTimeText, Mode=OneWay}" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,10"
                       FontWeight="SemiBold"/>
            
            <!-- 进度条 -->
            <ProgressBar Grid.Row="2" 
                         Value="{x:Bind WorkTimePercentage, Mode=OneWay}" 
                         Maximum="100" 
                         Width="300" 
                         Height="15"
                         Foreground="{ThemeResource SystemAccentColor}"/>
        </Grid>
        
        <Grid Grid.Row="3" Margin="0,30,0,0">
            <StackPanel HorizontalAlignment="Center">
                <Button Content="关闭" Width="200" Click="ButtonBase_OnClick" Style="{StaticResource PrimaryButtonStyle}"></Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
