using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;

namespace HddtodoUI.Helpers
{
    public static class TaskCategoryGroupingHelper
    {
        public static List<(string GroupName, List<TaskCategoryWithCount> Categories)> GroupCategoriesByDueTime(IEnumerable<TaskCategoryWithCount> categories, DateTime now)
        {
            var overtime = new List<TaskCategoryWithCount>();
            var group7 = new List<TaskCategoryWithCount>();
            var group30 = new List<TaskCategoryWithCount>();
            var group90 = new List<TaskCategoryWithCount>();
            var group180 = new List<TaskCategoryWithCount>();

            foreach (var c in categories)
            {
             
                if (c.Category.CategoryDueTime.HasValue)
                {
                    var days = (c.Category.CategoryDueTime.Value.Date - now.Date).TotalDays;
                    if (days < 0)
                    {
                        overtime.Add(c);
                      
                    }
                    else if (days <= 7)
                    {
                        group7.Add(c);
                      
                    }
                    else if (days <= 30)
                    {
                        group30.Add(c);
                      
                    }
                    else if (days <= 90)
                    {
                        group90.Add(c);
                       
                    }
                    else if (days <= 180)
                    {
                        group180.Add(c);
                       
                    }
                }
            }
            var result = new List<(string, List<TaskCategoryWithCount>)>();
            if (overtime.Count > 0) result.Add(("已超时", overtime));
            if (group7.Count > 0) result.Add(("7天内到期", group7));
            if (group30.Count > 0) result.Add(("30天内到期", group30));
            if (group90.Count > 0) result.Add(("90天内到期", group90));
            if (group180.Count > 0) result.Add(("半年内到期", group180));
            return result;
        }

          // 颜色逻辑
        public static SolidColorBrush GetBrushByDateTime(DateTime? categoryDueTime)
        {
            if (!categoryDueTime.HasValue) return new SolidColorBrush(Colors.Black);

            var now = DateTime.Now;
            var days = (categoryDueTime.Value.Date - now.Date).TotalDays;

            return days switch
            {
                < 0 => new SolidColorBrush(Colors.Red), // 已超时
                <= 7 => new SolidColorBrush(Colors.Orange), // 7天内到期
                <= 30 => new SolidColorBrush(Colors.Goldenrod), // 30天内到期
                <= 90 => new SolidColorBrush(Colors.SteelBlue), // 90天内到期
                <= 180 => new SolidColorBrush(Colors.Gray), // 半年内到期
                _ => new SolidColorBrush(Colors.Black) // 默认颜色
            };
        }
    }
}
