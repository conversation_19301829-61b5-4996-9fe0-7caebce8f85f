using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Data;
using System;
using System.Collections;

namespace HddtodoUI.Converters
{
    /// <summary>
    /// 将集合是否为空转换为可见性的转换器
    /// 当集合为空时返回Visible，否则返回Collapsed
    /// </summary>
    public class EmptyCollectionToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            // 反转逻辑，如果参数为"reverse"
            bool reverse = parameter is string paramStr && paramStr.ToLower() == "reverse";
            
            bool isEmpty = true;
            
            if (value == null)
            {
                isEmpty = true;
            }
            else if (value is int count)
            {
                isEmpty = count == 0;
            }
            else if (value is ICollection collection)
            {
                isEmpty = collection.Count == 0;
            }
            else if (value is IEnumerable enumerable)
            {
                // 检查是否有任何元素
                var enumerator = enumerable.GetEnumerator();
                isEmpty = !enumerator.MoveNext();
                
                // 如果是IDisposable，调用Dispose
                if (enumerator is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            
            // 如果集合为空，返回Visible，否则返回Collapsed
            // 如果参数为"reverse"，则反转逻辑
            return (isEmpty != reverse) ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            // 不需要实现反向转换
            throw new NotImplementedException();
        }
    }
}
