using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Windows.Foundation;
using Windows.Graphics;
using Windows.UI.Core;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;

namespace HddtodoUI.Utilities;

public class DragDropMoveWindowHelper
{
    private AppWindow _appWindow;

    private Point _dragStartPoint;

    // Variables for custom drag implementation
    private bool _isDragging = false;

    public DragDropMoveWindowHelper(object target)
    {
        _appWindow = WindowsUtils.GetAppWindow(target);
    }
    
    [DllImport("user32.dll")]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool GetCursorPos(out POINT lpPoint);

    [StructLayout(LayoutKind.Sequential)]
    private struct POINT
    {
        public int X;
        public int Y;
    }

    public void Border_PointerPressed(object sender, PointerRoutedEventArgs e)
    {
        var pointerPoint = e.GetCurrentPoint(null);
        if (!pointerPoint.Properties.IsLeftButtonPressed) return;
        
        _isDragging = true;
       
        _dragStartPoint = e.GetCurrentPoint(null).Position;
        (sender as UIElement).CapturePointer(e.Pointer);
    }

    /// <summary>
    /// Handle TaskTitleTextBlock pointer moved event
    /// </summary>
    public void Border_PointerMoved(object sender, PointerRoutedEventArgs e)
    {
       
            if (_isDragging)
            {
                if (GetCursorPos(out POINT point))
                {
                    var currentPosition = point;

                    //窗口左上角位置要加上拖拽是鼠标所在位置确定最后窗口左上角位置
                    var windowX = currentPosition.X - _dragStartPoint.X;
                    var windowY = currentPosition.Y - _dragStartPoint.Y;

                    var dest = new PointInt32((int)windowX, (int)windowY);
                    _appWindow.Move(dest);
                }
                
            }
     
    }
    
    public void Border_PointerReleased(object sender, PointerRoutedEventArgs e)
    {
     
        if (_isDragging)
        {
            _isDragging = false;
            (sender as UIElement).ReleasePointerCapture(e.Pointer);
        }
    }
}