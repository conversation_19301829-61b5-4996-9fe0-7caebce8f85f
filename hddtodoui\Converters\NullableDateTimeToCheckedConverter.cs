using Microsoft.UI.Xaml.Data;
using System;

namespace HddtodoUI.Converters
{
    public class NullableDateTimeToCheckedConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            // 如果日期时间有值，则表示已完成
            return value is DateTime;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            // 如果勾选，则返回当前时间，否则返回null
            return (value is bool isChecked && isChecked) ? DateTime.Now : null;
        }
    }
}
