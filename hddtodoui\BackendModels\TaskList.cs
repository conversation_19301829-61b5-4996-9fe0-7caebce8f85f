using System;
using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels
{
    public class TaskList
    {
        public string Key { get; set; }
                
     
        public long UserId {get; set; }
        
        public string Name { get; set; }

        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime ListCreateTime { get; set; }

        public bool IsHide { get; set; }

        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? ListCompleteTime { get; set; }

        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? ListDueTime { get; set; }
        
        public long ListOrder { get; set; }

        public string ParentKey { get; set; }

        public bool HasChildren { get; set; }

        // 无参构造函数用于JSON反序列化
        public TaskList()
        {
        }
        
        public TaskList(string name, string key, long userId)
        {
            this.UserId = userId;
            this.Name = name;
            this.Key = key;
            this.ListCreateTime = DateTime.Now;
        }
        
        public bool IsCompleted()
        {
            if (this.ListCompleteTime == null)
                return false;
            return true;
        }

        public void MakeMeCompleteNow()
        {
            ListCompleteTime = DateTime.Now;
        }

        public void SetDueTime(DateTime dueTime)
        {
            ListDueTime = dueTime;
        }
    }
}