﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\TaskRestartDialog.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "15D8E70B830E2CB76ECD87D61647EED0E7636FD95865AFB30AB05EAC9F533A4B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskRestartDialog : 
        global::Microsoft.UI.Xaml.Controls.ContentDialog, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 1: // Controls\TaskRestartDialog.xaml line 2
                {
                    global::Microsoft.UI.Xaml.Controls.ContentDialog element1 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)element1).CloseButtonClick += this.ContentDialog_CloseButtonClick;
                }
                break;
            case 2: // Controls\TaskRestartDialog.xaml line 46
                {
                    this.PeriodSpanPanel = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.StackPanel>(target);
                }
                break;
            case 3: // Controls\TaskRestartDialog.xaml line 52
                {
                    this.NextRestartInfoTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 4: // Controls\TaskRestartDialog.xaml line 48
                {
                    this.PeriodSpanNumberBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.NumberBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.NumberBox)this.PeriodSpanNumberBox).ValueChanged += this.PeriodSpanNumberBox_OnValueChanged;
                }
                break;
            case 5: // Controls\TaskRestartDialog.xaml line 35
                {
                    this.PeriodTypeComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ComboBox)this.PeriodTypeComboBox).SelectionChanged += this.PeriodTypeComboBox_SelectionChanged;
                }
                break;
            case 6: // Controls\TaskRestartDialog.xaml line 28
                {
                    this.RestartDatePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.DatePicker>(target);
                    ((global::Microsoft.UI.Xaml.Controls.DatePicker)this.RestartDatePicker).SelectedDateChanged += this.RestartDatePicker_OnSelectedDateChanged;
                }
                break;
            case 7: // Controls\TaskRestartDialog.xaml line 29
                {
                    this.RestartTimePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TimePicker>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TimePicker)this.RestartTimePicker).SelectedTimeChanged += this.RestartTimePicker_OnSelectedTimeChanged;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

