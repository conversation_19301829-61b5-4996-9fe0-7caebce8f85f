﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.TaskSearchDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter"/>
        <converters:DueDateColorConverter x:Key="DueDateColorConverter"/>
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
    </UserControl.Resources>

    <StackPanel Spacing="12" Padding="16">
        <!-- 包含已完成任务复选框 -->
        <CheckBox x:ConnectionId='2' x:Name="IncludeCompletedTasksCheckBox" 
                  Content="包含已完成任务" 
                  />
        
        <!-- 搜索栏和搜索按钮 -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:ConnectionId='7' x:Name="SearchTextBox" 
                     PlaceholderText="输入关键词搜索任务" 
                     Grid.Column="0" 
                     Margin="0,0,8,0"
                                                    />
            
            <Button x:ConnectionId='8' x:Name="SearchButton" 
                    Content="搜索" 
                    Grid.Column="1"
                    Style="{StaticResource AccentButtonStyle}"
                                              />
        </Grid>
        
        <!-- 搜索结果列表 -->
        <ScrollViewer x:ConnectionId='3' x:Name="ResultsScrollViewer" 
                      Height="600"
                      Visibility="Collapsed">
            <ListView x:ConnectionId='5' x:Name="SearchResultsListView" 
                      SelectionMode="Single"
                                                             
                      >
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="0,8">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="50"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 任务完成状态 -->
                            <CheckBox Grid.Row="0" 
                                      Grid.Column="0" 
                                      IsChecked="{Binding IsCompleted}" 
                                      IsEnabled="False"  
                                      Margin="0,0,8,0"/>
                            
                            <!-- 任务标题 -->
                            <TextBlock Grid.Row="0" 
                                       Grid.Column="1" 
                                       Text="{Binding Title}" 
                                       FontWeight="SemiBold"
                                       TextWrapping="Wrap"  />
                            
                            <!-- 任务详情 -->
                            <StackPanel Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Orientation="Vertical"  Spacing="2" >
                                <StackPanel Orientation="Horizontal" 
                                            Spacing="2"
                                            Margin="0,0,0,0"
                                            >
                                    <!-- 任务列表名称 -->
                                    <TextBlock Text="{Binding TaskListName}"  
                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}" 
                                               FontSize="12"/>
                                    
                                    <!-- 截止日期 -->
                                    <TextBlock Text="{Binding DueDate, Converter={StaticResource DateTimeConverter}}" 
                                               Visibility="{Binding DueDate, Converter={StaticResource HasDateVisibilityConverter}}"
                                               Foreground="{Binding DueDate, Converter={StaticResource DueDateColorConverter}}"
                                               FontSize="12"/>
                                   
                                  
                                </StackPanel>
                                
                                <TextBlock Text="{Binding Remarks }"
                                           FontSize="12"  Foreground="{ThemeResource TextFillColorSecondaryBrush}"/>
                            </StackPanel>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
        </ScrollViewer>
        
        <!-- 底部按钮 -->
        <StackPanel Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Spacing="8">
            <Button x:ConnectionId='4' x:Name="CancelButton" 
                    Content="关闭"
                                              />
        </StackPanel>
    </StackPanel>
</UserControl>

