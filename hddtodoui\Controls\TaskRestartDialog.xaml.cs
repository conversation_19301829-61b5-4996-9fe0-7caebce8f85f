using HddtodoUI.BackendModels.BackendStore;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using HddtodoUI.BackendModels;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskRestartDialog : ContentDialog
    {
        private TaskRestart _restart;
        private bool _isNewRestart;

        public TaskRestart Restart => _restart;

        public TaskRestartDialog()
        {
            this.InitializeComponent();
            InitializeDialog(null);
        }

        public TaskRestartDialog(TaskRestart restart)
        {
            this.InitializeComponent();
            InitializeDialog(restart);
        }

        private void InitializeDialog(TaskRestart restart)
        {
            _isNewRestart = restart == null;
            
            if (_isNewRestart)
            {
                // 创建新的重启设置，设置默认值
                var now = DateTime.Now;
                _restart = new TaskRestart
                {
                    TaskNextRestartDateTime = now,
                    Period = TaskPeriod.daily,
                    TaskPeriodSpanCount = 1,
                    FirstSettingRestartTime = now // 设置初始重复开始时间
                };
                this.CloseButtonText = "";  // 隐藏删除按钮，因为是新建重启设置
            }
            else
            {
                // 编辑现有重启设置
                _restart = restart;
                // 如果FirstSettingRestartTime为空，则设置为当前的TaskNextRestartDateTime
                if (_restart.FirstSettingRestartTime == null)
                {
                    _restart.FirstSettingRestartTime = _restart.TaskNextRestartDateTime;
                }
            }

            // 设置UI控件的初始值
            RestartDatePicker.Date = _restart.TaskNextRestartDateTime.Date;
            RestartTimePicker.Time = new TimeSpan(_restart.TaskNextRestartDateTime.Hour, _restart.TaskNextRestartDateTime.Minute, 0);

            // 设置重复类型下拉框
           
            PeriodTypeComboBox.SelectedIndex = (int)_restart.Period;

            // 设置周期间隔
            PeriodSpanNumberBox.Value = _restart.TaskPeriodSpanCount;
            
            // 更新UI显示
            UpdatePeriodSpanVisibility();
            UpdateNextRestartInfo();
        }

        private void PeriodTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PeriodTypeComboBox.SelectedItem != null)
            {
                var selectedItem = PeriodTypeComboBox.SelectedItem as ComboBoxItem;
                _restart.Period = (TaskPeriod)Enum.Parse(typeof(TaskPeriod), selectedItem.Tag.ToString(), true);
                
                UpdatePeriodSpanVisibility();
                UpdateNextRestartInfo();
            }
        }

        private void UpdatePeriodSpanVisibility()
        {
            // 只有非一次性重启才显示周期间隔设置
            PeriodSpanPanel.Visibility = _restart.Period == TaskPeriod.none ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateNextRestartInfo()
        {
            // 获取用户选择的日期和时间
            if (_restart == null) return;
            
            var selectedDate = RestartDatePicker.Date.Date;
            var selectedTime = RestartTimePicker.Time;
            var restartDateTime = selectedDate.Add(selectedTime);
            
            // 更新重启时间
            _restart.FirstSettingRestartTime = restartDateTime;
            _restart.TaskNextRestartDateTime = restartDateTime;
            // 同时更新FirstSettingRestartTime（如果是新建或者FirstSettingRestartTime为空）
            // if (_isNewRestart || _restart.FirstSettingRestartTime == null)
            // {
            //     _restart.FirstSettingRestartTime = restartDateTime;
            // }
            
            // 更新周期间隔
            if (PeriodSpanNumberBox.Value > 0)
            {
                _restart.TaskPeriodSpanCount = (int)PeriodSpanNumberBox.Value;
            }
            
            // 显示下次重启时间信息
            string periodInfo = GetPeriodDisplayInfo();
            
            NextRestartInfoTextBlock.Text = $"下次重启时间: {_restart.TaskNextRestartDateTime}\n{periodInfo}";
        }

        private string GetPeriodDisplayInfo()
        {
            if (_restart.Period == TaskPeriod.none)
            {
                return "一次性重启，不重复";
            }

            string unit = _restart.Period switch
            {
                TaskPeriod.hour => "小时",
                TaskPeriod.daily => "天",
                TaskPeriod.weekly => "周",
                TaskPeriod.monthly => "月",
                TaskPeriod.yearly => "年",
                _ => ""
            };

            return $"每 {_restart.TaskPeriodSpanCount} {unit}重启一次";
        }

        private void ContentDialog_CloseButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 用户点击了删除按钮
            _restart = null;
        }

        private void RestartDatePicker_OnSelectedDateChanged(DatePicker sender, DatePickerSelectedValueChangedEventArgs args)
        {
            UpdateNextRestartInfo();
        }

        private void RestartTimePicker_OnSelectedTimeChanged(TimePicker sender, TimePickerSelectedValueChangedEventArgs args)
        {
            UpdateNextRestartInfo();
        }

        private void PeriodSpanNumberBox_OnValueChanged(NumberBox sender, NumberBoxValueChangedEventArgs args)
        {
            UpdateNextRestartInfo();
        }
    }
}
