﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\QuickAddTaskDialog.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "C15E06158A92E9B5AF4848717A1E45F1043A8B844BBA7E2F0245FBAEC39FB286"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class QuickAddTaskDialog : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\QuickAddTaskDialog.xaml line 14
                {
                    this.TitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 3: // Controls\QuickAddTaskDialog.xaml line 82
                {
                    this.CancelButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.CancelButton).Click += this.CancelButton_Click;
                }
                break;
            case 4: // Controls\QuickAddTaskDialog.xaml line 85
                {
                    this.SaveButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.SaveButton).Click += this.SaveButton_Click;
                }
                break;
            case 5: // Controls\QuickAddTaskDialog.xaml line 89
                {
                    this.SaveAndStartButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.SaveAndStartButton).Click += this.SaveAndStartButton_Click;
                }
                break;
            case 6: // Controls\QuickAddTaskDialog.xaml line 35
                {
                    this.TaskListComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 7: // Controls\QuickAddTaskDialog.xaml line 40
                {
                    this.PriorityComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 8: // Controls\QuickAddTaskDialog.xaml line 50
                {
                    this.DueTimePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CalendarDatePicker>(target);
                    ((global::Microsoft.UI.Xaml.Controls.CalendarDatePicker)this.DueTimePicker).DateChanged += this.DueTimePicker_DateChanged;
                }
                break;
            case 9: // Controls\QuickAddTaskDialog.xaml line 55
                {
                    this.ClearDateButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.ClearDateButton).Click += this.ClearDateButton_Click;
                }
                break;
            case 10: // Controls\QuickAddTaskDialog.xaml line 23
                {
                    this.VoiceInputButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.VoiceInputButton).Click += this.VoiceInputButton_Click;
                }
                break;
            case 11: // Controls\QuickAddTaskDialog.xaml line 26
                {
                    this.TitleTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.TitleTextBox).KeyDown += this.TitleTextBox_KeyDown;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

