using System.Text.Json;
using System.Text.Json.Serialization;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// 提供统一的JsonSerializerOptions配置，确保所有DateTime属性使用统一格式
    /// </summary>
    public static class JsonSerializerOptionsProvider
    {
        private static JsonSerializerOptions _defaultOptions;
        
        /// <summary>
        /// 获取默认的JsonSerializerOptions，包含统一的DateTime转换器
        /// </summary>
        public static JsonSerializerOptions DefaultOptions
        {
            get
            {
                if (_defaultOptions == null)
                {
                    _defaultOptions = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        WriteIndented = false,
                        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                    };
                    
                    // 添加统一的DateTime转换器
                    _defaultOptions.Converters.Add(new DateTimeJsonConverter());
                    _defaultOptions.Converters.Add(new NullableDateTimeJsonConverter());
                }
                
                return _defaultOptions;
            }
        }
        
        /// <summary>
        /// 获取用于API通信的JsonSerializerOptions
        /// </summary>
        public static JsonSerializerOptions ApiOptions
        {
            get
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };
                
                // 添加统一的DateTime转换器
                options.Converters.Add(new DateTimeJsonConverter());
                options.Converters.Add(new NullableDateTimeJsonConverter());
                
                return options;
            }
        }
        
        /// <summary>
        /// 获取用于调试的JsonSerializerOptions（格式化输出）
        /// </summary>
        public static JsonSerializerOptions DebugOptions
        {
            get
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };
                
                // 添加统一的DateTime转换器
                options.Converters.Add(new DateTimeJsonConverter());
                options.Converters.Add(new NullableDateTimeJsonConverter());
                
                return options;
            }
        }
    }
}
