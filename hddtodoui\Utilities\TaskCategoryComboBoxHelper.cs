using System.Collections.Generic;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Utilities;

public static class TaskCategoryComboBoxHelper
{
    public static void MakeComboBoxItem( ComboBox TaskListComboBox)
    {
        var userCategories = new List<TaskCategoryViewObject>();

        // 使用 ITaskCategoryStore API 获取分类列表
        var categoryStore = StoreFactoryHolder.getTaskCategoryStore();
        var categoriesWithCount = categoryStore.GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId());
            
        foreach (var categoryWithCount in categoriesWithCount)
        {
            userCategories.Add(TaskCategoryViewObject.GetFrom(categoryWithCount.Category, categoryWithCount.TaskCount, categoryWithCount.SubcategoryCount));
        }

        foreach (var userCategory in userCategories)
        {
            if (userCategory.HasChildren)
            {
                var childrenWithCount = categoryStore.GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId(), userCategory.Key);
                foreach (var childWithCount in childrenWithCount)
                {
                    userCategory.Children.Add(TaskCategoryViewObject.GetFrom(childWithCount.Category, childWithCount.TaskCount, childWithCount.SubcategoryCount));
                }
                userCategory.IsChildrenLoaded = true;
            }
        }
            
        // 添加用户列表
        foreach (var list in userCategories)
        {
            TaskListComboBox.Items.Add(new ComboBoxItem { Content = list.Name, Tag = list.Key });
                
            foreach (var subList in list.Children)
            {
                TaskListComboBox.Items.Add(new ComboBoxItem { Content = "   -   "+subList.Name, Tag = subList.Key });
            }
               
        }
    }
}