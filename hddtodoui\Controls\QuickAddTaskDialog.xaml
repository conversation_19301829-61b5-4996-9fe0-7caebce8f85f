<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.QuickAddTaskDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    >

    <StackPanel Spacing="12" Padding="4" MinWidth="700" >
        
        <TextBlock Name="TitleTextBlock" Text="新建任务" HorizontalAlignment="center"></TextBlock>
        
        <Grid ColumnSpacing="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
      
            <Button x:Name="VoiceInputButton" Grid.Column="1" VerticalAlignment="Center" Click="VoiceInputButton_Click" ToolTipService.ToolTip="语音输入 (应用内识别)">
                <FontIcon FontFamily="Segoe MDL2 Assets" Glyph="&#xE720;"/>
            </Button>
            <TextBox x:Name="TitleTextBox" Grid.Column="2"
                     PlaceholderText="输入任务标题 或 点击左侧麦克风进行语音输入" 
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     KeyDown="TitleTextBox_KeyDown"/>
        </Grid>
        
        <StackPanel Orientation="Horizontal" 
                    HorizontalAlignment="Left" Spacing="25" >
            <ComboBox x:Name="TaskListComboBox" 
                      Header="任务列表"
                      SelectedIndex="0"
                      DisplayMemberPath="Name"/>
        
            <ComboBox x:Name="PriorityComboBox" 
                      Header="优先级"
                      SelectedIndex="1">
                <ComboBoxItem Content="高"/>
                <ComboBoxItem Content="中"/>
                <ComboBoxItem Content="低"/>
                
            </ComboBox>

            <Grid>
                <CalendarDatePicker x:Name="DueTimePicker"
                                    Header="截止日期"
                                    PlaceholderText="选择截止日期"
                                    DateChanged="DueTimePicker_DateChanged"/>
            
                <Button x:Name="ClearDateButton"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        Margin="0,0,4,4"
                        Click="ClearDateButton_Click"
                        Visibility="Collapsed"
                        ToolTipService.ToolTip="清除已选择的日期">
                    <FontIcon FontFamily="Segoe MDL2 Assets" 
                              Glyph="&#xE894;"
                              FontSize="12"/>
                    <Button.Resources>
                        <ResourceDictionary>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Padding" Value="8,4"/>
                            </Style>
                        </ResourceDictionary>
                    </Button.Resources>
                </Button>
            </Grid>
        </StackPanel>
       
        
        <StackPanel Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Spacing="8">
            <Button x:Name="CancelButton" 
                    Content="取消"
                    Click="CancelButton_Click"/>
            <Button x:Name="SaveButton" 
                    Content="保存(回车)"
                    Style="{StaticResource AccentButtonStyle}"
                    Click="SaveButton_Click"/>
            <Button x:Name="SaveAndStartButton" 
                    Content="保存并开始任务(ctrl+回车)"
                    Style="{StaticResource AccentButtonStyle}"
                    Click="SaveAndStartButton_Click"/>
        </StackPanel>
    </StackPanel>
</UserControl>
