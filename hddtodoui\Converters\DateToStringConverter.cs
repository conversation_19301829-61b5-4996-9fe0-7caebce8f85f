using Microsoft.UI.Xaml.Data;
using System;

namespace HddtodoUI.Converters
{
    public class DateToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is DateTime date)
            {
                if (date.Date == DateTime.Today)
                {
                    return "今天";
                }
                else if (date.Date == DateTime.Today.AddDays(1))
                {
                    return "明天";
                }
                else if (date.Date == DateTime.Today.AddDays(-1))
                {
                    return "昨天";
                }
                else
                {
                    return date.ToString("MM/dd");
                }
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
