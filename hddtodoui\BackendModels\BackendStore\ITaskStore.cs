using System;
using System.Collections.Generic;
using System.Linq;

namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskStore
    {
        List<TTask> getRecentRunningTask(int count, long userId);
        List<TTask> getAllUnCompleteTaskByBelongToTaskList(TaskCategory tl, long userId);
        List<TTask> getAllUnCompleteTaskByDueByDate(DateTime dueDate, long userId);
        long getAllUnCompleteTaskCountsByDueByDate(DateTime dueDate, long userId);
        List<TTask> getAllUnCompleteTaskByDueBetweenDate(DateTime startDueDate, DateTime endDueDate, long userId);
        long getAllUnCompleteTaskCountsByDueBetweenDate(DateTime startDueDate, DateTime endDueDate, long userId);
        TTask getTaskById(long taskId, long userId);
        IEnumerable<IGrouping<DateTime, TTask>> getGroupByDateCompleteTasks(TaskCategory taskList, long userId);
        IEnumerable<IGrouping<DateTime, TTask>> getGroupByTodayCompleteTasks(long userId);
        TTask createTask(string title, TaskPriority priority, long userId, TaskCategory list, string parentTaskIds = null);
        TTask createTask(string title, TaskPriority priority, DateTime? dueTime, long userId, TaskCategory list, string parentTaskIds = null);
        void addTaskToUncompleteList(TTask task, long userId);
        void addTaskToCompleteList(TTask task, long userId);
        void saveTaskChange(TTask task, long userId);
        bool switchTaskToUncompleteList(TTask task, long userId);
        bool switchTaskToCompleteList(TTask task, long userId);
        void moveTaskToOtherTaskList(TTask source, TaskCategory target, long userId);
        void dragUncompleteTask(long startId, long targetId, long userId);
        string GetTaskTitle(long taskId, long userId);
        List<TTask> getTaskListByKeyword(string keyword, long userId);
        List<TTask> getTaskListByKeyword(string keyword, bool isAll, long userId);

        TTask getLastExecutionUncompletedTask(long userId);

        List<TTask> getWaitToComputeRestartTimeTaskList(long userId);
        public List<TTask> getWaitToRestartTaskList(long userId);

        public List<TTask> getPagedCompleteTasks(string listKey, long userId, int page, int pageSize);

        public long getCompleteTasksCount(string listKey, long userId);

        public List<TTask> getTodayCompleteTasks(long userId);

        public List<TTask> getAllUnCompleteHighPriorityTasks(long userId);

        public long getAllUnCompleteHighPriorityTasksCount(long userId);

        public long getDeletedTasksCount(long userId);
        
        public List<TTask> getPagedDeletedTasks(long userId, int page, int pageSize);

        public void removeTask(long taskId, long userId);

        public void unremoveTask(long taskId, long userId);

        public List<TTask> getSubTasksByParentIds(long userId, string parentIds);

        public long updateSubtaskCount(long userId, long taskId);
    }
}