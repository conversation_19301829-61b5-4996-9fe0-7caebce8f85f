using System;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// 统一的DateTime JSON转换器，使用DateTimeFormats中定义的格式
    /// </summary>
    public class DateTimeJsonConverter : JsonConverter<DateTime>
    {
        // 使用统一的格式常量
        private static string DateTimeFormat => DateTimeFormats.GetCurrentFormat();

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var dateString = reader.GetString();
            if (string.IsNullOrEmpty(dateString))
                return DateTime.MinValue;

            // 首先尝试使用统一格式解析
            if (DateTime.TryParseExact(dateString, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out var exactResult))
                return exactResult;

            // 尝试使用标准格式解析（兼容性）
            if (DateTime.TryParse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.None, out var standardResult))
                return standardResult;

            // 如果标准解析失败，尝试其他常见格式（向后兼容）
            // 使用统一定义的支持格式数组
            string[] fallbackFormats = DateTimeFormats.SupportedFormats;

            if (DateTime.TryParseExact(dateString, fallbackFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var fallbackResult))
                return fallbackResult;

            // 最后尝试默认解析
            return DateTime.Parse(dateString, CultureInfo.InvariantCulture);
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // 使用统一定义的格式进行输出
            // 如果DateTime.Kind为Unspecified，则视为本地时间
            DateTime outputTime = value.Kind == DateTimeKind.Unspecified
                ? DateTime.SpecifyKind(value, DateTimeKind.Local)
                : value;

            writer.WriteStringValue(outputTime.ToString(DateTimeFormat, CultureInfo.InvariantCulture));
        }
    }
}