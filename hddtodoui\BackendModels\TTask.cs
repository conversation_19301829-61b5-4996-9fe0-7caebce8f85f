using System;
// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期格式请在枚举处使用 JsonPropertyName(全大写) 或放在 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，哪怕包含拼写错误 (如 TaskCreatTime)。
// 3. 若需定制序列化逻辑，仅在此文件或 JsonConverters 中写一次，避免重复。
// 4. 其它文件只使用现有 Converter / 属性注解.

using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.BackendModels
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum TaskPriority
    {
        [JsonPropertyName("HIGH")]
        high,
        [JsonPropertyName("NORMAL")]
        normal,
        [JsonPropertyName("LOW")]
        low
    }
    
    public class TTask
    {

        public long TaskID { get; set; }
        
        public long UserId { get; set; }

        public string Title { get; set; }
        
        public int TomatoCount { get; set; }
        
        public TaskPriority Priority { get; set; }
        
        [JsonPropertyName("taskCreateTime")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime TaskCreatTime { get; set; }
        
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? TaskDueTime { get; set; }
        
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? TaskCompleteTime { get; set; }
        
        public string BelongToListKey { get; set; }
        
        public long TaskOrder { get; set; }
        
        public string Color { get; set; }
        
        public int TomatoTimeSpan { get; set; }
        

        public string TaskRemark { get; set; }

        public bool DeletedStatus { get; set; }
        
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? TaskNextRestartDateTime { get; set; }
        
        [JsonConverter(typeof(NullableEnumConverter<TaskPeriod>))]
        public TaskPeriod? Period { get; set; }
        
        [JsonPropertyName("taskPeriodSpanCount")]
        public int TaskPeroidSpanCount { get; set; }

        // 新增字段，对应后台Scala新增字段
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? DeletedTime { get; set; }
        
        public bool RealDeletedStatus { get; set; }
        
        public string ParentTaskIDs { get; set; }
        
        public int DirectSubTaskCount { get; set; }
        
        public int TotalSubTaskCount { get; set; }
        
        public int CompletedSubTaskCount { get; set; }
    
        // 添加无参构造函数
        public TTask()
        {
            TomatoCount = 0;
            Priority = TaskPriority.normal;
            TaskCreatTime = DateTime.Now;
            TomatoTimeSpan = TomatoConfig.tomatoTimeSpan;
            DeletedStatus = false;
            TaskPeroidSpanCount = 0;
            RealDeletedStatus = false;
            DirectSubTaskCount = 0;
            TotalSubTaskCount = 0;
            CompletedSubTaskCount = 0;
        }

        public TTask(string title, TaskPriority priority, string listKey, long userId)
        {
            Title = title;
            TomatoCount = 0;
            Priority = priority;
            TaskCreatTime = DateTime.Now;
            BelongToListKey = listKey;
            TomatoTimeSpan = TomatoConfig.tomatoTimeSpan;
            DeletedStatus = false;
            UserId = userId;
        }
        
        public TTask(string title, TaskPriority priority, DateTime? dueTime, string listKey, long userId)
        {
            Title = title;
            TomatoCount = 0;
            Priority = priority;
            TaskCreatTime = DateTime.Now;
            BelongToListKey = listKey;
            TomatoTimeSpan = TomatoConfig.tomatoTimeSpan;
            TaskDueTime = dueTime;
            DeletedStatus = false;
            UserId = userId;
        }

        // public TTask(long taskId, string title, int tomatoCount, TaskPriority priority, DateTime taskCreatTime, DateTime? taskDueTime, DateTime? taskCompleteTime, string listKey)
        // {
        //     TaskID = taskId;
        //     Title = title;
        //     TomatoCount = tomatoCount;
        //     Priority = priority;
        //     TaskCreatTime = taskCreatTime;
        //     TaskDueTime = taskDueTime;
        //     TaskCompleteTime = taskCompleteTime;
        //     BelongToListKey = listKey;
        // }
        //
        // public TTask(string title, int tomatoCount, TaskPriority priority, DateTime taskCreatTime, DateTime? taskDueTime,
        //     DateTime? taskCompleteTime, string listKey) : this(0,title, tomatoCount, priority, taskCreatTime,
        //     taskDueTime, taskCompleteTime, listKey)
        // {
        //     
        // }

        public bool IsCompleted()
        {
            if (this.TaskCompleteTime == null)
                return false;
            return true;
        }

        public bool IsHasParent()
        {
            return ! string.IsNullOrEmpty(ParentTaskIDs);
        }
        

        public DateTime CaculateTaskNextRestartTime()
        {
            if (this.Period == null)
            {
                throw new Exception("period is null");
            }
            
            if (this.Period == TaskPeriod.none)
            {
                throw new Exception("period is non");
            }
            
            if(this.TaskPeroidSpanCount == 0 )
            {
                throw new Exception("taskPeroidSpanCount is 0");
            }

            DateTime startDate = this.TaskDueTime.GetValueOrDefault(this.TaskCreatTime);
            
            /*任务：
              到期时间
              创建时间 8月1日周四

              循环任务：每周四关闭一下服务器

              今天是13号周二
              13号开始转成循环任务

              那么应该本周15号开始执行第一次任务
              假设15号本周没有完成
              在下一周的周四22号完成
              则任务重新生成应该是在29号生成*/

            DateTime ret = CaculateTaskNextRestartTime(startDate);
            
            if ( ret < DateTime.Now)
                if (this.Period == TaskPeriod.hour)
                    return DateTime.Now;
                else
                    return DateTime.Now.Date;
            
            return ret;
        }
        
        private DateTime CaculateTaskNextRestartTime(DateTime startDate)
        {
            var dayDate = startDate.Date;
            var hourDate = startDate;
            
            switch (this.Period)
            {
                case TaskPeriod.hour:
                    var hourOfToday = DateTime.Today.AddHours(this.TaskPeroidSpanCount);
                    var hourOfTomorrow = DateTime.Today.AddDays(1).AddHours(this.TaskPeroidSpanCount);
                    if (hourOfToday < DateTime.Now)
                        return hourOfTomorrow;
                    else 
                        return hourOfToday;
                case TaskPeriod.daily:
                    return dayDate.AddDays(this.TaskPeroidSpanCount);
                case TaskPeriod.weekly:
                    int dayOfWeek = (int)dayDate.DayOfWeek;
                    int today = (int)DateTime.Now.DayOfWeek;
                    
                    LogService.Instance.Info("dayOfWeek=" + dayOfWeek);
                    LogService.Instance.Info("today=" + today);
                    
                    if (dayOfWeek < today)
                    {
                        return DateTime.Now.Date.AddDays(7 - (today - dayOfWeek)+ 7*(this.TaskPeroidSpanCount-1));
                    }
                    
                    return dayDate.AddDays(7*(this.TaskPeroidSpanCount));
                
                case TaskPeriod.monthly:
                    return dayDate.AddMonths(this.TaskPeroidSpanCount);
                case TaskPeriod.yearly:
                    return dayDate.AddYears(this.TaskPeroidSpanCount);
            }
            throw new Exception("non period");
        }

        public void GenerateNextRestartTimeFromTaskCreateTime()
        {
            this.TaskNextRestartDateTime = CaculateTaskNextRestartTime();
            //this.TaskDueTime = this.TaskNextRestartDateTime;
        }

        public List<long> GetParentTaskIds()
        {
            if( ParentTaskIDs == null)
                return new List<long>();
            
            return ParentTaskIDs.Split(',').Select(long.Parse).ToList();
        }
        
        public String MakeMySelfAParentTaskIdsString()
        {
            var taskIds = TaskID.ToString();
            if ( ! String.IsNullOrEmpty(ParentTaskIDs))
                taskIds = ParentTaskIDs + "," + TaskID;

            return taskIds;
        }
        
        public long GetDirectParentTaskId()
        {
            var taskIds = ParentTaskIDs.Split(',').Select(long.Parse).ToList();
            return taskIds.Last();
        }
        
    }
}