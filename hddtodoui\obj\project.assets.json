{"version": 3, "targets": {"net8.0-windows10.0.22621": {"CommunityToolkit.Common/8.2.1": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Web.WebView2/1.0.2903.40": {"type": "package", "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.7.250401001": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2903.40", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x86"}}}, "NLog/5.4.0": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.3.25171.5"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "WindowsInput/6.4.1": {"type": "package", "compile": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "runtime": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "contentFiles": {"contentFiles/any/net6.0-windows7.0/.nuget/mouse-keyboard-hook-logo.png": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}}, "net8.0-windows10.0.22621/win-arm64": {"CommunityToolkit.Common/8.2.1": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Web.WebView2/1.0.2903.40": {"type": "package", "native": {"runtimes/win-arm64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.7.250401001": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2903.40", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "NLog/5.4.0": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.3.25171.5"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "WindowsInput/6.4.1": {"type": "package", "compile": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "runtime": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "contentFiles": {"contentFiles/any/net6.0-windows7.0/.nuget/mouse-keyboard-hook-logo.png": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}}, "net8.0-windows10.0.22621/win-x64": {"CommunityToolkit.Common/8.2.1": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Web.WebView2/1.0.2903.40": {"type": "package", "native": {"runtimes/win-x64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.7.250401001": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2903.40", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "NLog/5.4.0": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.3.25171.5"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "WindowsInput/6.4.1": {"type": "package", "compile": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "runtime": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "contentFiles": {"contentFiles/any/net6.0-windows7.0/.nuget/mouse-keyboard-hook-logo.png": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}}, "net8.0-windows10.0.22621/win-x86": {"CommunityToolkit.Common/8.2.1": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Common.dll": {"related": ".pdb;.xml"}}}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"related": ".pdb;.pri;.xml"}}}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"type": "package", "dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.6.250108002"}, "compile": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Web.WebView2/1.0.2903.40": {"type": "package", "native": {"runtimes/win-x86/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.7.250401001": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.2903.40", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "NLog/5.4.0": {"type": "package", "compile": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NLog.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.3.25171.5"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "WindowsInput/6.4.1": {"type": "package", "compile": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "runtime": {"lib/net6.0-windows7.0/WindowsInput.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "contentFiles": {"contentFiles/any/net6.0-windows7.0/.nuget/mouse-keyboard-hook-logo.png": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}}}, "libraries": {"CommunityToolkit.Common/8.2.1": {"sha512": "LWuhy8cQKJ/MYcy3XafJ916U3gPH/YDvYoNGWyQWN11aiEKCZszzPOTJAOvBjP9yG8vHmIcCyPUt4L82OK47Iw==", "type": "package", "path": "communitytoolkit.common/8.2.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "communitytoolkit.common.8.2.1.nupkg.sha512", "communitytoolkit.common.nuspec", "lib/net6.0/CommunityToolkit.Common.dll", "lib/net6.0/CommunityToolkit.Common.pdb", "lib/net6.0/CommunityToolkit.Common.xml", "lib/netstandard2.0/CommunityToolkit.Common.dll", "lib/netstandard2.0/CommunityToolkit.Common.pdb", "lib/netstandard2.0/CommunityToolkit.Common.xml", "lib/netstandard2.1/CommunityToolkit.Common.dll", "lib/netstandard2.1/CommunityToolkit.Common.pdb", "lib/netstandard2.1/CommunityToolkit.Common.xml"]}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"sha512": "Wx3t1zADrzBWDar45uRl+lmSxDO5Vx7tTMFm/mNgl3fs5xSQ1ySPdGqD10EFov3rkKc5fbpHGW5xj8t62Yisvg==", "type": "package", "path": "communitytoolkit.winui.controls.primitives/8.2.250402", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "ReadMe.md", "communitytoolkit.winui.controls.primitives.8.2.250402.nupkg.sha512", "communitytoolkit.winui.controls.primitives.nuspec", "icon.png", "lib/net7.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.pri", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.xml", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Primitives.aar", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Primitives.xml", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Primitives.xml", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Primitives.xml", "lib/net9.0-windows10.0.18362/_._", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.pri", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.xml", "lib/net9.0/CommunityToolkit.WinUI.Controls.Primitives.dll", "lib/net9.0/CommunityToolkit.WinUI.Controls.Primitives.pdb", "lib/net9.0/CommunityToolkit.WinUI.Controls.Primitives.xml"]}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"sha512": "8kWJX+TxUyYbLFPpzoivtYPnSI9kgBGL1dfdsteQSw6Yq69v7mv/Z3emTLHqBJXOi9WKCiq2HNhKXuCH18/ctA==", "type": "package", "path": "communitytoolkit.winui.controls.sizers/8.2.250402", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "ReadMe.md", "communitytoolkit.winui.controls.sizers.8.2.250402.nupkg.sha512", "communitytoolkit.winui.controls.sizers.nuspec", "icon.png", "lib/net7.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.pri", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.xml", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Sizers.aar", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Controls.Sizers.xml", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Controls.Sizers.xml", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Controls.Sizers.xml", "lib/net9.0-windows10.0.18362/_._", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.pri", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.xml", "lib/net9.0/CommunityToolkit.WinUI.Controls.Sizers.dll", "lib/net9.0/CommunityToolkit.WinUI.Controls.Sizers.pdb", "lib/net9.0/CommunityToolkit.WinUI.Controls.Sizers.xml"]}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"sha512": "rAOYzNX6kdUeeE1ejGd6Q8B+xmyZvOrWFUbqCgOtP8OQsOL66en9ZQTtzxAlaaFC4qleLvnKcn8FJFBezujOlw==", "type": "package", "path": "communitytoolkit.winui.extensions/8.2.250402", "files": [".nupkg.metadata", ".signature.p7s", "License.md", "ReadMe.md", "communitytoolkit.winui.extensions.8.2.250402.nupkg.sha512", "communitytoolkit.winui.extensions.nuspec", "icon.png", "lib/net7.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.18362/_._", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.pdb", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.pri", "lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.xml", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Extensions.aar", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Extensions.dll", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Extensions.pdb", "lib/net9.0-android35.0/CommunityToolkit.WinUI.Extensions.xml", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Extensions.dll", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Extensions.pdb", "lib/net9.0-ios18.0/CommunityToolkit.WinUI.Extensions.xml", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Extensions.dll", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Extensions.pdb", "lib/net9.0-maccatalyst18.0/CommunityToolkit.WinUI.Extensions.xml", "lib/net9.0-windows10.0.18362/_._", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.pdb", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.pri", "lib/net9.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.xml", "lib/net9.0/CommunityToolkit.WinUI.Extensions.dll", "lib/net9.0/CommunityToolkit.WinUI.Extensions.pdb", "lib/net9.0/CommunityToolkit.WinUI.Extensions.xml"]}, "Microsoft.Web.WebView2/1.0.2903.40": {"sha512": "THrzYAnJgE3+cNH+9Epr44XjoZoRELdVpXlWGPs6K9C9G6TqyDfVCeVAR/Er8ljLitIUX5gaSkPsy9wRhD1sgQ==", "type": "package", "path": "microsoft.web.webview2/1.0.2903.40", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "buildTransitive/Microsoft.Web.WebView2.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net6.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/net8.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.2903.40.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"sha512": "ypcHjr4KEi6xQhgClnbXoANHcyyX/QsC4Rky4igs6M4GiDa+weegPo8JuV/VMxqrZCV4zlqDsp2krgkN7ReAAg==", "type": "package", "path": "microsoft.windows.sdk.buildtools/10.0.26100.1742", "files": [".nupkg.metadata", ".signature.p7s", "bin/10.0.26100.0/arm64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/ComparePackage.exe", "bin/10.0.26100.0/arm64/DeployUtil.exe", "bin/10.0.26100.0/arm64/MakeCert.exe", "bin/10.0.26100.0/arm64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/arm64/PackageEditor.exe", "bin/10.0.26100.0/arm64/ServicingCommon.dll", "bin/10.0.26100.0/arm64/SirepClient.assembly.manifest", "bin/10.0.26100.0/arm64/SirepClient.dll", "bin/10.0.26100.0/arm64/SirepInterop.dll", "bin/10.0.26100.0/arm64/SshClient.dll", "bin/10.0.26100.0/arm64/WinAppDeployCmd.exe", "bin/10.0.26100.0/arm64/WinAppDeployCommon.dll", "bin/10.0.26100.0/arm64/appxpackaging.dll", "bin/10.0.26100.0/arm64/appxsip.dll", "bin/10.0.26100.0/arm64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/arm64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/arm64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/arm64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/arm64/makeappx.exe", "bin/10.0.26100.0/arm64/makecat.exe", "bin/10.0.26100.0/arm64/makecat.exe.manifest", "bin/10.0.26100.0/arm64/makepri.exe", "bin/10.0.26100.0/arm64/mc.exe", "bin/10.0.26100.0/arm64/mdmerge.exe", "bin/10.0.26100.0/arm64/midl.exe", "bin/10.0.26100.0/arm64/midlc.exe", "bin/10.0.26100.0/arm64/midlrt.exe", "bin/10.0.26100.0/arm64/midlrtmd.dll", "bin/10.0.26100.0/arm64/mrmsupport.dll", "bin/10.0.26100.0/arm64/msisip.dll", "bin/10.0.26100.0/arm64/mssign32.dll", "bin/10.0.26100.0/arm64/mt.exe", "bin/10.0.26100.0/arm64/mt.exe.config", "bin/10.0.26100.0/arm64/opcservices.dll", "bin/10.0.26100.0/arm64/rc.exe", "bin/10.0.26100.0/arm64/rcdll.dll", "bin/10.0.26100.0/arm64/signtool.exe", "bin/10.0.26100.0/arm64/signtool.exe.manifest", "bin/10.0.26100.0/arm64/tracewpp.exe", "bin/10.0.26100.0/arm64/uuidgen.exe", "bin/10.0.26100.0/arm64/veiid.exe", "bin/10.0.26100.0/arm64/winmdidl.exe", "bin/10.0.26100.0/arm64/wintrust.dll", "bin/10.0.26100.0/arm64/wintrust.dll.ini", "bin/10.0.26100.0/x64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/ComparePackage.exe", "bin/10.0.26100.0/x64/DeployUtil.exe", "bin/10.0.26100.0/x64/MakeCert.exe", "bin/10.0.26100.0/x64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x64/PackageEditor.exe", "bin/10.0.26100.0/x64/ServicingCommon.dll", "bin/10.0.26100.0/x64/SirepClient.assembly.manifest", "bin/10.0.26100.0/x64/SirepClient.dll", "bin/10.0.26100.0/x64/SirepInterop.dll", "bin/10.0.26100.0/x64/SshClient.dll", "bin/10.0.26100.0/x64/WinAppDeployCmd.exe", "bin/10.0.26100.0/x64/WinAppDeployCommon.dll", "bin/10.0.26100.0/x64/appxpackaging.dll", "bin/10.0.26100.0/x64/appxsip.dll", "bin/10.0.26100.0/x64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x64/makeappx.exe", "bin/10.0.26100.0/x64/makecat.exe", "bin/10.0.26100.0/x64/makecat.exe.manifest", "bin/10.0.26100.0/x64/makepri.exe", "bin/10.0.26100.0/x64/mc.exe", "bin/10.0.26100.0/x64/mdmerge.exe", "bin/10.0.26100.0/x64/midl.exe", "bin/10.0.26100.0/x64/midlc.exe", "bin/10.0.26100.0/x64/midlrt.exe", "bin/10.0.26100.0/x64/midlrtmd.dll", "bin/10.0.26100.0/x64/mrmsupport.dll", "bin/10.0.26100.0/x64/msisip.dll", "bin/10.0.26100.0/x64/mssign32.dll", "bin/10.0.26100.0/x64/mt.exe", "bin/10.0.26100.0/x64/mt.exe.config", "bin/10.0.26100.0/x64/opcservices.dll", "bin/10.0.26100.0/x64/rc.exe", "bin/10.0.26100.0/x64/rcdll.dll", "bin/10.0.26100.0/x64/signtool.exe", "bin/10.0.26100.0/x64/signtool.exe.manifest", "bin/10.0.26100.0/x64/tracewpp.exe", "bin/10.0.26100.0/x64/uuidgen.exe", "bin/10.0.26100.0/x64/veiid.exe", "bin/10.0.26100.0/x64/winmdidl.exe", "bin/10.0.26100.0/x64/wintrust.dll", "bin/10.0.26100.0/x64/wintrust.dll.ini", "bin/10.0.26100.0/x86/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/ComparePackage.exe", "bin/10.0.26100.0/x86/DeployUtil.exe", "bin/10.0.26100.0/x86/MakeCert.exe", "bin/10.0.26100.0/x86/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x86/PackageEditor.exe", "bin/10.0.26100.0/x86/ServicingCommon.dll", "bin/10.0.26100.0/x86/SirepClient.assembly.manifest", "bin/10.0.26100.0/x86/SirepClient.dll", "bin/10.0.26100.0/x86/SirepInterop.dll", "bin/10.0.26100.0/x86/SshClient.dll", "bin/10.0.26100.0/x86/WinAppDeployCmd.exe", "bin/10.0.26100.0/x86/WinAppDeployCommon.dll", "bin/10.0.26100.0/x86/appxpackaging.dll", "bin/10.0.26100.0/x86/appxsip.dll", "bin/10.0.26100.0/x86/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x86/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x86/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x86/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x86/makeappx.exe", "bin/10.0.26100.0/x86/makecat.exe", "bin/10.0.26100.0/x86/makecat.exe.manifest", "bin/10.0.26100.0/x86/makepri.exe", "bin/10.0.26100.0/x86/mc.exe", "bin/10.0.26100.0/x86/mdmerge.exe", "bin/10.0.26100.0/x86/midl.exe", "bin/10.0.26100.0/x86/midlc.exe", "bin/10.0.26100.0/x86/midlrt.exe", "bin/10.0.26100.0/x86/midlrtmd.dll", "bin/10.0.26100.0/x86/mrmsupport.dll", "bin/10.0.26100.0/x86/msisip.dll", "bin/10.0.26100.0/x86/mssign32.dll", "bin/10.0.26100.0/x86/mt.exe", "bin/10.0.26100.0/x86/mt.exe.config", "bin/10.0.26100.0/x86/opcservices.dll", "bin/10.0.26100.0/x86/rc.exe", "bin/10.0.26100.0/x86/rcdll.dll", "bin/10.0.26100.0/x86/signtool.exe", "bin/10.0.26100.0/x86/signtool.exe.manifest", "bin/10.0.26100.0/x86/tracewpp.exe", "bin/10.0.26100.0/x86/uuidgen.exe", "bin/10.0.26100.0/x86/winmdidl.exe", "bin/10.0.26100.0/x86/wintrust.dll", "bin/10.0.26100.0/x86/wintrust.dll.ini", "build/Microsoft.Windows.SDK.BuildTools.props", "build/Microsoft.Windows.SDK.BuildTools.targets", "buildTransitive/Microsoft.Windows.SDK.BuildTools.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets", "microsoft.windows.sdk.buildtools.10.0.26100.1742.nupkg.sha512", "microsoft.windows.sdk.buildtools.nuspec", "schemas/10.0.26100.0/winrt/AppxManifestSchema.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2010_v2.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2013.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v10.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v11.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v12.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v13.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15a.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v16.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v17.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v3.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v4.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v5.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v6.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v7.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v8.xsd"]}, "Microsoft.WindowsAppSDK/1.7.250401001": {"sha512": "kPsJ2LZoo3Xs/6FtIWMZRGnQ2ZMx9zDa0ZpqRGz1qwZr0gwwlXZJTmngaA1Ym2AHmIa05NtX2jEE2He8CzfhTg==", "type": "package", "path": "microsoft.windowsappsdk/1.7.250401001", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "WindowsAppSDK-VersionInfo.json", "WindowsAppSDK-VersionInfo.xml", "build/AppDevPackageScripts/Add-AppDevPackage.ps1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Install.ps1", "build/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "build/Landing/extras/br.png", "build/Landing/extras/br_snippet.png", "build/Landing/image.png", "build/Landing/index.template.html", "build/Landing/logo.png", "build/Landing/style.css", "build/Microsoft.Build.Msix.Common.props", "build/Microsoft.Build.Msix.Cpp.props", "build/Microsoft.Build.Msix.Cpp.targets", "build/Microsoft.Build.Msix.Cs.targets", "build/Microsoft.Build.Msix.DesignTime.targets", "build/Microsoft.Build.Msix.Packaging.targets", "build/Microsoft.Build.Msix.Pri.targets", "build/Microsoft.Build.Msix.props", "build/Microsoft.Build.Msix.targets", "build/Microsoft.InteractiveExperiences.Capabilities.props", "build/Microsoft.InteractiveExperiences.Capabilities.targets", "build/Microsoft.InteractiveExperiences.Common.props", "build/Microsoft.InteractiveExperiences.Common.targets", "build/Microsoft.InteractiveExperiences.EC.Capabilities.props", "build/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "build/Microsoft.InteractiveExperiences.EC.Common.props", "build/Microsoft.InteractiveExperiences.EC.Common.targets", "build/Microsoft.InteractiveExperiences.EC.props", "build/Microsoft.InteractiveExperiences.EC.targets", "build/Microsoft.InteractiveExperiences.props", "build/Microsoft.InteractiveExperiences.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.props", "build/Microsoft.UI.Xaml.Markup.Compiler.targets", "build/Microsoft.WinUI.AppX.targets", "build/Microsoft.WinUI.NET.Markup.Compiler.targets", "build/Microsoft.WinUI.ProjectCapabilities.props", "build/Microsoft.WinUI.References.targets", "build/Microsoft.WinUI.props", "build/Microsoft.WinUI.targets", "build/Microsoft.WindowsAppSDK.AppXReference.props", "build/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "build/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "build/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "build/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "build/Microsoft.WindowsAppSDK.Common.props", "build/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "build/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "build/Microsoft.WindowsAppSDK.DWrite.props", "build/Microsoft.WindowsAppSDK.DWrite.targets", "build/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "build/Microsoft.WindowsAppSDK.Foundation.props", "build/Microsoft.WindowsAppSDK.Foundation.targets", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/Microsoft.WindowsAppSDK.Metapackage.props", "build/Microsoft.WindowsAppSDK.SelfContained.targets", "build/Microsoft.WindowsAppSDK.SingleFile.targets", "build/Microsoft.WindowsAppSDK.SingleProject.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "build/Microsoft.WindowsAppSDK.Widgets.targets", "build/Microsoft.WindowsAppSDK.WinUI.props", "build/Microsoft.WindowsAppSDK.WinUI.targets", "build/Microsoft.WindowsAppSDK.arm64ec.targets", "build/Microsoft.WindowsAppSDK.props", "build/Microsoft.WindowsAppSDK.targets", "build/Microsoft.Xaml.Tooling.targets", "build/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "build/MrtCore.PriGen.targets", "build/MrtCore.References.targets", "build/MrtCore.props", "build/MrtCore.targets", "build/ProjectItemsSchema.xaml", "build/README.md", "build/Rules/MsixPackageDebugPropertyPage.xaml", "build/Rules/WindowsPackageTypePropertyPage.xaml", "build/Templates/Package.appinstaller", "build/native/LiftedWinRTClassRegistrations.xml", "build/native/Microsoft.InteractiveExperiences.EC.props", "build/native/Microsoft.InteractiveExperiences.EC.targets", "build/native/Microsoft.InteractiveExperiences.props", "build/native/Microsoft.InteractiveExperiences.targets", "build/native/Microsoft.WinUI.References.targets", "build/native/Microsoft.WinUI.props", "build/native/Microsoft.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.Foundation.props", "build/native/Microsoft.WindowsAppSDK.Foundation.targets", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/native/Microsoft.WindowsAppSDK.Widgets.targets", "build/native/Microsoft.WindowsAppSDK.WinUI.props", "build/native/Microsoft.WindowsAppSDK.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.props", "build/native/Microsoft.WindowsAppSDK.targets", "build/native/MrtCore.C.props", "build/native/MrtCore.props", "build/native/MrtCore.targets", "build/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "build/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "build/native/WindowsAppSDK-Nuget-Native.C.props", "build/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "build/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "build/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "build/native/WindowsAppSDK-Nuget-Native.WinRt.props", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.ps1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Install.ps1", "buildTransitive/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "buildTransitive/Landing/extras/br.png", "buildTransitive/Landing/extras/br_snippet.png", "buildTransitive/Landing/image.png", "buildTransitive/Landing/index.template.html", "buildTransitive/Landing/logo.png", "buildTransitive/Landing/style.css", "buildTransitive/Microsoft.Build.Msix.Common.props", "buildTransitive/Microsoft.Build.Msix.Cpp.props", "buildTransitive/Microsoft.Build.Msix.Cpp.targets", "buildTransitive/Microsoft.Build.Msix.Cs.targets", "buildTransitive/Microsoft.Build.Msix.DesignTime.targets", "buildTransitive/Microsoft.Build.Msix.Packaging.targets", "buildTransitive/Microsoft.Build.Msix.Pri.targets", "buildTransitive/Microsoft.Build.Msix.props", "buildTransitive/Microsoft.Build.Msix.targets", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/Microsoft.InteractiveExperiences.props", "buildTransitive/Microsoft.InteractiveExperiences.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.props", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.AppX.targets", "buildTransitive/Microsoft.WinUI.NET.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.ProjectCapabilities.props", "buildTransitive/Microsoft.WinUI.References.targets", "buildTransitive/Microsoft.WinUI.props", "buildTransitive/Microsoft.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.AppXReference.props", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Common.props", "buildTransitive/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/Microsoft.WindowsAppSDK.Metapackage.props", "buildTransitive/Microsoft.WindowsAppSDK.SelfContained.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleFile.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleProject.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.arm64ec.targets", "buildTransitive/Microsoft.WindowsAppSDK.props", "buildTransitive/Microsoft.WindowsAppSDK.targets", "buildTransitive/Microsoft.Xaml.Tooling.targets", "buildTransitive/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "buildTransitive/MrtCore.PriGen.targets", "buildTransitive/MrtCore.References.targets", "buildTransitive/MrtCore.props", "buildTransitive/MrtCore.targets", "buildTransitive/ProjectItemsSchema.xaml", "buildTransitive/README.md", "buildTransitive/Rules/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/WindowsPackageTypePropertyPage.xaml", "buildTransitive/Templates/Package.appinstaller", "buildTransitive/native/LiftedWinRTClassRegistrations.xml", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/native/Microsoft.InteractiveExperiences.props", "buildTransitive/native/Microsoft.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WinUI.References.targets", "buildTransitive/native/Microsoft.WinUI.props", "buildTransitive/native/Microsoft.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.props", "buildTransitive/native/Microsoft.WindowsAppSDK.targets", "buildTransitive/native/MrtCore.C.props", "buildTransitive/native/MrtCore.props", "buildTransitive/native/MrtCore.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.C.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.WinRt.props", "include/DeploymentManagerAutoInitializer.cpp", "include/DeploymentManagerAutoInitializer.cs", "include/MRM.h", "include/MddBootstrap.h", "include/MddBootstrapAutoInitializer.cpp", "include/MddBootstrapAutoInitializer.cs", "include/Microsoft.UI.Composition.Interop.h", "include/Microsoft.UI.Dispatching.Interop.h", "include/Microsoft.UI.Input.InputCursor.Interop.h", "include/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/Microsoft.UI.Interop.h", "include/Microsoft.Windows.ApplicationModel.Resources.idl", "include/MsixDynamicDependency.h", "include/Security.AccessControl.h", "include/UndockedRegFreeWinRT-AutoInitializer.cpp", "include/UndockedRegFreeWinRT-AutoInitializer.cs", "include/WindowsAppRuntimeAutoInitializer.cpp", "include/WindowsAppRuntimeAutoInitializer.cs", "include/WindowsAppRuntimeInsights.h", "include/WindowsAppSDK-VersionInfo.cs", "include/WindowsAppSDK-VersionInfo.h", "include/dwrite.h", "include/dwrite_1.h", "include/dwrite_2.h", "include/dwrite_3.h", "include/dwrite_core.h", "include/microsoft.ui.xaml.hosting.referencetracker.h", "include/microsoft.ui.xaml.hosting.referencetracker.idl", "include/microsoft.ui.xaml.media.dxinterop.h", "include/microsoft.ui.xaml.media.dxinterop.idl", "include/microsoft.ui.xaml.window.h", "include/microsoft.ui.xaml.window.idl", "include/wil_msixdynamicdependency.h", "include/winrt/Microsoft.UI.Composition.Interop.h", "include/winrt/Microsoft.UI.Dispatching.Interop.h", "include/winrt/Microsoft.UI.Input.InputCursor.Interop.h", "include/winrt/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/winrt/Microsoft.UI.Interop.h", "include/winrtdirect3d11.h", "include/winrtdirectxcommon.h", "include/xamlom.winui.h", "include/xamlom.winui.idl", "lib/native/win10-arm64/Microsoft.UI.Dispatching.lib", "lib/native/win10-arm64ec/Microsoft.UI.Dispatching.lib", "lib/native/win10-x64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x86/Microsoft.UI.Dispatching.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Security.Authentication.OAuth.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.BadgeNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Media.Capture.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.xml", "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/uap10.0.17763/Microsoft.Foundation.winmd", "lib/uap10.0.17763/Microsoft.Graphics.winmd", "lib/uap10.0.17763/Microsoft.Graphics.xml", "lib/uap10.0.17763/Microsoft.UI.winmd", "lib/uap10.0.17763/Microsoft.UI.xml", "lib/uap10.0.18362/Microsoft.Foundation.winmd", "lib/uap10.0.18362/Microsoft.Graphics.winmd", "lib/uap10.0.18362/Microsoft.Graphics.xml", "lib/uap10.0.18362/Microsoft.UI.winmd", "lib/uap10.0.18362/Microsoft.UI.xml", "lib/uap10.0/Microsoft.Security.Authentication.OAuth.winmd", "lib/uap10.0/Microsoft.UI.Text.winmd", "lib/uap10.0/Microsoft.UI.Text.xml", "lib/uap10.0/Microsoft.UI.Xaml.winmd", "lib/uap10.0/Microsoft.UI.Xaml.xml", "lib/uap10.0/Microsoft.UI/Themes/generic.xaml", "lib/uap10.0/Microsoft.Windows.AppLifecycle.winmd", "lib/uap10.0/Microsoft.Windows.AppLifecycle.xml", "lib/uap10.0/Microsoft.Windows.AppNotifications.Builder.winmd", "lib/uap10.0/Microsoft.Windows.AppNotifications.Builder.xml", "lib/uap10.0/Microsoft.Windows.AppNotifications.winmd", "lib/uap10.0/Microsoft.Windows.AppNotifications.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Background.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.xml", "lib/uap10.0/Microsoft.Windows.BadgeNotifications.winmd", "lib/uap10.0/Microsoft.Windows.Globalization.winmd", "lib/uap10.0/Microsoft.Windows.Globalization.xml", "lib/uap10.0/Microsoft.Windows.Management.Deployment.winmd", "lib/uap10.0/Microsoft.Windows.Management.Deployment.xml", "lib/uap10.0/Microsoft.Windows.Media.Capture.winmd", "lib/uap10.0/Microsoft.Windows.PushNotifications.winmd", "lib/uap10.0/Microsoft.Windows.PushNotifications.xml", "lib/uap10.0/Microsoft.Windows.Security.AccessControl.winmd", "lib/uap10.0/Microsoft.Windows.Security.AccessControl.xml", "lib/uap10.0/Microsoft.Windows.Storage.winmd", "lib/uap10.0/Microsoft.Windows.Storage.xml", "lib/uap10.0/Microsoft.Windows.System.Power.winmd", "lib/uap10.0/Microsoft.Windows.System.Power.xml", "lib/uap10.0/Microsoft.Windows.System.winmd", "lib/uap10.0/Microsoft.Windows.System.xml", "lib/uap10.0/Microsoft.Windows.Widgets.winmd", "lib/uap10.0/Microsoft.Windows.Widgets.xml", "lib/win10-arm64/DWriteCore.lib", "lib/win10-arm64/MRM.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.lib", "lib/win10-arm64ec/DWriteCore.lib", "lib/win10-arm64ec/MRM.lib", "lib/win10-arm64ec/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-arm64ec/Microsoft.WindowsAppRuntime.lib", "lib/win10-x64/DWriteCore.lib", "lib/win10-x64/MRM.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.lib", "lib/win10-x86/DWriteCore.lib", "lib/win10-x86/MRM.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.lib", "license.txt", "manifests/Microsoft.InteractiveExperiences.manifest", "manifests/Microsoft.WindowsAppSdk.Foundation.manifest", "manifests/manifests/Microsoft.WindowsAppSdk.WinUI.manifest", "microsoft.windowsappsdk.1.7.250401001.nupkg.sha512", "microsoft.windowsappsdk.nuspec", "runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "tools/MSIX/win10-arm64/MSIX.inventory", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.1.7.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.DDLM.1.7.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Main.1.7.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Singleton.1.7.msix", "tools/MSIX/win10-arm64ec/MSIX.inventory", "tools/MSIX/win10-arm64ec/Microsoft.WindowsAppRuntime.1.7.msix", "tools/MSIX/win10-x64/MSIX.inventory", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.1.7.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.DDLM.1.7.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Main.1.7.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Singleton.1.7.msix", "tools/MSIX/win10-x86/MSIX.inventory", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.1.7.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.DDLM.1.7.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Main.1.7.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Singleton.1.7.msix", "tools/NOTICE.txt", "tools/arm64/GenXbf.dll", "tools/arm64ec/GenXbf.dll", "tools/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net472/Microsoft.Build.Framework.dll", "tools/net472/Microsoft.Build.Msix.dll", "tools/net472/Microsoft.Build.Utilities.Core.dll", "tools/net472/Microsoft.Build.dll", "tools/net472/Microsoft.Cci.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net472/Microsoft.VisualStudio.RemoteControl.dll", "tools/net472/Microsoft.VisualStudio.Telemetry.dll", "tools/net472/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/System.Text.Encodings.Web.dll", "tools/net472/System.Text.Json.dll", "tools/net472/System.Threading.Tasks.Dataflow.dll", "tools/net472/System.Threading.Tasks.Extensions.dll", "tools/net472/XamlCompiler.exe", "tools/net472/XamlCompiler.exe.config", "tools/net6.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/Microsoft.Build.Msix.dll", "tools/net6.0/Microsoft.Cci.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net6.0/Microsoft.VisualStudio.RemoteControl.dll", "tools/net6.0/Microsoft.VisualStudio.Telemetry.dll", "tools/net6.0/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net6.0/Newtonsoft.Json.dll", "tools/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/net6.0/System.Text.Encodings.Web.dll", "tools/net6.0/System.Text.Json.dll", "tools/x64/GenXbf.dll", "tools/x86/GenXbf.dll"]}, "NLog/5.4.0": {"sha512": "LwMcGSW3soF3/SL68rlJN3Eh3ktrAPycC3zZR/07OYBPraZUu0bygEC7kIN10lUQgMXT4s84Fi1chglGdGrQEg==", "type": "package", "path": "nlog/5.4.0", "files": [".nupkg.metadata", ".signature.p7s", "N.png", "lib/net35/NLog.dll", "lib/net35/NLog.xml", "lib/net45/NLog.dll", "lib/net45/NLog.xml", "lib/net46/NLog.dll", "lib/net46/NLog.xml", "lib/netstandard1.3/NLog.dll", "lib/netstandard1.3/NLog.xml", "lib/netstandard1.5/NLog.dll", "lib/netstandard1.5/NLog.xml", "lib/netstandard2.0/NLog.dll", "lib/netstandard2.0/NLog.xml", "nlog.5.4.0.nupkg.sha512", "nlog.nuspec"]}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"sha512": "rpAIQlaC6r04B8nD//rIDEVdQMAKCeAN9ypDx34gS77Q4ET9+sE+4ApziGvGv5Ve50bT5PoJAt/29JT9LmRhTA==", "type": "package", "path": "system.configuration.configurationmanager/10.0.0-preview.3.25171.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net10.0/System.Configuration.ConfigurationManager.dll", "lib/net10.0/System.Configuration.ConfigurationManager.xml", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.10.0.0-preview.3.25171.5.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"sha512": "ABk8eOWyQ9Pgmz63EE9hmj0G0eLnWItwM9NV0OGMUOEdseVN1PeA1ryQUIQr5C1odDG2ip4oBTLGSrb5+Z2S3w==", "type": "package", "path": "system.diagnostics.eventlog/10.0.0-preview.3.25171.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net10.0/System.Diagnostics.EventLog.dll", "lib/net10.0/System.Diagnostics.EventLog.xml", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.10.0.0-preview.3.25171.5.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"sha512": "qsIqM920BE+fPW5RDNUPx2fx3Uk6ZCi7UiUqpGKIWUdt8aTCxdemeh4NaWFQhwd61JDnUsfNFeY8yMYbNzS20g==", "type": "package", "path": "system.security.cryptography.protecteddata/10.0.0-preview.3.25171.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net10.0/System.Security.Cryptography.ProtectedData.dll", "lib/net10.0/System.Security.Cryptography.ProtectedData.xml", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.10.0.0-preview.3.25171.5.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "WindowsInput/6.4.1": {"sha512": "wfg9uu5xDvBcaNqvR1ZTFQqBf4bKzkxdd5WviTZPy4cAywEzLzofuYswZB3nGIEnnlwGLBGSgrNOEZH/w8JI9w==", "type": "package", "path": "windowsinput/6.4.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "content/.nuget/mouse-keyboard-hook-logo.png", "contentFiles/any/net461/.nuget/mouse-keyboard-hook-logo.png", "contentFiles/any/net6.0-windows7.0/.nuget/mouse-keyboard-hook-logo.png", "contentFiles/any/netcoreapp3.1/.nuget/mouse-keyboard-hook-logo.png", "lib/net461/WindowsInput.dll", "lib/net6.0-windows7.0/WindowsInput.dll", "lib/netcoreapp3.1/WindowsInput.dll", "mouse-keyboard-hook-logo.png", "windowsinput.6.4.1.nupkg.sha512", "windowsinput.nuspec"]}}, "projectFileDependencyGroups": {"net8.0-windows10.0.22621": ["CommunityToolkit.WinUI.Controls.Primitives >= 8.2.250402", "CommunityToolkit.WinUI.Controls.Sizers >= 8.2.250402", "CommunityToolkit.WinUI.Extensions >= 8.2.250402", "Microsoft.Windows.SDK.BuildTools >= 10.0.26100.1742", "Microsoft.WindowsAppSDK >= 1.7.250401001", "NLog >= 5.4.0", "System.Configuration.ConfigurationManager >= 10.0.0-preview.3.25171.5", "WindowsInput >= 6.4.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "d:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\netProject\\newhddtodoui\\hddtodoUI\\HddtodoUI.csproj", "projectName": "HddtodoUI", "projectPath": "D:\\netProject\\newhddtodoui\\hddtodoUI\\HddtodoUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\netProject\\newhddtodoui\\hddtodoUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows10.0.22621.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.22621": {"targetAlias": "net8.0-windows10.0.22621.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows10.0.22621": {"targetAlias": "net8.0-windows10.0.22621.0", "dependencies": {"CommunityToolkit.WinUI.Controls.Primitives": {"target": "Package", "version": "[8.2.250402, )"}, "CommunityToolkit.WinUI.Controls.Sizers": {"target": "Package", "version": "[8.2.250402, )"}, "CommunityToolkit.WinUI.Extensions": {"target": "Package", "version": "[8.2.250402, )"}, "Microsoft.Windows.SDK.BuildTools": {"target": "Package", "version": "[10.0.26100.1742, )"}, "Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.7.250401001, )"}, "NLog": {"target": "Package", "version": "[5.4.0, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[10.0.0-preview.3.25171.5, )"}, "WindowsInput": {"target": "Package", "version": "[6.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-arm64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-arm64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.26100.57, 10.0.26100.57]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-arm64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}