using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.BackendStore.HttpStore;
using HddtodoUI.BackendModels.BackendStore.HttpStore.cached;
using HddtodoUI.BackendModels.BackendStore.HttpStore.HddtodoUI.BackendModels.BackendStore.HttpStore;

namespace HddtodoUI.BackendModels.StoreFactory;

public class HttpStoreFactory:IStoreFactory
{
   
    private static ITaskStore _taskStore = new HttpTaskStore();
    private static ITaskListStore _taskListStore = new HttpTaskListStore();
    private static ITaskListStore _taskListCacheStore = new CachedTaskListStore(_taskListStore);
    private static ITaskTimeLogStore _taskTimeLogStore = new HttpTaskTimeLogStore();
    private static IUserStore _userStore = new HttpUserStore();
    private static ITaskStepStore _taskStepStore = new HttpTaskStepStore();
    private static ITaskReminderStore _taskReminderStore = new HttpTaskReminderStore();
    private static ITaskRestartStore _taskRestartStore = new HttpTaskRestartStore();
    private static ITaskCategoryStore _taskCategoryStore = new HttpTaskCategoryStore();
    private static ITaskCategoryStore _taskCategoryCachedStore = new CachedTaskCategoryStore(_taskCategoryStore);
    private static ITaskActivityStore _taskActivityStore = new TaskActivityHttpStore();
   
    public ITaskStore getTaskStore()
    {
        return _taskStore;
    }

    public ITaskListStore getTaskListStore()
    {
        return _taskListCacheStore;
    }

    public ITaskTimeLogStore getTaskTimeLogStore()
    {
       return _taskTimeLogStore;
    }

    public ITaskStepStore getTaskStepStore()
    {
        return _taskStepStore;
    }

    public IUserStore getUserStore()
    {
        return _userStore;
    }
    
    public ITaskReminderStore getTaskReminderStore()
    {
        return _taskReminderStore;
    }
    
    public ITaskRestartStore getTaskRestartStore()
    {
        return _taskRestartStore;
    }

    public ITaskCategoryStore getTaskCategoryStore()
    {
        return _taskCategoryCachedStore;
    }

    public ITaskActivityStore getTaskActivityStore()
    {
       return  _taskActivityStore;
    }
}