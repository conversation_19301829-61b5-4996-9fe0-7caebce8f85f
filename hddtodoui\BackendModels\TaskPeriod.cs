using System.Text.Json.Serialization;

// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期格式请在枚举处使用 JsonPropertyName(全大写) 或放在 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致。
// 3. 若需定制序列化逻辑，仅在此文件或 JsonConverters 中写一次，避免重复。
// 4. 其它文件只使用现有 Converter / 属性注解.

namespace HddtodoUI.BackendModels
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum TaskPeriod
    {
        [JsonPropertyName("NONE")]
        none = 0,
        [Json<PERSON>ropertyName("DAILY")]
        daily = 1,
        [JsonPropertyName("WEEKLY")]
        weekly = 2,
        [JsonPropertyName("MONTHLY")]
        monthly = 3,
        [JsonPropertyName("YEARLY")]
        yearly = 4,
        [JsonPropertyName("HOUR")]
        hour = 5
    }
}