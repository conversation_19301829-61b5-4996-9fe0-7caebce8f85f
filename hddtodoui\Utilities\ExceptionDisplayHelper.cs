using System;

namespace HddtodoUI.Utilities;

public static class ExceptionDisplayHelper
{
    public static string GetFullExceptionDetails(Exception ex)
    {
        var details = new System.Text.StringBuilder();
        details.AppendLine("异常信息：");
        details.AppendLine($"消息: {ex.Message}");
        details.AppendLine($"堆栈跟踪: {ex.StackTrace}");

        // 如果有内部异常，递归获取详细信息
        if (ex.InnerException != null)
        {
            details.AppendLine("内部异常：");
            details.AppendLine(GetFullExceptionDetails(ex.InnerException));
        }

        // 其他可能的属性
        details.AppendLine($"异常类型: {ex.GetType().FullName}");
        details.AppendLine($"来源: {ex.Source}");
        if (ex.Data.Count > 0)
        {
            details.AppendLine("附加数据：");
            foreach (System.Collections.DictionaryEntry entry in ex.Data)
            {
                details.AppendLine($"  {entry.Key}: {entry.Value}");
            }
        }

        return details.ToString();
    }
}