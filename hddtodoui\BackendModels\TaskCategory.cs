using System;
// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期格式请在枚举处使用 JsonPropertyName(全大写) 或放在 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，保持大小写/拼写。
// 3. 日期统一使用 yyyy-MM-dd'T'HH:mm:ss.fffzzz 格式；使用统一的JsonConverter。
// 4. 若需定制序列化逻辑，仅在此文件或 JsonConverters 中写一次，避免重复。
// 5. 其它文件只使用现有 Converter / 属性注解.

using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels
{
    public class TaskCategory
    {
        [JsonPropertyName("key")]
        public string Key { get; set; }
        
        [JsonPropertyName("categoryOrder")]
        public long CategoryOrder { get; set; }
        
        [JsonPropertyName("parentCategoryKey")]
        public string ParentCategoryKey { get; set; }
        
        [JsonPropertyName("userId")]
        public long UserId { get; set; }
        
        [JsonPropertyName("name")]
        public string Name { get; set; }
        
        [JsonPropertyName("categoryCreateTime")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime CategoryCreateTime { get; set; }

        [JsonPropertyName("categoryCompleteTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? CategoryCompleteTime { get; set; }

        [JsonPropertyName("categoryDueTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? CategoryDueTime { get; set; }
        
        [JsonPropertyName("isHide")]
        public bool IsHide { get; set; }
        
        [JsonPropertyName("isDeleted")]
        public bool IsDeleted { get; set; }
        
        [JsonPropertyName("deletedTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? DeletedTime { get; set; }
        
        [JsonPropertyName("hasChildren")]
        public bool HasChildren { get; set; }

        // 无参构造函数用于JSON反序列化
        public TaskCategory()
        {
        }
        
        public TaskCategory(string name, string key, long userId, string parentCategoryKey = null)
        {
            this.Name = name;
            this.Key = key;
            this.UserId = userId;
            this.ParentCategoryKey = parentCategoryKey;
            this.CategoryCreateTime = DateTime.Now;
            this.IsHide = false;
            this.IsDeleted = false;
            this.HasChildren = false;
        }
        
        public bool IsCompleted()
        {
            if (this.CategoryCompleteTime == null)
                return false;
            return true;
        }

        public void MakeMeCompleteNow()
        {
            CategoryCompleteTime = DateTime.Now;
        }

        public void SetDueTime(DateTime dueTime)
        {
            CategoryDueTime = dueTime;
        }
        
        public void Delete()
        {
            IsDeleted = true;
            DeletedTime = DateTime.Now;
        }
        
        public void Restore()
        {
            IsDeleted = false;
            DeletedTime = null;
        }
    }
}
