﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.SettingsDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="16">
        <Grid.Resources>
            <Style x:Key="SettingCheckBoxStyle" TargetType="CheckBox">
                <Setter Property="Margin" Value="0,8,0,8"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>
        </Grid.Resources>
        
        <StackPanel Spacing="16">
        
            
            <CheckBox x:ConnectionId='2' x:Name="AutoHideMainFormCheckBox" 
                      Content="任务开始时自动隐藏主界面" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <CheckBox x:ConnectionId='3' x:Name="TaskStartAutoShowTaskDetailCheckBox" 
                      Content="任务开始时自动显示该任务细节界面" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <CheckBox x:ConnectionId='4' x:Name="TaskPauseAutoSwitchCheckBox" 
                      Content="任务暂停时自动切回到任务选择界面" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <CheckBox x:ConnectionId='5' x:Name="TaskCompletedAutoSwitchCheckBox" 
                      Content="任务完成后自动切回到任务选择界面" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <CheckBox x:ConnectionId='6' x:Name="CountTimeStrategyCheckBox" 
                      Content="将番茄倒计时改成任务执行计时(重启程序生效)" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <CheckBox x:ConnectionId='7' x:Name="TaskStepWindowEnabledCheckBox" 
                      Content="启用任务步骤伴生窗口" 
                      Style="{StaticResource SettingCheckBoxStyle}"/>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Spacing="8" Margin="0,16,0,0">
                <Button x:ConnectionId='8' x:Name="SaveButton" Content="保存"                         />
                <Button x:ConnectionId='9' x:Name="CancelButton" Content="取消"                           />
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>

