using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Windows.System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using WinRT.Interop;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskSearchDialog : UserControl
    {
        private ObservableCollection<TaskSearchResultItem> _searchResults;

        public TaskSearchDialog()
        {
            this.InitializeComponent();
           
            
            _searchResults = new ObservableCollection<TaskSearchResultItem>();
            SearchResultsListView.ItemsSource = _searchResults;
            
            // 设置初始焦点
            this.Loaded += TaskSearchDialog_Loaded;
            
        }

        public event EventHandler DialogClosed;

        private void TaskSearchDialog_Loaded(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Focus(FocusState.Keyboard);
            CancelButton.Visibility = Visibility.Collapsed;
            IncludeCompletedTasksCheckBox.IsChecked = true;
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            PerformSearch();
        }

        private void SearchTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
            else if (e.Key == VirtualKey.Escape)
            {
                DialogClosed?.Invoke(this, EventArgs.Empty);
                e.Handled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogClosed?.Invoke(this, EventArgs.Empty);
        }

        private void PerformSearch()
        {
            var parent = WindowsUtils.GetAppWindowFrom(this.XamlRoot);
            WindowsUtils.ResizeWindow(parent,800,800);
            WindowsUtils.CenterWindow(parent);
            CancelButton.Visibility = Visibility.Visible;
            string keyword = SearchTextBox.Text.Trim();
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return;
            }

            try
            {
                _searchResults.Clear();
                
                // 获取搜索结果
                bool includeCompleted = IncludeCompletedTasksCheckBox.IsChecked ?? false;
                var tasks = StoreFactoryHolder.getTaskStore().getTaskListByKeyword(keyword, includeCompleted, UserInfoHolder.getUserId());
                
                // 转换为显示项
                foreach (var task in tasks)
                {
                    var taskListName = string.Empty;
                    try
                    {
                        var taskList = StoreFactoryHolder.getTaskListStore().getTaskListByKey(task.BelongToListKey, UserInfoHolder.getUserId());
                        taskListName = taskList?.Name ?? "未知列表";
                    }
                    catch
                    {
                        taskListName = "未知列表";
                    }

                    string remarks = task.TaskRemark;;
                    if (! string.IsNullOrEmpty(remarks))
                    {
                        remarks = "备注:   "+remarks.TrimEnd();
                        if (remarks.Length > 100)
                        {
                            remarks = remarks.Substring(0, 100) + "...";
                        }
                    }
                    
                    _searchResults.Add(new TaskSearchResultItem
                    {
                        TaskId = task.TaskID,
                        Title = task.Title,
                        IsCompleted = task.IsCompleted(),
                        DueDate = task.TaskDueTime,
                        TaskListName = taskListName,
                        TaskListKey = task.BelongToListKey,
                        Remarks = remarks
                    });
                }
                
                // 显示结果
                ResultsScrollViewer.Visibility = _searchResults.Count > 0 ? Visibility.Visible : Visibility.Collapsed;
                
                if (_searchResults.Count == 0)
                {
                    // 显示无结果提示
                    var dialog = new ContentDialog
                    {
                        Title = "搜索结果",
                        Content = "没有找到匹配的任务",
                        CloseButtonText = "确定",
                        XamlRoot = this.XamlRoot
                    };
                    
                    _ = dialog.ShowAsync();
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"搜索任务时出错: {ex.Message}");
                
                // 显示错误提示
                var dialog = new ContentDialog
                {
                    Title = "搜索错误",
                    Content = $"搜索任务时出错: {ex.Message}",
                    CloseButtonText = "确定",
                    XamlRoot = this.XamlRoot
                };
                
                _ = dialog.ShowAsync();
            }
        }
        
        private void SearchResultsListView_OnTapped(object sender, TappedRoutedEventArgs e)
        {
            var listView = sender as ListView;
            if (listView.SelectedItem is TaskSearchResultItem clickedItem)
            {
                LogService.Instance.Info("tapped tag="+clickedItem.Title);
                TTask tt = StoreFactoryHolder.getTaskStore().getTaskById(clickedItem.TaskId, UserInfoHolder.getUserId());
                var tl = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(clickedItem.TaskListKey, UserInfoHolder.getUserId());
                
                var aw = WindowsUtils.GetAppWindowFrom(this.XamlRoot);
                TheUICoordinator.Instance.ShowTaskDetailsWindow(TodoTaskViewObject.GetFrom(tt,tl),aw);
                
            }
        }
    }

    // 搜索结果项类
    public class TaskSearchResultItem
    {
        public long TaskId { get; set; }
        public string Title { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? DueDate { get; set; }
        public string TaskListName { get; set; }
        public string TaskListKey { get; set; }
        
        public String Remarks { get; set; }
    }
}
