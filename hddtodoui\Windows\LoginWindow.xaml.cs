using System;
using System.IO;
using System.Runtime.InteropServices;
using Windows.Graphics;
using HddtodoUI.BackendModels;
using HddtodoUI.UICoordinator;
using Microsoft.UI;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using WinRT.Interop;

namespace HddtodoUI.Windows
{
    public sealed partial class LoginWindow : Window
    {
        private AppWindow _appWindow;
        private OverlappedPresenter _presenter;
        private IntPtr _windowHandle;

        public LoginWindow()
        {
            this.InitializeComponent();

            // Get the window handle
            _windowHandle = WindowNative.GetWindowHandle(this);
            var windowId = Win32Interop.GetWindowIdFromWindow(_windowHandle);
            _appWindow = AppWindow.GetFromWindowId(windowId);

            // Get the presenter
            _presenter = _appWindow.Presenter as OverlappedPresenter;
            if (_presenter != null)
            {
                _presenter.IsMaximizable = false;
                _presenter.IsMinimizable = false;
                _presenter.IsResizable = false;
                //_presenter.IsAlwaysOnTop = true;
                _presenter.SetBorderAndTitleBar(false, false);
            }

            // Set window size
            _appWindow.Resize(new SizeInt32(400, 500));

            // Center the window
            var displayArea = DisplayArea.Primary;
            if (displayArea != null)
            {
                var centerX = (displayArea.WorkArea.Width - 400) / 2;
                var centerY = (displayArea.WorkArea.Height - 500) / 2;
                _appWindow.Move(new PointInt32(centerX, centerY));
            }

            // Setup events
            LoginControl.LoginSucceeded += LoginControl_LoginSucceeded;
            LoginControl.ExitRequested += LoginControl_ExitRequested;
            this.Activated += LoginWindow_Activated;
            
            this.AppWindow.SetIcon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets/HddTodoIcon.ico"));
        }

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        private void LoginWindow_Activated(object sender, WindowActivatedEventArgs args)
        {
            if (args.WindowActivationState != WindowActivationState.Deactivated)
            {
                // 强制设置键盘焦点到用户名文本框
                DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, () =>
                {
                    var textBox = LoginControl.FindName("UsernameTextBox") as TextBox;
                    if (textBox != null)
                    {
                        textBox.Focus(FocusState.Keyboard);
                    }
                });
            }
            
            if ( this.AppWindow.IsVisible)
                this.AppWindow.MoveInZOrderAtTop();
        }

        private void LoginControl_LoginSucceeded(object sender, UserInfo userInfo)
        {
            // 登录成功后显示主窗口
            var mainWindow = TheUICoordinator.Instance.GetMainWindow();
            mainWindow.Activate();
            
            FocusServer.FocusServer.Instance.Start();

            // 关闭登录窗口
            Close();
        }

        private void LoginControl_ExitRequested(object sender, EventArgs e)
        {
            // 退出应用程序
            Application.Current.Exit();
        }

        public void ShowAndActivate()
        {
            this.Activate();
            // 强制将窗口带到前台
            SetForegroundWindow(_windowHandle);
        }
    }
}