﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.TaskDetailsControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:models="using:HddtodoUI.Models"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
  

    mc:Ignorable="d"
    d:DesignHeight="1000"
    d:DesignWidth="1280">

    <UserControl.Resources>
        <converters:DateToStringConverter x:Key="DateToStringConverter" />

        <Style x:Key="TaskPropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13" />
            <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
            <Setter Property="Margin" Value="0,0,0,0" />
           
        </Style>

        <Style x:Key="TaskPropertyControlStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,0" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>
        
        <Style x:Key="RowRectTangleStyle" TargetType="Rectangle">
            <Setter Property="Margin" Value="0,5,0,5" />
            <Setter Property="Height" Value="1" />
            <Setter Property="Fill" Value="{ThemeResource BorderBrush}" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>

        <Style x:Key="CustomTitleTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>

        <Style x:Key="CustomSubTaskTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>

        <Style x:Key="CustomRelativePanelStyle" TargetType="RelativePanel">
            <Setter Property="Margin" Value="0,0,0,0" />
            <Setter Property="Height" Value="32" />
        </Style>

    </UserControl.Resources>

    <Grid Background="{ThemeResource WindowBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Main Content -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*" MinWidth="100" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="4*" MinWidth="100" />
            </Grid.ColumnDefinitions>

            <!-- Left Panel -->
            <Grid Grid.Column="0" Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                   
                </Grid.RowDefinitions>

                <!-- Back Button -->
                <StackPanel Orientation="Horizontal"   Margin="0,0,0,24" >
                    
                    <Button x:ConnectionId='19' Grid.Row="0"
                            x:Name="BackButton"
                            Content="&#xE80F;"
                            FontFamily="Segoe MDL2 Assets"
                            Style="{StaticResource SecondaryButtonStyle}"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Top"
                          
                                                     Margin="0,0,5,0" />
                    
                    <FontIcon x:ConnectionId='20' Name="IndicatorIcon" Glyph="&#xE76C;" FontFamily="Segoe MDL2 Assets" 
                              VerticalAlignment="Center" FontSize="8" FontWeight="Bold"  Visibility="Collapsed" Margin="0,0,4,0"></FontIcon>
                  
                
                    <BreadcrumbBar x:ConnectionId='21' x:Name="BreadcrumbBar1" VerticalAlignment="Center">
                        <BreadcrumbBar.ItemTemplate>
                            <DataTemplate                            >
                                <BreadcrumbBarItem >
                                    <BreadcrumbBarItem.ContentTemplate>
                                        <DataTemplate>
                                            <ToggleButton x:ConnectionId='24' Content="{Binding Path=Name}"                                 Tag="{Binding Path=TaskId}"/>
                                        </DataTemplate>
                                    </BreadcrumbBarItem.ContentTemplate>
                                </BreadcrumbBarItem>
                            </DataTemplate>
                        </BreadcrumbBar.ItemTemplate>
                    </BreadcrumbBar>
                </StackPanel>
               
                
               

                <!-- Task Title with Completion CheckBox -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="30" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!-- Task Completion CheckBox -->
                    <CheckBox x:ConnectionId='17' Grid.Column="0"
                              x:Name="TaskCompletionCheckBox"
                              VerticalAlignment="Center"
                              Margin="0,0,8,24"
                                                                      
                                                                           />

                    <!-- Task Title -->
                    <TextBox x:ConnectionId='18' Grid.Column="1"
                             x:Name="TaskTitleTextBox"
                             PlaceholderText="任务标题"
                             TextWrapping="Wrap"
                             Style="{StaticResource CustomTitleTextBoxStyle}"
                             Margin="0,0,0,24"
                              />
                </Grid>

                <!-- Task Timer -->
                <StackPanel Grid.Row="2"
                            Orientation="Vertical"
                            HorizontalAlignment="Center"
                            Margin="0,0,0,24">

                    <Border MinHeight="150">
                        <TextBlock x:ConnectionId='16' x:Name="TimerTextBlock"
                                   Text="00:00"
                                   FontSize="72"
                                   FontWeight="SemiBold"    
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,16"
                                                                              />
                    </Border>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="8">
                        <Button x:ConnectionId='15' x:Name="StartButton"

                                Style="{StaticResource SecondaryButtonStyle}"
                                                         >
                            <FontIcon
                                Glyph="{Binding  Status, Mode=OneWay, Converter={StaticResource TaskStatusIconConverter}}"
                                FontSize="36"
                                Foreground="{Binding  Status, Mode=OneWay, Converter={StaticResource TaskStatusColorConverter}}" />
                        </Button>
                    </StackPanel>
                </StackPanel>

                <!-- Notes -->
                <Grid x:ConnectionId='13' x:Name="NotesGrid" Grid.Row="3"  Margin="0,10,0,0" >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                               Text="备注"
                               Style="{StaticResource TaskPropertyLabelStyle}" Margin="0,5" />

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" >
                    <TextBox x:ConnectionId='14' Grid.Row="1"
                             x:Name="NotesTextBox"
                             PlaceholderText="添加备注..."
                             AcceptsReturn="True"
                             TextWrapping="Wrap"
                             VerticalAlignment="Stretch"
                             HorizontalAlignment="Stretch"
                             VerticalContentAlignment="Stretch"
                             BorderThickness="1"
                             BorderBrush="{ThemeResource BorderBrush}"
                             FontSize="15"
                             Padding="12"
                             MinHeight="200"
                             Height="Auto" />
                    </ScrollViewer>
                </Grid>
            </Grid>

            <!-- GridSplitter -->

            <controls:GridSplitter Grid.Column="1"
                                   Width="5"
                                   ResizeBehavior="BasedOnAlignment"
                                   ResizeDirection="Auto"
                                   Background="{ThemeResource BorderBrush}" Opacity="0.5">
                <controls:GridSplitter.RenderTransform>
                    <TranslateTransform X="-1" />
                </controls:GridSplitter.RenderTransform>
            </controls:GridSplitter>

            <!-- Right Panel -->
            <Grid Grid.Column="2" Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- Task Properties -->
                <StackPanel Grid.Row="0" Margin="0,0,0,8" MinWidth="200">
                    <!-- Due Date -->
                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}">
                        <TextBlock Text="到期时间" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True" />
                        <CalendarDatePicker x:ConnectionId='12' x:Name="DueDatePicker"  Width="150"
                                            PlaceholderText="选择日期"
                                            FirstDayOfWeek="Monday"
                                            IsTodayHighlighted="True"
                                            DateFormat="{}{year.full}/{month.integer}/{day.integer} {dayofweek.abbreviated}" RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True" FontSize="12"/>
                    </RelativePanel>
                   
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>

                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}" >
                    <!-- Priority -->
                        <TextBlock Text="优先级" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True"/>
                        <ComboBox x:ConnectionId='11' x:Name="PriorityComboBox" Width="100"
                                  Style="{StaticResource TaskPropertyControlStyle}"
                                  RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True" FontSize="12">
                            <ComboBoxItem Content="低" Tag="low" />
                            <ComboBoxItem Content="中" Tag="normal" />
                            <ComboBoxItem Content="高" Tag="high" />
                        </ComboBox>
                    </RelativePanel>
                    
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>
                    
                    <!-- Category -->
                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}">
                        <TextBlock Text="所属清单" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True"/>
                        <ComboBox x:ConnectionId='10' x:Name="CategoryComboBox"  MinWidth="100"
                                  Style="{StaticResource TaskPropertyControlStyle}"
                                  HorizontalAlignment="Stretch" RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True" FontSize="12"/>
                    </RelativePanel>
                    
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>

                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}" >
                    <!-- Created Date -->
                        <TextBlock Text="创建时间" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True"/>
                        <TextBlock x:ConnectionId='9' x:Name="CreatedDateTextBlock" RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True"
                                   Style="{StaticResource BodyTextStyle}"
                                   FontSize="12" />
                    </RelativePanel>
                    
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>

                    <!-- Task Reminder -->
                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}" >
                        <TextBlock Text="任务提醒" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True"/>
                        <StackPanel Orientation="Horizontal" RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True">
                            <TextBlock x:ConnectionId='7' x:Name="ReminderInfoTextBlock" Style="{StaticResource BodyTextStyle}" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="12"/>
                            <Button x:ConnectionId='8' x:Name="SetReminderButton" Content="设置"                                 Style="{StaticResource SecondaryButtonStyle}" FontSize="12"/>
                        </StackPanel>
                    </RelativePanel>
                    
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>

                    <!-- Task Restart -->
                    <RelativePanel Style="{StaticResource CustomRelativePanelStyle}" >
                        <TextBlock Text="任务重启" Style="{StaticResource TaskPropertyLabelStyle}" RelativePanel.AlignVerticalCenterWithPanel="True"/>
                        <StackPanel Orientation="Horizontal" RelativePanel.AlignRightWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True" >
                            <TextBlock x:ConnectionId='5' x:Name="RestartInfoTextBlock" Style="{StaticResource BodyTextStyle}" VerticalAlignment="Center" Margin="0,0,8,0" TextWrapping="Wrap" FontSize="12"/>
                            <Button x:ConnectionId='6' x:Name="SetRestartButton" Content="设置"                                Style="{StaticResource SecondaryButtonStyle}" FontSize="12"/>
                        </StackPanel>
                    </RelativePanel>
                    
                    <Rectangle  Style="{StaticResource RowRectTangleStyle}"/>
                </StackPanel>

                <!-- Task Steps Control -->
                <local:TaskStepsControl x:ConnectionId='4' Grid.Row="1" x:Name="TaskStepsControl" Margin="0,8,0,0" />
            </Grid>
        </Grid>

        <!-- Save Button (Bottom Row) -->
        <Grid x:ConnectionId='2' Grid.Row="1" x:Name="BottomBarGrid">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <Button x:ConnectionId='3' Margin="5,5,5,5"
                    x:Name="SaveButton"
                    Content="保存"
                    Style="{StaticResource PrimaryButtonStyle}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Visibility="Collapsed"
                                             />
        </Grid>
    </Grid>
</UserControl>

