using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Windows.ApplicationModel.DataTransfer;
using Windows.Foundation;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskCategoriesPanel : UserControl, INotifyPropertyChanged
    {
        private bool _isAddListNameValid;
        private bool _isEditMode = false; // 添加标志，用于区分新建和编辑模式
        private TaskListViewObject _currentEditingTaskList = null; // 当前正在编辑的任务列表

        // 构造函数
        public TaskCategoriesPanel()
        {
            this.InitializeComponent();
            InitializeCollections();
        }

        // 属性
        private ObservableCollection<TaskListViewObject> SystemCategories { get; set; }
        private ObservableCollection<TaskListViewObject> UserCategories { get; set; }

        public bool IsAddListNameValid
        {
            get => _isAddListNameValid;
            set
            {
                if (_isAddListNameValid != value)
                {
                    _isAddListNameValid = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsAddListNameValid)));
                }
            }
        }

        // 添加 PropertyChanged 事件
        public event PropertyChangedEventHandler PropertyChanged;

        // 事件定义，用于与 MainView 交互
        public event EventHandler<TaskListViewObject> CategorySelected;

        // 初始化集合
        private void InitializeCollections()
        {
            SystemCategories = new ObservableCollection<TaskListViewObject>();
            UserCategories = new ObservableCollection<TaskListViewObject>();

            // 设置数据源
            SystemTasksListView.ItemsSource = SystemCategories;
            // UserTasksTreeView的ItemsSource已在XAML中通过绑定设置
        }

        // 公共方法：初始化系统类别
        public void InitializeSystemCategories(IEnumerable<TaskListViewObject> categories)
        {
            SystemCategories.Clear();
            foreach (var category in categories)
            {
                SystemCategories.Add(category);
            }
        }

        // 公共方法：初始化用户类别
        public void InitializeUserCategories(IEnumerable<TaskListViewObject> categories)
        {
            UserCategories.Clear();
            foreach (var category in categories)
            {
                UserCategories.Add(category);
            }
        }

        // 事件处理程序：列表选择改变
        private void TasksListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListView listView && listView.SelectedItem is TaskListViewObject selectedCategory)
            {
                // 如果从系统列表选择，取消用户列表的选择
                if (listView == SystemTasksListView && UserTasksTreeView.SelectedItem != null)
                {
                    UserTasksTreeView.SelectedItem = null;
                }

                // 触发事件
                CategorySelected?.Invoke(this, selectedCategory);
            }
           
        }
        
        private void UserTasksTreeView_OnSelectionChanged(TreeView treeView, TreeViewSelectionChangedEventArgs args)
        {
            SystemTasksListView.SelectedItem = null;

            if(args.AddedItems.Count == 0) return;
            
            var selectedTreeCategory  = args.AddedItems[0] as TaskListViewObject;
            
            if(selectedTreeCategory == null) return;
            
            // 触发事件
            CategorySelected?.Invoke(this, selectedTreeCategory);

          
        }

        // 事件处理程序：添加列表按钮点击
        private async void AddListButton_Click(object sender, RoutedEventArgs e)
        {
            // 设置为新建模式
            _isEditMode = false;
            _currentEditingTaskList = null;

            // 设置对话框标题和按钮文本
            AddListDialog.Title = "添加新列表";
            AddListDialog.PrimaryButtonText = "添加";

            // 重置对话框状态
            ListNameTextBox.Text = string.Empty;
            ListDueDatePicker.Date = null;
            ListPriorityComboBox.SelectedIndex = 1;
            IsAddListNameValid = false;

            // 设置XamlRoot
            AddListDialog.XamlRoot = this.XamlRoot;

            await AddListDialog.ShowAsync();
        }

        // 事件处理程序：列表名称文本框内容改变
        private void ListNameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            IsAddListNameValid = !string.IsNullOrWhiteSpace(ListNameTextBox.Text);
        }

        // 事件处理程序：添加列表对话框确定按钮点击
        private void AddListDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            if (_isEditMode && _currentEditingTaskList != null)
            {
                // 编辑模式：更新现有任务列表

                // 获取原始任务列表
                var originalList = StoreFactoryHolder.getTaskListStore()
                    .getTaskListByKey(_currentEditingTaskList.Key, UserInfoHolder.getUserId());

                if (originalList != null)
                {
                    // 更新任务列表
                    originalList.Name = ListNameTextBox.Text.Trim();

                    // 更新到期时间
                    if (ListDueDatePicker.Date.HasValue)
                    {
                        originalList.SetDueTime(ListDueDatePicker.Date.Value.DateTime);
                    }
                    else
                    {
                        originalList.ListDueTime = null;
                    }

                    // 保存更改
                    var savedTaskList = StoreFactoryHolder.getTaskListStore().saveTaskListChange(originalList, UserInfoHolder.getUserId());

                    // 更新UI
                    int index = UserCategories.IndexOf(_currentEditingTaskList);
                    if (index >= 0)
                    {
                        // 创建新的视图对象
                        var updatedViewObject = TaskListViewObject.GetFrom(savedTaskList);
                        updatedViewObject.TaskCount = _currentEditingTaskList.TaskCount; // 保持任务计数

                        // 更新集合
                        UserCategories[index] = updatedViewObject;

                        // 如果当前选中的是被编辑的项，更新选择
                        if (UserTasksTreeView.SelectedItem == _currentEditingTaskList)
                        {
                            UserTasksTreeView.SelectedItem = updatedViewObject;
                            // 触发选择事件
                            CategorySelected?.Invoke(this, updatedViewObject);
                        }
                    }

                    TheUICoordinator.Instance.OnTaskListAdded();
                }
            }
            else
            {
                // 新建模式：创建新的任务列表
                var newList = new TaskListViewObject
                {
                    Name = ListNameTextBox.Text.Trim(),
                    DueDate = ListDueDatePicker.Date?.DateTime,
                    Priority = (TaskPriority)ListPriorityComboBox.SelectedIndex,
                    IsSystemList = false,
                    TaskCount = 0
                };

                TaskList newCreated = StoreFactoryHolder.getTaskListStore()
                    .createEmptyTaskList(newList.Name, newList.Name, UserInfoHolder.getUserId());

                if (newList.DueDate.HasValue)
                {
                    newCreated.SetDueTime(newList.DueDate.Value);
                    StoreFactoryHolder.getTaskListStore().saveTaskListChange(newCreated, UserInfoHolder.getUserId());
                }

                UserCategories.Insert(UserCategories.Count - 1, TaskListViewObject.GetFrom(newCreated));
            }

            // 重置模式
            _isEditMode = false;
            _currentEditingTaskList = null;
        }

        // 事件处理程序：添加列表对话框取消按钮点击
        private void AddListDialog_CloseButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 重置模式
            _isEditMode = false;
            _currentEditingTaskList = null;

            // 重置对话框标题和按钮文本
            AddListDialog.Title = "添加新列表";
            AddListDialog.PrimaryButtonText = "添加";
        }


        // 事件处理程序：添加新任务菜单项点击
        private async void AddTaskMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem != null)
            {
                var taskList = menuItem.DataContext as TaskListViewObject;
                if (taskList != null)
                {

                    var dialog = new QuickAddTaskDialog();

                    // 创建 ContentDialog 来显示 QuickAddTaskDialog
                    ContentDialog contentDialog = new ContentDialog
                    {
                        //Title = $"添加任务到{taskList.Name}",
                        Content = dialog,
                        PrimaryButtonText = "", // 不使用内置按钮，而是使用 QuickAddTaskDialog 的按钮
                        CloseButtonText = "",
                        XamlRoot = this.XamlRoot
                    };

                    dialog.HideTaskListComboBox();

                    // 创建一个标志，用于跟踪任务是否已保存
                    bool taskSaved = false;

                    // 订阅事件
                    dialog.TaskSaved += (s, taskVo) =>
                    {
                        // 设置任务的类别
                        taskVo.Category = taskList.Name;

                        var list = StoreFactoryHolder.getTaskCategoryStore()
                            .GetTaskCategoryByKey(taskVo.Category, UserInfoHolder.getUserId());
                        // 添加任务到集合
                        var task = TTaskFactory.CreateTaskInTaskList(taskVo.Title, taskVo.DueDate, taskVo.Priority, list);

                        TaskUICoordinatorFactory.Instance(task).OnTaskAdded(task, list);

                        if (dialog.IsStartTask)
                        {
                            var ttm = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                            TaskUICoordinatorFactory.Instance(task).StartOrPauseButtonClick(ttm);
                        }

                        // 标记任务已保存
                        taskSaved = true;

                        // 关闭对话框
                        contentDialog.Hide();
                    };

                    dialog.DialogCancelled += (s, e) =>
                    {
                        // 关闭对话框
                        contentDialog.Hide();
                    };

                    contentDialog.Resources["ContentDialogMaxWidth"] = 2000;
                    contentDialog.Resources["ContentDialogMaxHeight"] = 1000;
                    dialog.SetTitleBlockText($"添加任务到{taskList.Name}");
                    // 显示对话框
                    await contentDialog.ShowAsync();

                }
            }
        }

        // 事件处理程序：编辑标题菜单项点击
        private async void EditTitleMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem != null)
            {
                var taskList = menuItem.DataContext as TaskListViewObject;
                if (taskList != null && taskList.Name != "收集箱")
                {
                    // 获取原始任务列表
                    var originalList = StoreFactoryHolder.getTaskListStore()
                        .getTaskListByKey(taskList.Key, UserInfoHolder.getUserId());

                    if (originalList == null)
                    {
                        return;
                    }

                    // 设置为编辑模式
                    _isEditMode = true;
                    _currentEditingTaskList = taskList;

                    // 设置对话框标题和按钮文本
                    AddListDialog.Title = "编辑任务列表";
                    AddListDialog.PrimaryButtonText = "保存";

                    // 设置对话框内容
                    ListNameTextBox.Text = taskList.Name;
                    ListDueDatePicker.Date = taskList.DueDate;

                    // 设置优先级
                    ListPriorityComboBox.SelectedIndex = (int)taskList.Priority;

                    // 启用确定按钮
                    IsAddListNameValid = true;

                    // 设置XamlRoot
                    AddListDialog.XamlRoot = this.XamlRoot;

                    // 显示对话框
                    var result = await AddListDialog.ShowAsync();

                    // 重置为新建模式
                    _isEditMode = false;
                    _currentEditingTaskList = null;

                    // 重置对话框标题和按钮文本
                    AddListDialog.Title = "添加新列表";
                    AddListDialog.PrimaryButtonText = "添加";
                }
            }
        }

        // 事件处理程序：删除任务列表菜单项点击
        private async void CompleteThisListMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem != null)
            {
                var taskList = menuItem.DataContext as TaskListViewObject;
                if (taskList != null && taskList.Name != "收集箱")
                {
                    // 触发删除类别请求事件

                    var list = StoreFactoryHolder.getTaskListStore()
                        .getTaskListByKey(taskList.Key, UserInfoHolder.getUserId());
                    list.ListCompleteTime = DateTime.Now;
                    UserCategories.Remove(taskList);

                    await Task.Run(() =>
                    {
                        StoreFactoryHolder.getTaskListStore().saveTaskListChange(list, UserInfoHolder.getUserId());
                    });

                }
            }
        }

        // 事件处理程序：管理已完成任务列表按钮点击事件
        private async void ManageCompletedListsButton_Click(object sender, RoutedEventArgs e)
        {
            // var dialog = new CompletedTaskListsDialog();
            //
            // // 创建 ContentDialog 来显示 CompletedTaskListsDialog
            // ContentDialog contentDialog = new ContentDialog
            // {
            //     Title = "管理已完成的任务列表",
            //     Content = dialog,
            //     PrimaryButtonText = "", // 不使用内置按钮，而是使用 CompletedTaskListsDialog 的按钮
            //     CloseButtonText = "",
            //     XamlRoot = this.XamlRoot
            // };
            //
            // contentDialog.Resources["ContentDialogMaxWidth"] = 2000;
            // contentDialog.Resources["ContentDialogMaxHeight"] = 1000;
            // // 订阅事件
            // dialog.DialogClosed += (s, args) =>
            // {
            //     contentDialog.Hide();
            // };
            //
            // dialog.TaskCategorySetIncomplete += (s, taskList) =>
            // {
            //     // 添加到用户列表
            //     var userCategory = UserCategories.FirstOrDefault(c => c.Key == taskList.Key);
            //     if (userCategory == null)
            //     {
            //         long taskCount = StoreFactoryHolder.getTaskCategoryStore().getTaskListUnCompleteCount(taskList.Key, UserInfoHolder.getUserId());
            //         taskList.TaskCount = taskCount;
            //
            //         // 重新插入到 UserCategories
            //         int index = UserCategories.Count - 1;
            //         while (index >= 0)
            //         {
            //             var temp = UserCategories[index];
            //
            //             if (!temp.IsSystemList)
            //             {
            //                 if (temp.ListOrder < taskList.CategoryOrder)
            //                 {
            //                     UserCategories.Insert(index + 1, taskList);
            //                     // TreeView没有ScrollIntoView方法，可以通过其他方式处理
            //                     break;
            //                 }
            //             }
            //
            //             index--;
            //         }
            //
            //         //UserCategories.Add(taskList);
            //     }
            // };
            //
            // // 显示对话框
            // await contentDialog.ShowAsync();
        }

        private void ScrollViewer_OnViewChanged(object sender, ScrollViewerViewChangedEventArgs e)
        {
            var scrollView = sender as ScrollViewer;
            if (scrollView == null) return;

            // 当前滚动位置
            double verticalOffset = scrollView.VerticalOffset;
            // 可滚动的总高度
            double scrollableHeight = scrollView.ScrollableHeight;

            //LogService.Instance.Debug( $"verticalOffset: {verticalOffset}, scrollableHeight: {scrollableHeight}");
            // 判断是否显示上方提示
            if (verticalOffset > 10)
            {
                TopHint.Visibility = Visibility.Visible;
            }
            else
            {
                TopHint.Visibility = Visibility.Collapsed;
            }

            // 判断是否显示下方提示
            if (verticalOffset < scrollableHeight - 10)
            {
                BottomHint.Visibility = Visibility.Visible;
            }
            else
            {
                BottomHint.Visibility = Visibility.Collapsed;
            }
        }

        private void FrameworkElement_OnLoaded(object sender, RoutedEventArgs e)
        {
            ScrollViewer_OnViewChanged(sender, null);
        }

        // TreeView不支持重新排序功能，这些方法已不再需要
        public void SetTaskCategoryListViewCanReOrder()
        {
            // TreeView不支持ReorderMode
        }

        public void SetTaskCategoryListViewCanotReOrder()
        {
            // TreeView不支持ReorderMode
        }

        // 新增方法：处理TreeView的Expanding事件
        private void UserTasksTreeView_Expanding(TreeView sender, TreeViewExpandingEventArgs args)
        {
           
        }

        // 新增方法：处理TreeView的Collapsed事件（可选，用于释放资源）
        private void UserTasksTreeView_Collapsed(TreeView sender, TreeViewCollapsedEventArgs args)
        {
            // 如果需要释放资源，可以在此处清空子节点
            // 但为了用户体验，通常不建议这样做，除非内存占用很大
            // if (args.Item is TaskListViewObject taskList)
            // {
            //     taskList.Children.Clear();
            //     taskList.AreChildrenLoaded = false;
            // }
        }

        public void AddUserCategory(TaskListViewObject category)
        {
            UserCategories.Add(category);
        }

        public void RemoveUserCategory(string key)
        {
            UserCategories.Select(t => t).Where(t => t.Key == key).ToList().ForEach(t => UserCategories.Remove(t));
        }

        public void UpdateUserCategoryUncompletedTaskCount(string key, int count)
        {
            UserCategories.Select(t => t).Where(t => t.Key == key).ToList().ForEach(t =>
                t.TaskCount = count
            );
            // 刷新用户类别集合
            var tempList = UserCategories.ToList();
            UserCategories.Clear();
            foreach (var item in tempList)
            {
                UserCategories.Add(item);
            }
        }


    }
}