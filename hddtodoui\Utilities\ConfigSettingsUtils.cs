using System;
using System.Configuration;
using System.Drawing;
using System.Linq;


namespace HddtodoUI.Utilities;

public class ConfigSettingsUtils
{
    public static T GetConfigValue<T>(string key, T defaultValue)
    {
        var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
        var setting = config.AppSettings.Settings[key];
        return setting == null ? defaultValue : (T)Convert.ChangeType(setting.Value, typeof(T));
    }
    
    public static void SetConfigValue<T>(string key, T value)
    {
        var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
        UpdateSetting(config, key, value.ToString());
        config.Save(ConfigurationSaveMode.Modified);
        ConfigurationManager.RefreshSection("appSettings");
    }
    
    public static bool getAutoHideMainFormConfig()
    {
        return GetConfigValue("AutoHideMainForm", false);
    }
    
    public static void setAutoHideMainFormConfig(bool autoHideMainForm)
    {
        SetConfigValue("AutoHideMainForm", autoHideMainForm);
    }

    public static bool getTaskPauseAutoSwitchToTaskSelectionConfig()
    {
        return GetConfigValue("TaskPauseAutoSwitchToTaskSelection", false);
    }
    
    public static void setTaskPauseAutoSwitchToTaskSelectionConfig(bool taskPauseAutoSwitchToTaskSelection)
    {
        SetConfigValue("TaskPauseAutoSwitchToTaskSelection", taskPauseAutoSwitchToTaskSelection);
    }
    
    public static bool GetTaskCompletedAutoSwitchToTaskSelectionConfig()
    {
        return GetConfigValue("TaskFinishedAutoSwitchToTaskSelection", false);
    }
    
    public static void SetTaskCompletedAutoSwitchToTaskSelectionConfig(bool taskPauseAutoSwitchToTaskSelection)
    {
        SetConfigValue("TaskFinishedAutoSwitchToTaskSelection", taskPauseAutoSwitchToTaskSelection);
    }
    
    public static bool getCountTimeStrategyConfig()
    {
        return GetConfigValue("CountTimeStrategy", false);
    }
    
    public static void setCountTimeStrategyConfig(bool countTimeStrategy)
    {
        SetConfigValue("CountTimeStrategy", countTimeStrategy);
    }

    public static void setTaskStartAutoShowTaskDetailConfig(bool config)
    {
        SetConfigValue("TaskStartAutoShowTaskDetailConfig", config);
    }
    
    public static bool getTaskStartAutoShowTaskDetailConfig()
    {
       return GetConfigValue("TaskStartAutoShowTaskDetailConfig", false);
    }

    // ===== TaskStepWindow Enabled =====
    public static bool GetTaskStepWindowEnabledConfig()
    {
        return GetConfigValue("EnableTaskStepWindow", false);
    }

    public static void SetTaskStepWindowEnabledConfig(bool enabled)
    {
        SetConfigValue("EnableTaskStepWindow", enabled);
    }
    
    private static void UpdateSetting(Configuration config, string key, string value)
    {
        if (config.AppSettings.Settings[key] == null)
        {
            config.AppSettings.Settings.Add(key, value);
        }
        else
        {
            config.AppSettings.Settings[key].Value = value;
        }
    }
}