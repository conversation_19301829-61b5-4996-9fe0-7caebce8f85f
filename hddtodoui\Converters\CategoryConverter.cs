using System;
using HddtodoUI.TaskTomatoManager.Constants;
using Microsoft.UI.Xaml.Data;

namespace HddtodoUI.Converters;

public class CategoryConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, string language)
    {
        if (value is string category)
        {
            return category == SpecialTaskListConstants.CollectBox
                ? SpecialTaskListConstants.CollectBoxDisplayName
                : category;
        }
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, string language)
    {
        throw new NotImplementedException();
    }
}