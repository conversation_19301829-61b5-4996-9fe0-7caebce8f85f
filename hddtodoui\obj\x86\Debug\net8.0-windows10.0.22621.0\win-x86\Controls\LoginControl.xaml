﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.LoginControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <UserControl.Resources>
        <Style x:Key="LoginTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>

        <Style x:Key="LoginTextBoxStyle" TargetType="TextBox">
            <Setter Property="Margin" Value="0,0,0,16" />
            <Setter Property="Height" Value="40" />
        </Style>

        <Style x:Key="LoginPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Margin" Value="0,0,0,16" />
            <Setter Property="Height" Value="40" />
        </Style>
    </UserControl.Resources>

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <StackPanel VerticalAlignment="Center" Padding="32">
            <!-- Logo and Title -->
            <Image Source="/Assets/Square44x44Logo.png"
                   Width="80" Height="80"
                   Margin="0,0,0,16" />

            <TextBlock Text="HddTodo"
                       FontSize="28"
                       FontWeight="SemiBold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,32" />

            <!-- Login Form -->
            <TextBlock Text="用户名" Style="{StaticResource LoginTextBlockStyle}" />
            <TextBox x:ConnectionId='2' x:Name="UsernameTextBox"
                     PlaceholderText="请输入用户名"
                     Style="{StaticResource LoginTextBoxStyle}"
                                                       />

            <TextBlock Text="密码" Style="{StaticResource LoginTextBlockStyle}" />
            <PasswordBox x:ConnectionId='3' x:Name="PasswordBox"
                         PlaceholderText="请输入密码"
                         Style="{StaticResource LoginPasswordBoxStyle}"
                                                       />

            <!-- Remember Password Checkbox -->
            <CheckBox x:ConnectionId='4' x:Name="RememberPasswordCheckBox" 
                      Content="记住用户名和密码" 
                      Margin="0,0,0,16" />

            <!-- Error Message -->
            <TextBlock x:ConnectionId='5' x:Name="ErrorMessageTextBlock"
                       Foreground="Red"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"
                       Margin="0,0,0,16" />

            <!-- Buttons -->
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!-- Exit Button -->
                <Button x:ConnectionId='7' x:Name="ExitButton"
                        Content="退出"
                        HorizontalAlignment="Stretch"
                        Height="44"
                        Margin="0,0,4,0"
                                                 />

                <!-- Login Button -->
                <Button x:ConnectionId='8' x:Name="LoginButton"
                        Content="登录"
                        Grid.Column="1"
                        HorizontalAlignment="Stretch"
                        Height="44"
                        Margin="4,0,0,0"
                        Style="{StaticResource AccentButtonStyle}"
                                                  />
            </Grid>

            <!-- Loading Indicator -->
            <ProgressRing x:ConnectionId='6' x:Name="LoadingProgressRing"
                          IsActive="False"
                          Margin="0,8,0,0"
                          Width="24" Height="24"
                          HorizontalAlignment="Center"
                          Visibility="Collapsed" />
        </StackPanel>
    </Grid>
</UserControl>

