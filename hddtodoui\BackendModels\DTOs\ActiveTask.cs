using System;
using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels.DTOs
{
    public class ActiveTask
    {
        [JsonPropertyName("userId")]
        public long UserId { get; set; }

        [Json<PERSON>ropertyName("clientId")]
        public string ClientId { get; set; }

        [JsonPropertyName("clientType")]
        public string ClientType { get; set; }

        [Json<PERSON>ropertyName("taskId")]
        public long TaskId { get; set; }

        [JsonPropertyName("expiresAt")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime ExpiresAt { get; set; }
    }
}
