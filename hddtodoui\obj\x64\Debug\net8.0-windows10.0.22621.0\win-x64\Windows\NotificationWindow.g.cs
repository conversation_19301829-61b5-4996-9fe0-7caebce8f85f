﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Windows\NotificationWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "D397080B1518441525D18A290C2EBB71060F5B08804A42202643520A62E84966"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Windows
{
    partial class NotificationWindow : 
        global::Microsoft.UI.Xaml.Window, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Windows\NotificationWindow.xaml line 10
                {
                    this.RootGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 3: // Windows\NotificationWindow.xaml line 17
                {
                    this.LevelIndicator = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Shapes.Rectangle>(target);
                }
                break;
            case 4: // Windows\NotificationWindow.xaml line 69
                {
                    this.ContentContainer = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 5: // Windows\NotificationWindow.xaml line 80
                {
                    this.CloseButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.CloseButton).Click += this.CloseButton_Click;
                }
                break;
            case 6: // Windows\NotificationWindow.xaml line 71
                {
                    this.ContentTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 7: // Windows\NotificationWindow.xaml line 75
                {
                    this.CustomContentPresenter = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentPresenter>(target);
                }
                break;
            case 8: // Windows\NotificationWindow.xaml line 52
                {
                    this.LevelIcon = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            case 9: // Windows\NotificationWindow.xaml line 60
                {
                    this.TitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

