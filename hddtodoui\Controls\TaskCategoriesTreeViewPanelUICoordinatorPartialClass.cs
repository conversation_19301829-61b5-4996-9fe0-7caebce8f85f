using System.Linq;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Controls;

public partial class TaskCategoriesTreeViewPanel
{
    public void AddChildUserCategoryToParent(string parentKey, TaskCategoryViewObject chldCategory)
    {
        var parentCategory = FindCategoryInTree(parentKey);
        if (parentCategory == null) return;

        int taskCount = StoreFactoryHolder.getTaskCategoryStore()
            .GetCategoryUncompleteTaskCount(chldCategory.Key, UserInfoHolder.getUserId());
        chldCategory.TaskCount = taskCount;

        parentCategory.Children.Add(chldCategory);
    }

    public void UpdateUserCategoryUncompletedTaskCount(string key, int count)
    {

        var userCategory = FindCategoryInTree(key);
        if (userCategory == null) return;
        userCategory.TaskCount = count;
        UpdateCategoryInTree(key, userCategory);

    }

    public async void UpdateSystemCategoryUncompletedTaskCount(string key, int count)
    {

        SystemCategories.Select(t => t).Where(t => t.Key == key).ToList().ForEach(t =>
            t.TaskCount = count
        );
        SystemTasksListView.ItemsSource = null;
        SystemTasksListView.ItemsSource = SystemCategories;
    }

    private void ClearRecycleBinMenuItem_Click(object sender, RoutedEventArgs e)
    {
        //throw new System.NotImplementedException();
    }

    public async Task SwitchToUserCategory(TaskCategoryViewObject category)
    {
        SystemTasksListView.SelectedItem = null;

        var result = FindCategoryInTree(category.Key);
        if (result != null)
        {
            UserCategoriesTreeView.SelectedItem = result;
        }
        else
        {
            var parentPath = TaskCategpryParentPathHelper.GetParentPath(category.ParentCategoryKey, UserInfoHolder.getUserId());
            
            for (int i = 0; i < parentPath.Length; i++)
            {
                var parent = FindCategoryInTree(parentPath[i].Key);
                if (parent != null)
                {
                    parent.IsChildrenLoaded = true;
                    await LoadChildCategories(parent);
                    var parentNode = FindNodeByKey(UserCategoriesTreeView, parent.Key);
                    parentNode.IsExpanded = true;
                }
            }
            var newResult = FindCategoryInTree(category.Key);
            UserCategoriesTreeView.SelectedItem = newResult;
        }
    }

    public void RemoveUserTaskCategoryFromPanel(TaskCategoryViewObject category)
    {
        var userCategory = FindCategoryInTree(category.Key);
        // 如果找不到父集合，直接从根集合中移除
        UserCategories.Remove(userCategory);
    }

}