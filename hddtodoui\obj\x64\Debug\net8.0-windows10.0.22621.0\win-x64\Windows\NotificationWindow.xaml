﻿<Window
    x:Class="HddtodoUI.Windows.NotificationWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Windows"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid x:ConnectionId='2' x:Name="RootGrid" Background="{ThemeResource ApplicationPageBackgroundThemeBrush}" Padding="15" CornerRadius="8">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 级别指示器 - 横向显示在顶部 -->
        <Rectangle x:ConnectionId='3' x:Name="LevelIndicator" 
                   Grid.Row="0"
                   Height="5" 
                   Fill="#0078D7" 
                   Margin="-15,-12,-15,10" 
                   VerticalAlignment="Top"/>
        
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <Grid.Resources>
                <Style x:Key="NotificationTitleStyle" TargetType="TextBlock">
                    <Setter Property="FontWeight" Value="SemiBold"/>
                    <Setter Property="FontSize" Value="14"/>
                    <Setter Property="Margin" Value="0,0,0,5"/>
                </Style>
                
                <Style x:Key="NotificationContentStyle" TargetType="TextBlock">
                    <Setter Property="TextWrapping" Value="Wrap"/>
                    <Setter Property="FontSize" Value="13"/>
                </Style>
            </Grid.Resources>
            
            <!-- 标题区域 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 级别图标 -->
                <FontIcon x:ConnectionId='8' x:Name="LevelIcon" 
                          Grid.Column="0"
                          Glyph="&#xE946;" 
                          FontSize="16" 
                          Margin="0,0,8,0"
                          Visibility="Visible"/>
                
                <!-- 标题 -->
                <TextBlock x:ConnectionId='9' x:Name="TitleTextBlock" 
                           Grid.Column="1"
                           Text="通知标题" 
                           FontWeight="SemiBold" 
                           FontSize="16" 
                           Margin="0,0,0,10"/>
            </Grid>
            
            <!-- 内容区域 -->
            <Grid x:ConnectionId='4' x:Name="ContentContainer" Grid.Row="1">
                <!-- 默认文本内容 -->
                <TextBlock x:ConnectionId='6' x:Name="ContentTextBlock" 
                           Style="{StaticResource NotificationContentStyle}"/>
                
                <!-- 自定义内容容器 -->
                <ContentPresenter x:ConnectionId='7' x:Name="CustomContentPresenter" 
                                  Visibility="Collapsed"/>
            </Grid>
            
            <!-- 关闭按钮 -->
            <Button x:ConnectionId='5' x:Name="CloseButton" 
                    Grid.Row="2" 
                    Background="Transparent" 
                    BorderThickness="0"
                    Padding="4"
                    HorizontalAlignment="Right" 
                    Margin="0,10,0,0" 
                                             >
                <FontIcon Glyph="&#xE711;" FontSize="10"/>
            </Button>
        </Grid>
    </Grid>
</Window>

