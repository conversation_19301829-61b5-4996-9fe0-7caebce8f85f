﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\TasksPanel.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "6C795C2B0BC9FAB515D1998D27C1256F83976F61F21972C0F95C9E69D48E62F4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TasksPanel : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(global::Microsoft.UI.Xaml.Controls.ItemsControl obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.ItemsSource = value;
            }
            public static void Set_Microsoft_UI_Xaml_Data_CollectionViewSource_Source(global::Microsoft.UI.Xaml.Data.CollectionViewSource obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.Source = value;
            }
            public static void Set_HddtodoUI_Controls_TaskItemControl_TaskVO(global::HddtodoUI.Controls.TaskItemControl obj, global::HddtodoUI.Models.TodoTaskViewObject value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::HddtodoUI.Models.TodoTaskViewObject) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::HddtodoUI.Models.TodoTaskViewObject), targetNullValue);
                }
                obj.TaskVO = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::HddtodoUI.BackendModels.TaskCategory value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::HddtodoUI.BackendModels.TaskCategory) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::HddtodoUI.BackendModels.TaskCategory), targetNullValue);
                }
                obj.Category = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.CategoryCount = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.TaskCount = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj26_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Models.TodoTaskViewObject dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj26;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj26TaskVODisabled = false;

            private TasksPanel_obj26_BindingsTracking bindingsTracking;

            public TasksPanel_obj26_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj26_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 198 && columnNumber == 72)
                {
                    isobj26TaskVODisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 26: // Controls\TasksPanel.xaml line 198
                        this.obj26 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskItemControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj26.Target as global::HddtodoUI.Controls.TaskItemControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TodoTaskViewObject obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 198
                    if (!isobj26TaskVODisabled)
                    {
                        if ((this.obj26.Target as global::HddtodoUI.Controls.TaskItemControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskItemControl_TaskVO((this.obj26.Target as global::HddtodoUI.Controls.TaskItemControl), obj, null);
                        }
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj26_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj26_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj26_BindingsTracking(TasksPanel_obj26_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj26_Bindings>(obj);
                }

                public TasksPanel_obj26_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj26_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj30_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj30;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj30CategoryDisabled = false;
            private static bool isobj30CategoryCountDisabled = false;
            private static bool isobj30TaskCountDisabled = false;

            public TasksPanel_obj30_Bindings()
            {
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 127 && columnNumber == 29)
                {
                    isobj30CategoryDisabled = true;
                }
                else if (lineNumber == 128 && columnNumber == 29)
                {
                    isobj30CategoryCountDisabled = true;
                }
                else if (lineNumber == 129 && columnNumber == 29)
                {
                    isobj30TaskCountDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 30: // Controls\TasksPanel.xaml line 126
                        this.obj30 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoryItemForPlanControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(item), 1 << phase);
            }

            public void Recycle()
            {
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | (1 << 0))) != 0)
                    {
                        this.Update_Category(obj.Category, phase);
                        this.Update_SubcategoryCount(obj.SubcategoryCount, phase);
                        this.Update_TaskCount(obj.TaskCount, phase);
                    }
                }
            }
            private void Update_Category(global::HddtodoUI.BackendModels.TaskCategory obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TasksPanel.xaml line 126
                    if (!isobj30CategoryDisabled)
                    {
                        if ((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj, null);
                        }
                    }
                }
            }
            private void Update_SubcategoryCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TasksPanel.xaml line 126
                    if (!isobj30CategoryCountDisabled)
                    {
                        if ((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }
            private void Update_TaskCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TasksPanel.xaml line 126
                    if (!isobj30TaskCountDisabled)
                    {
                        if ((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount((this.obj30.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj33_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Models.TodoTaskViewObject dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj33;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj33TaskVODisabled = false;

            private TasksPanel_obj33_BindingsTracking bindingsTracking;

            public TasksPanel_obj33_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj33_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 91 && columnNumber == 44)
                {
                    isobj33TaskVODisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 33: // Controls\TasksPanel.xaml line 91
                        this.obj33 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskItemControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj33.Target as global::HddtodoUI.Controls.TaskItemControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TodoTaskViewObject obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 91
                    if (!isobj33TaskVODisabled)
                    {
                        if ((this.obj33.Target as global::HddtodoUI.Controls.TaskItemControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskItemControl_TaskVO((this.obj33.Target as global::HddtodoUI.Controls.TaskItemControl), obj, null);
                        }
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj33_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj33_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj33_BindingsTracking(TasksPanel_obj33_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj33_Bindings>(obj);
                }

                public TasksPanel_obj33_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj33_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Controls.TasksPanel dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.ListView obj14;
            private global::Microsoft.UI.Xaml.Controls.ListView obj23;
            private global::Microsoft.UI.Xaml.Data.CollectionViewSource obj27;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj14ItemsSourceDisabled = false;
            private static bool isobj23ItemsSourceDisabled = false;
            private static bool isobj27SourceDisabled = false;

            private TasksPanel_obj1_BindingsTracking bindingsTracking;

            public TasksPanel_obj1_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 272 && columnNumber == 44)
                {
                    isobj14ItemsSourceDisabled = true;
                }
                else if (lineNumber == 184 && columnNumber == 47)
                {
                    isobj23ItemsSourceDisabled = true;
                }
                else if (lineNumber == 99 && columnNumber == 65)
                {
                    isobj27SourceDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 14: // Controls\TasksPanel.xaml line 267
                        this.obj14 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                        break;
                    case 23: // Controls\TasksPanel.xaml line 179
                        this.obj23 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                        break;
                    case 27: // Controls\TasksPanel.xaml line 99
                        this.obj27 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Data.CollectionViewSource>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TasksPanel>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TasksPanel obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_CompletedTaskCategories(obj.CompletedTaskCategories, phase);
                        this.Update_CompletedTasks(obj.CompletedTasks, phase);
                        this.Update_SubCategories(obj.SubCategories, phase);
                    }
                }
            }
            private void Update_CompletedTaskCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_CompletedTaskCategories(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 267
                    if (!isobj14ItemsSourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(this.obj14, obj, null);
                    }
                }
            }
            private void Update_CompletedTasks(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_CompletedTasks(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 179
                    if (!isobj23ItemsSourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(this.obj23, obj, null);
                    }
                }
            }
            private void Update_SubCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_SubCategories(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 99
                    if (!isobj27SourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Data_CollectionViewSource_Source(this.obj27, obj, null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj1_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj1_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj1_BindingsTracking(TasksPanel_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj1_Bindings>(obj);
                }

                public TasksPanel_obj1_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_CompletedTaskCategories(null);
                    UpdateChildListeners_CompletedTasks(null);
                    UpdateChildListeners_SubCategories(null);
                }

                public void PropertyChanged_CompletedTaskCategories(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_CompletedTaskCategories(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> cache_CompletedTaskCategories = null;
                public void UpdateChildListeners_CompletedTaskCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj)
                {
                    if (obj != cache_CompletedTaskCategories)
                    {
                        if (cache_CompletedTaskCategories != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_CompletedTaskCategories).PropertyChanged -= PropertyChanged_CompletedTaskCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_CompletedTaskCategories).CollectionChanged -= CollectionChanged_CompletedTaskCategories;
                            cache_CompletedTaskCategories = null;
                        }
                        if (obj != null)
                        {
                            cache_CompletedTaskCategories = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_CompletedTaskCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_CompletedTaskCategories;
                        }
                    }
                }
                public void PropertyChanged_CompletedTasks(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_CompletedTasks(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> cache_CompletedTasks = null;
                public void UpdateChildListeners_CompletedTasks(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj)
                {
                    if (obj != cache_CompletedTasks)
                    {
                        if (cache_CompletedTasks != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_CompletedTasks).PropertyChanged -= PropertyChanged_CompletedTasks;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_CompletedTasks).CollectionChanged -= CollectionChanged_CompletedTasks;
                            cache_CompletedTasks = null;
                        }
                        if (obj != null)
                        {
                            cache_CompletedTasks = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_CompletedTasks;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_CompletedTasks;
                        }
                    }
                }
                public void PropertyChanged_SubCategories(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_SubCategories(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount> cache_SubCategories = null;
                public void UpdateChildListeners_SubCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount> obj)
                {
                    if (obj != cache_SubCategories)
                    {
                        if (cache_SubCategories != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_SubCategories).PropertyChanged -= PropertyChanged_SubCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_SubCategories).CollectionChanged -= CollectionChanged_SubCategories;
                            cache_SubCategories = null;
                        }
                        if (obj != null)
                        {
                            cache_SubCategories = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_SubCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_SubCategories;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TasksPanel.xaml line 27
                {
                    this.mainGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 3: // Controls\TasksPanel.xaml line 53
                {
                    this.TaskInputTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.TaskInputTextBox).KeyDown += this.TaskInputTextBox_KeyDown;
                }
                break;
            case 4: // Controls\TasksPanel.xaml line 59
                {
                    this.TaskListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskListView).DragItemsStarting += this.TaskListView_DragItemsStarting;
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskListView).DragItemsCompleted += this.TaskListView_DragItemsCompleted;
                }
                break;
            case 5: // Controls\TasksPanel.xaml line 97
                {
                    this.SubCategoriesGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 6: // Controls\TasksPanel.xaml line 136
                {
                    this.CompletedTasksButtonBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 7: // Controls\TasksPanel.xaml line 145
                {
                    this.CompletedTasksExpander = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Expander>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedTasksExpander).Expanding += this.CompletedTasksExpander_OnExpanding;
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedTasksExpander).Collapsed += this.CompletedTasksExpander_OnCollapsed;
                }
                break;
            case 8: // Controls\TasksPanel.xaml line 236
                {
                    this.CompletedCategoryExpander = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Expander>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedCategoryExpander).Expanding += this.CompletedCategoryExpander_OnExpanding;
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedCategoryExpander).Collapsed += this.CompletedCategoryExpander_OnCollapsed;
                }
                break;
            case 11: // Controls\TasksPanel.xaml line 319
                {
                    this.PreviousCategoryPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PreviousCategoryPageButton).Click += this.PreviousCategoryPage_Click;
                }
                break;
            case 12: // Controls\TasksPanel.xaml line 323
                {
                    this.CategoryPageInfoText = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 13: // Controls\TasksPanel.xaml line 326
                {
                    this.NextCategoryPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.NextCategoryPageButton).Click += this.NextCategoryPage_Click;
                }
                break;
            case 14: // Controls\TasksPanel.xaml line 267
                {
                    this.CompletedCategoryListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 17: // Controls\TasksPanel.xaml line 293
                {
                    global::Microsoft.UI.Xaml.Controls.Button element17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element17).Click += this.SetIncompleteButton_Click;
                }
                break;
            case 20: // Controls\TasksPanel.xaml line 220
                {
                    this.PreviousPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PreviousPageButton).Click += this.PreviousPage_Click;
                }
                break;
            case 21: // Controls\TasksPanel.xaml line 224
                {
                    this.PageInfoText = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 22: // Controls\TasksPanel.xaml line 227
                {
                    this.NextPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.NextPageButton).Click += this.NextPage_Click;
                }
                break;
            case 23: // Controls\TasksPanel.xaml line 179
                {
                    this.CompletedTasksListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 27: // Controls\TasksPanel.xaml line 99
                {
                    this.SubCategoriesCVS = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Data.CollectionViewSource>(target);
                }
                break;
            case 28: // Controls\TasksPanel.xaml line 110
                {
                    this.SubCategoriesListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 34: // Controls\TasksPanel.xaml line 44
                {
                    this.CategoryTitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 35: // Controls\TasksPanel.xaml line 47
                {
                    this.ColorComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 36: // Controls\TasksPanel.xaml line 48
                {
                    this.SortButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TasksPanel.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TasksPanel_obj1_Bindings bindings = new TasksPanel_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            case 26: // Controls\TasksPanel.xaml line 198
                {                    
                    global::HddtodoUI.Controls.TaskItemControl element26 = (global::HddtodoUI.Controls.TaskItemControl)target;
                    TasksPanel_obj26_Bindings bindings = new TasksPanel_obj26_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element26.DataContext);
                    element26.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element26, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element26, bindings);
                }
                break;
            case 30: // Controls\TasksPanel.xaml line 126
                {                    
                    global::HddtodoUI.Controls.TaskCategoryItemForPlanControl element30 = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)target;
                    TasksPanel_obj30_Bindings bindings = new TasksPanel_obj30_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element30.DataContext);
                    element30.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element30, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element30, bindings);
                }
                break;
            case 33: // Controls\TasksPanel.xaml line 91
                {                    
                    global::HddtodoUI.Controls.TaskItemControl element33 = (global::HddtodoUI.Controls.TaskItemControl)target;
                    TasksPanel_obj33_Bindings bindings = new TasksPanel_obj33_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element33.DataContext);
                    element33.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element33, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element33, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

