using System;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Data;

namespace HddtodoUI.Converters;

public class HasDateVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, string language)
    {
        var vo = value as DateTime?;
        return vo.HasValue ? Visibility.Visible : Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, string language)
    {
        throw new NotImplementedException();
    }
}