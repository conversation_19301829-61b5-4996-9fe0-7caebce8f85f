using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Windows.System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Windows.Media.SpeechRecognition;
using Windows.Globalization;
using System.Threading.Tasks;
using WindowsInput;
using WindowsInput.Events; // For KeyCode enum

namespace HddtodoUI.Controls
{
    public sealed partial class QuickAddTaskDialog : UserControl
    {
        
        public bool IsStartTask { get; set; }
        
        public QuickAddTaskDialog()
        {
            this.InitializeComponent();
            this.Loaded += QuickAddTaskDialog_Loaded;
            IsStartTask = false;
            LoadTaskLists();
            
            var ctrlEnterAccelerator = new KeyboardAccelerator
            {
                Key = VirtualKey.Enter,
                Modifiers = VirtualKeyModifiers.Control 
            };
          
            ctrlEnterAccelerator.Invoked += (sender, args) =>
            {
                SaveAndStartButton_Click(null, null);
            };
            
            
            var enterAccelerator = new KeyboardAccelerator
            {
                Key = VirtualKey.Enter,
            };
            enterAccelerator.Invoked += (sender, args) =>
            {
               SaveTask();
            };
            
            this.KeyboardAccelerators.Add(enterAccelerator);
            this.KeyboardAccelerators.Add(ctrlEnterAccelerator);
        }

        public event EventHandler<TodoTaskViewObject> TaskSaved;
        public event EventHandler DialogCancelled;

        private void LoadTaskLists()
        {
            TaskListComboBox.Items.Clear();

            TaskCategoryComboBoxHelper.MakeComboBoxItem(TaskListComboBox);
            
            TaskListComboBox.SelectedIndex = 0;
            
            PriorityComboBox.SelectedIndex = 1;
        }

        public void HideTaskListComboBox()
        {
            TaskListComboBox.Visibility = Visibility.Collapsed;
        }

        private void QuickAddTaskDialog_Loaded(object sender, RoutedEventArgs e)
        {
            TitleTextBox.Focus(FocusState.Keyboard);
            TitleTextBox.SelectAll();
            LogService.Instance.Debug("title text box selected");
        }
        
        public void SetTitleBlockText(string text)
        {
            TitleTextBox.Text = text;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            SaveTask();
        }
        private void SaveAndStartButton_Click(object sender, RoutedEventArgs e)
        {
            IsStartTask = true;
            SaveTask();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogCancelled?.Invoke(this, EventArgs.Empty);
        }

        private void ClearDateButton_Click(object sender, RoutedEventArgs e)
        {
            DueTimePicker.Date = null;
        }

        private void DueTimePicker_DateChanged(CalendarDatePicker sender, CalendarDatePickerDateChangedEventArgs args)
        {
            ClearDateButton.Visibility = sender.Date.HasValue ? Visibility.Visible : Visibility.Collapsed;
        }

        private void TitleTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
           
            if (e.Key == VirtualKey.Escape)
            {
                DialogCancelled?.Invoke(this, EventArgs.Empty);
                e.Handled = true;
            }
        }
        
        private async void VoiceInputButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Ensure the TextBox has focus before simulating key presses.
                TitleTextBox.Focus(FocusState.Programmatic);

                // Introduce a small delay to ensure focus is set before simulating input.
                // This might be necessary in some cases.
                // await Task.Delay(100); // Uncomment if focus issues persist

                await Simulate.Events()
                    .ClickChord(KeyCode.LWin, KeyCode.H)
                    .Invoke();
            }
            catch (Exception ex)
            {
                // Log or display an error if something goes wrong with input simulation
                Debug.WriteLine($"Error simulating Win+H: {ex.Message}");
                // Optionally, inform the user via a ContentDialog if appropriate.
            }
        }

        private void SaveTask()
        {
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text))
            {
                return;
            }

            // var priority =
            //     (TaskPriority)Enum.Parse(typeof(TaskPriority), (PriorityComboBox.SelectedIndex + 1).ToString());
            
            TaskPriority priority = (TaskPriority)Enum.Parse(typeof(TaskPriority), (PriorityComboBox.SelectedIndex ).ToString());

            // 获取选中的任务列表
            var selectedList = TaskListComboBox.SelectedItem as ComboBoxItem;
            string category = selectedList.Tag as string;

            var task = new TodoTaskViewObject
            {
                Title = TitleTextBox.Text,
                Priority = priority,
                DueDate = DueTimePicker.Date?.DateTime,
                Category = category
            };

            TaskSaved?.Invoke(this, task);
        }

        public void ClearAndReset()
        {
            TitleTextBox.Text = string.Empty;
            PriorityComboBox.SelectedIndex = 1;
            DueTimePicker.Date = null;

            // 重新加载任务列表
            LoadTaskLists();
        }

      
    }
}