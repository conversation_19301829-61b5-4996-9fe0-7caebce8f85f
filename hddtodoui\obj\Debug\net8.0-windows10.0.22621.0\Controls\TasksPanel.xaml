﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TasksPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:models="using:HddtodoUI.Models"
    xmlns:backendStore="using:HddtodoUI.BackendModels.BackendStore"
    mc:Ignorable="d" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
        <converters:TaskStatusTooltipConverter x:Key="TaskStatusTooltipConverter" />
        <converters:TaskStatusIconConverter x:Key="TaskStatusIconConverter" />
        <converters:TaskStatusColorConverter x:Key="TaskStatusColorConverter" />
        <converters:CategoryConverter x:Key="CategoryConverter" />
        
        <!-- 添加空集合到可见性转换器 -->
        <converters:EmptyCollectionToVisibilityConverter x:Key="EmptyCollectionToVisibilityConverter" />
    </UserControl.Resources>

    <Grid x:ConnectionId='2' Name="mainGrid" Padding="32,24" Background="{ThemeResource WindowBackgroundBrush}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" MinWidth="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" /> <!-- List Header -->
            <RowDefinition Height="Auto" /> <!-- Task Input -->
            <RowDefinition Height="*" /> <!-- Task List (with Sub Categories as Footer) -->
            <RowDefinition Height="Auto" /> <!-- Show Completed Tasks Button -->

        </Grid.RowDefinitions>

        <!-- List Header -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock x:ConnectionId='34' x:Name="CategoryTitleTextBlock" Text="收集箱" Style="{StaticResource SubHeaderTextStyle}" />

            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12" VerticalAlignment="Center">
                <ComboBox x:ConnectionId='35' x:Name="ColorComboBox" PlaceholderText="选择颜色" MinWidth="120" />
                <Button x:ConnectionId='36' x:Name="SortButton" Content="排序" Style="{StaticResource SecondaryButtonStyle}" />
            </StackPanel>
        </Grid>

        <!-- Task Input -->
        <TextBox x:ConnectionId='3' x:Name="TaskInputTextBox" Grid.Row="1"
                 PlaceholderText="写下接下来要做得事情，按回车键保存"
                 Margin="0,24,0,16"
                                                    />

        <!-- Task List -->
        <ListView x:ConnectionId='4' x:Name="TaskListView"
                  Grid.Row="2"
                  SelectionMode="None"
                  HorizontalAlignment="Stretch"
                  HorizontalContentAlignment="Stretch"

                  CanDragItems="True"
                  CanReorderItems="True"
                  AllowDrop="True"
                                                                    
                                                                      >
            <ListView.GroupStyle>
                <GroupStyle>
                    <GroupStyle.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Key.CategoryName,Converter={StaticResource CategoryConverter}}"
                                       Style="{StaticResource CategoryHeaderTextStyle}"
                                       Margin="5,8,0,6" />
                        </DataTemplate>
                    </GroupStyle.HeaderTemplate>
                </GroupStyle>
            </ListView.GroupStyle>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Margin" Value="0,4" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate                                       >
                    <local:TaskItemControl x:ConnectionId='28'                               />
                </DataTemplate>
            </ListView.ItemTemplate>

            <!-- Sub Categories as Footer -->
            <ListView.Footer>
                <Grid x:ConnectionId='29' x:Name="SubCategoriesGrid" Margin="0,16,0,0"
                                                                                                                                 >
                    <Grid.Resources>
                        <CollectionViewSource x:ConnectionId='30' x:Name="SubCategoriesCVS"                                              />
                    </Grid.Resources>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- Sub Categories Header -->
                    <TextBlock Grid.Row="0" Text="子分类" Style="{StaticResource CategoryHeaderTextStyle}" Margin="0,0,0,8" />

                    <!-- Sub Categories List -->
                    <ListView x:ConnectionId='31' x:Name="SubCategoriesListView" Grid.Row="1"
                              HorizontalContentAlignment="Stretch"
                              ItemsSource="{Binding Source={StaticResource SubCategoriesCVS}}"
                              SelectionMode="None"
                              HorizontalAlignment="Stretch"
                              MaxHeight="200">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                <Setter Property="Padding" Value="0" />
                                <Setter Property="Margin" Value="0,2,0,2" />
                                <Setter Property="MinHeight" Value="0" />
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate                                                >
                                <local:TaskCategoryItemForPlanControl x:ConnectionId='33'
                                                                
                                                                             
                                                                   />
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </ListView.Footer>
        </ListView>

        <!-- Show Completed Tasks Button -->
        <Border x:ConnectionId='5' Name="CompletedTasksButtonBorder" Grid.Row="3" Background="Transparent">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <StackPanel Spacing="5">
                
                
                <Expander x:ConnectionId='6' Grid.Column="0" x:Name="CompletedTasksExpander"  
                        
                          HorizontalAlignment="Stretch" 
                          HorizontalContentAlignment="Stretch" 
                          Padding="0" 
                                                                        
                                                                        
                          >
                
                    <Expander.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                <TextBlock x:Name="CompletedTasksExpanderTextBlock" Text="查看已完成任务" Style="{StaticResource CaptionTextStyle}" Opacity="0.5"/>
                            </StackPanel>
                        </DataTemplate>
                    </Expander.HeaderTemplate>
               
                    <Expander.Content >
                  
                        <!-- Completed Tasks Section -->
                        <Grid MaxHeight="400" >
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <ScrollViewer Grid.Row="0" Grid.Column="0" 
                                          VerticalScrollBarVisibility="Auto"
                                          HorizontalScrollBarVisibility="Disabled">
                                <Grid>
                                    <ListView x:ConnectionId='22' x:Name="CompletedTasksListView" 
                                              HorizontalAlignment="Stretch"
                                              Padding="0"
                                              SelectionMode="None"
                                              Visibility="Collapsed"
                                                                                                >
                                        <ListView.GroupStyle>
                                            <GroupStyle>
                                                <GroupStyle.HeaderTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Key}"
                                                                   Style="{StaticResource SubHeaderTextStyle}"
                                                                   Margin="0,16,0,8" />
                                                    </DataTemplate>
                                                </GroupStyle.HeaderTemplate>
                                            </GroupStyle>
                                        </ListView.GroupStyle>
                                        <ListView.ItemTemplate>
                                            <DataTemplate                                       >
                                                <local:TaskItemControl x:ConnectionId='25'                               />
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>
                                    <!-- 空数据提示 -->
                                    <TextBlock Text="没有已完成任务" 
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Margin="0,20"
                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                               FontSize="14"
                                               Visibility="{Binding ItemsSource, ElementName=CompletedTasksListView, Converter={StaticResource EmptyCollectionToVisibilityConverter}}" />
                                </Grid>
                            </ScrollViewer>

                            <!-- Pagination -->
                            <StackPanel Grid.Row="1"
                                        Orientation="Horizontal"
                                        HorizontalAlignment="Center"
                                        Margin="0,8,0,0"
                                        Spacing="8"
                                        Visibility="{Binding ElementName=CompletedTasksListView, Path=Visibility}">
                                <Button x:ConnectionId='19' x:Name="PreviousPageButton"
                                        Content="上一页"
                                        Style="{StaticResource SecondaryButtonStyle}"
                                                                   />
                                <TextBlock x:ConnectionId='20' x:Name="PageInfoText"
                                           VerticalAlignment="Center"
                                           Style="{StaticResource BodyTextStyle}" />
                                <Button x:ConnectionId='21' x:Name="NextPageButton"
                                        Content="下一页"
                                        Style="{StaticResource SecondaryButtonStyle}"
                                                               />
                            </StackPanel>
                        </Grid>
                   
                </Expander.Content>
            </Expander>
            <Expander x:ConnectionId='7' Grid.Column="0" x:Name="CompletedCategoryExpander" 
                      HorizontalAlignment="Stretch" 
                      HorizontalContentAlignment="Stretch" 
                      Padding="0"
                                                                       
                                                                       
                      >
                
                <Expander.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <TextBlock x:Name="CompletedCategoryExpanderTextBlock" Text="查看已完成分类"  Style="{StaticResource CaptionTextStyle}" Opacity="0.5"/>
                        </StackPanel>
                    </DataTemplate>
                </Expander.HeaderTemplate>
                <Expander.Content>
                    <!-- Completed Categories Section -->
                    <Grid MaxHeight="400">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <ScrollViewer Grid.Row="0" Grid.Column="0"
                                      VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Disabled">
                            <Grid>
                                <ListView x:ConnectionId='13' x:Name="CompletedCategoryListView"
                                           HorizontalAlignment="Stretch"
                                           Padding="0"
                                           SelectionMode="None"
                                           Visibility="Collapsed"
                                                                                                      >
                                    <ListView.ItemTemplate>
                                        <DataTemplate                                           >
                                            
                                            <Grid Margin="0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                        
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Name}" 
                                                               Style="{StaticResource BodyTextStyle}" 
                                                               FontWeight="SemiBold"/>
                                                    <TextBlock Text="{Binding CategoryCompleteTime, Converter={StaticResource DateTimeConverter}}" 
                                                               Style="{StaticResource CaptionTextStyle}" 
                                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                                               Margin="0,4,0,0"/>
                                                </StackPanel>
                        
                                                <Button x:ConnectionId='16' Grid.Column="2" 
                                                        Content="设为未完成" 
                                                                                         
                                                        Tag="{Binding }"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                                <!-- 空数据提示 -->
                                <TextBlock Text="没有已完成分类" 
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Margin="0,20"
                                           Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                           FontSize="14"
                                           Visibility="{Binding ItemsSource, ElementName=CompletedCategoryListView, Converter={StaticResource EmptyCollectionToVisibilityConverter}}" />
                            </Grid>
                        </ScrollViewer>

                        <!-- Pagination for Categories -->
                        <StackPanel Grid.Row="1"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Center"
                                    Margin="0,8,0,0"
                                    Spacing="8"
                                    Visibility="{Binding ElementName=CompletedCategoryListView, Path=Visibility}">
                            <Button x:ConnectionId='10' x:Name="PreviousCategoryPageButton"
                                    Content="上一页"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                                                       />
                            <TextBlock x:ConnectionId='11' x:Name="CategoryPageInfoText"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource BodyTextStyle}" />
                            <Button x:ConnectionId='12' x:Name="NextCategoryPageButton"
                                    Content="下一页"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                                                   />
                        </StackPanel>
                    </Grid>
                </Expander.Content>
            </Expander>

                </StackPanel>
            </Grid>
        </Border>


    </Grid>
</UserControl>

