namespace HddtodoUI.TaskTomatoManager.TomatoStrategy;

public class CountDownStrategy: ITomatoStrategy
{
    
    public int ResetCountDown(Tomato tomato)
    {
        return tomato.taskTimeSpan;
    }

    public bool ClockTicked(Tomato tomato)
    {
        if (tomato.isStarted())
        {
            tomato.CountDown = tomato.CountDown - 1;
           
            if (tomato.CountDown <= 0)
            {
                tomato.endTomato();
                return true;
            }
        }
        return false;
    }
}