using System;
using Windows.UI;
using Microsoft.UI;
using Colors = Microsoft.UI.Colors;

namespace HddtodoUI.Utilities;

public static class ColorUtils
{
    
    public static Color ColorFromName(string colorName)
    {
        // WinUI 3 中没有直接的 Brushes 类，这里简单映射常见颜色
        switch (colorName.ToLower())
        {
            case "pink": return Colors.Pink;
            case "orange": return Colors.Orange;
            case "green": return Colors.Green;
            case "blue": return Colors.Blue;
            case "purple": return Colors.Purple;
            case "brown": return Colors.Brown;
            case "gold": return Colors.Gold;
            case "black": return Colors.Black;
            case "olive": return Colors.Olive;
            case "teal": return Colors.Teal;
            case "tomato": return Colors.Tomato;
            default: return Colors.Black; // 默认颜色
            
        }
    }
    
    public static Color ColorFromHex(string hexColor)
    {
        hexColor = hexColor.TrimStart('#');
        byte a = 255;
        byte r = 0, g = 0, b = 0;

        if (hexColor.Length == 8)
        {
            a = Convert.ToByte(hexColor.Substring(0, 2), 16);
            r = Convert.ToByte(hexColor.Substring(2, 2), 16);
            g = Convert.ToByte(hexColor.Substring(4, 2), 16);
            b = Convert.ToByte(hexColor.Substring(6, 2), 16);
        }
        else if (hexColor.Length == 6)
        {
            r = Convert.ToByte(hexColor.Substring(0, 2), 16);
            g = Convert.ToByte(hexColor.Substring(2, 2), 16);
            b = Convert.ToByte(hexColor.Substring(4, 2), 16);
        }

        return Color.FromArgb(a, r, g, b);
    }
}