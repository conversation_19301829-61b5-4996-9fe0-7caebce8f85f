using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Controls
{
    // 分组辅助类
    public class Grouping<K, T> : ObservableCollection<T>
    {
        public K Key { get; private set; }
        public Grouping(K key, IEnumerable<T> items) : base(items)
        {
            Key = key;
        }
    }

    public sealed partial class PlannedProjectsPanel : UserControl
    {
        public ObservableCollection<TaskCategoryWithCount> PlannedCategories { get; } = new();
        public ObservableCollection<Grouping<string, TaskCategoryWithCount>> GroupedPlannedCategories { get; } = new();

        public PlannedProjectsPanel()
        {
            this.InitializeComponent();
        }

        /// <summary>
        /// 加载数据并刷新界面
        /// </summary>
        public async Task LoadAsync()
        {
            PlannedCategories.Clear();
            GroupedPlannedCategories.Clear();

            var lists = await Task.Run(() => SystemCategoryManager.getPlannedProjectCategories());
            var now = DateTime.Now;
            var grouped = HddtodoUI.Helpers.TaskCategoryGroupingHelper.GroupCategoriesByDueTime(lists, now);
            foreach (var (groupName, categories) in grouped)
            {
                GroupedPlannedCategories.Add(new Grouping<string, TaskCategoryWithCount>(groupName, categories));
            }

            // 填充原始集合用于Header计数
            foreach (var c in lists)
                PlannedCategories.Add(c);

            HeaderText.Text = $"{HddtodoUI.TaskTomatoManager.SystemCategoryManager.makeSystemCategories().FirstOrDefault(c => c.Key == HddtodoUI.TaskTomatoManager.Constants.SpecialTaskListConstants.Planned)?.Name} ( {PlannedCategories.Count} )";
        }
    }
}
