﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Views.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:controls="using:HddtodoUI.Controls"
    xmlns:controls1="using:CommunityToolkit.WinUI.Controls"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter"/>
        <converters:DueDateColorConverter x:Key="DueDateColorConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" MinHeight="100"/> <!-- User Info -->
            <RowDefinition Height="*"/> <!-- Main Content -->
            <RowDefinition Height="22"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- User Info Section -->
        <Grid Grid.Row="0" Padding="24,32" Background="{ThemeResource UserInfoBackgroundBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <TextBlock x:ConnectionId='6'                               
                      Style="{StaticResource HeaderTextStyle}"
                      Foreground="{ThemeResource UserInfoTextBrush}"
                      VerticalAlignment="Center"/>
         

            <StackPanel Grid.Column="1" 
                       Orientation="Horizontal" 
                       Spacing="12"
                       VerticalAlignment="Center">
                <Button x:ConnectionId='7' Content="统计" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Foreground="{ThemeResource UserInfoTextBrush}"
                        BorderBrush="{ThemeResource UserInfoTextBrush}"
                                                      />
                <Button x:ConnectionId='8' Content="设置" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Foreground="{ThemeResource UserInfoTextBrush}"
                        BorderBrush="{ThemeResource UserInfoTextBrush}"
                                                      
                        />
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <Grid x:ConnectionId='2' x:Name="MainViewControl" Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" MaxWidth="500" MinWidth="200"/>
<ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Task Categories -->
            <controls:TaskCategoriesTreeViewPanel x:ConnectionId='3' x:Name="TaskCategoriesPanel"
                                                                                                      
                                                                                                                  
                                      />

            <!-- Right Panel - Content Host (TasksPanel or PlannedProjectsPanel) -->
            <Grid Grid.Column="1">
    <controls:TasksPanel x:ConnectionId='4' x:Name="TasksPanel" />
    <controls:PlannedProjectsPanel x:ConnectionId='5' x:Name="PlannedProjectsPanel" Visibility="Collapsed" />
</Grid>
             
            
            <controls1:GridSplitter Grid.Column="1"
                                   Width="16"
                                   HorizontalAlignment="Left"
                                   ResizeBehavior="BasedOnAlignment"
                                   ResizeDirection="Auto">
                <controls1:GridSplitter.RenderTransform>
                    <TranslateTransform X="-7" />
                </controls1:GridSplitter.RenderTransform>
            </controls1:GridSplitter>

        </Grid>

        <!-- Status Bar -->
        <Grid Grid.Row="2" Background="{ThemeResource StatusBarBackgroundBrush}">
            <TextBlock Text="状态栏" Style="{StaticResource CaptionTextStyle}" 
                     Margin="12,0" VerticalAlignment="Center"/>
        </Grid>
    </Grid>
</UserControl>

