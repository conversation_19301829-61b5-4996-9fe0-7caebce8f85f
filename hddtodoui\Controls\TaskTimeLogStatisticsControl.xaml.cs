using System;
using System.Collections.ObjectModel;
using System.Linq;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskTimeLogStatisticsControl : UserControl
    {
        private ObservableCollection<TaskTimeLogStatistics> _statisticsCollection;
        private double _totalWorkTime;
        private double _workTimePercentage;
        private string _totalWorkTimeText;
        private bool _nextDayButtonEnable;
        private bool _nextDayButtonEnabled;
        private int _completedTasksCount;
        private int _uncompletedTasksCount;

        public TaskTimeLogStatisticsControl()
        {
            this.InitializeComponent();

            // 初始化数据
            _statisticsCollection = new ObservableCollection<TaskTimeLogStatistics>();
            StatisticsListView.ItemsSource = _statisticsCollection;

            DatePicker.MaxDate = DateTime.Today;

            // 设置默认日期为今天
            DatePicker.Date = DateTime.Today;
            
            // 加载今天的统计数据
            LoadStatisticsData(DateTime.Today);
            
            // 更新后一天按钮的可见性
            UpdateNextDayButtonEnable(DateTime.Today);
        }

        public double TotalWorkTime
        {
            get => _totalWorkTime;
            private set
            {
                if (_totalWorkTime != value)
                {
                    _totalWorkTime = value;
                    UpdateWorkTimePercentage();
                }
            }
        }

        public double WorkTimePercentage
        {
            get => _workTimePercentage;
            private set
            {
                if (_workTimePercentage != value)
                {
                    _workTimePercentage = value;
                    this.Bindings.Update();
                }
            }
        }

        public string TotalWorkTimeText
        {
            get => _totalWorkTimeText;
            private set
            {
                if (_totalWorkTimeText != value)
                {
                    _totalWorkTimeText = value;
                    this.Bindings.Update();
                }
            }
        }

        public bool IsNextDayButtonEnable
        {
            get => _nextDayButtonEnable;
            private set
            {
                if (_nextDayButtonEnable != value)
                {
                    _nextDayButtonEnable = value;
                    this.Bindings.Update();
                }
            }
        }
        
        // public Visibility IsNextDayButtonEnable
        // {
        //     get => _nextDayButtonVisibility;
        //     private set
        //     {
        //         if (_nextDayButtonVisibility != value)
        //         {
        //             _nextDayButtonVisibility = value;
        //             this.Bindings.Update();
        //         }
        //     }
        // }

        public int CompletedTasksCount
        {
            get => _completedTasksCount;
            private set
            {
                if (_completedTasksCount != value)
                {
                    _completedTasksCount = value;
                    this.Bindings.Update();
                }
            }
        }

        public int UncompletedTasksCount
        {
            get => _uncompletedTasksCount;
            private set
            {
                if (_uncompletedTasksCount != value)
                {
                    _uncompletedTasksCount = value;
                    this.Bindings.Update();
                }
            }
        }

        private void UpdateWorkTimePercentage()
        {
            // 计算工作时间占8小时的百分比
            const double workHoursInMinutes = 8 * 60; // 8小时 = 480分钟
            WorkTimePercentage = Math.Min((TotalWorkTime / workHoursInMinutes) * 100, 100);
            
            // 更新总工作时长文本
            int hours = (int)(TotalWorkTime / 60);
            int minutes = (int)(TotalWorkTime % 60);
            TotalWorkTimeText = $"总工作时长: {hours}小时{minutes}分钟 ({WorkTimePercentage:F1}% / 8小时)";
        }

        private void UpdateNextDayButtonEnable(DateTime date)
        {
            // 如果选择的日期是今天或未来日期，隐藏后一天按钮
            IsNextDayButtonEnable = date < DateTime.Today ? true : false;
        }

        private void UpdateTasksCount(System.Collections.Generic.List<TaskTimeLogStatistics> statistics)
        {
            // 统计已完成和未完成任务数量
            CompletedTasksCount = statistics.Count(s => s.IsCompleted);
            UncompletedTasksCount = statistics.Count(s => !s.IsCompleted);
        }

        private void LoadStatisticsData(DateTime date)
        {
            // 清空现有数据
            _statisticsCollection.Clear();

            // 获取指定日期的任务时间日志统计数据
            var statistics = StoreFactoryHolder.getTaskTimeLogStore().GetTaskTimeLogStatistics(date, UserInfoHolder.getUserId());
            
            // 按照时长降序排序
            var sortedStatistics = statistics.OrderByDescending(s => s.TotalDuration).ToList();
            
            // 将数据添加到集合中
            foreach (var stat in sortedStatistics)
            {
                _statisticsCollection.Add(stat);
            }

            // 计算总工作时长
            TotalWorkTime = sortedStatistics.Sum(s => s.TotalDuration);
            
            // 更新任务完成情况统计
            UpdateTasksCount(sortedStatistics);
        }

        private void DatePicker_DateChanged(CalendarDatePicker sender, CalendarDatePickerDateChangedEventArgs args)
        {
            if (args.NewDate.HasValue)
            {
                DateTime selectedDate = args.NewDate.Value.Date;
                LoadStatisticsData(selectedDate);
                UpdateNextDayButtonEnable(selectedDate);
            }
        }

        private void PreviousDayButton_Click(object sender, RoutedEventArgs e)
        {
            if (DatePicker.Date.HasValue)
            {
                DatePicker.Date = DatePicker.Date.Value.AddDays(-1);
            }
        }

        private void NextDayButton_Click(object sender, RoutedEventArgs e)
        {
            if (DatePicker.Date.HasValue)
            {
                DatePicker.Date = DatePicker.Date.Value.AddDays(1);
            }
        }

        private void ButtonBase_OnClick(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            ContentDialog dialog = FindContentDialog(button);
            if (dialog != null)
            {
                dialog.Hide();
            }
        }
        
        private ContentDialog FindContentDialog(DependencyObject element)
        {
            while (element != null && !(element is ContentDialog))
            {
                element = VisualTreeHelper.GetParent(element);
            }
            return element as ContentDialog;
        }
    }
}
