﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\TaskStepsControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "68828354159B747031CDB6307390772A52DD2322DDB9F87D6ACC7FB711783580"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskStepsControl : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_FrameworkElement_Tag(global::Microsoft.UI.Xaml.FrameworkElement obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.Tag = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_Primitives_ToggleButton_IsChecked(global::Microsoft.UI.Xaml.Controls.Primitives.ToggleButton obj, global::System.Nullable<global::System.Boolean> value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Boolean) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Boolean), targetNullValue);
                }
                obj.IsChecked = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_TextDecorations(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::Windows.UI.Text.TextDecorations value)
            {
                obj.TextDecorations = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_Border_Background(global::Microsoft.UI.Xaml.Controls.Border obj, global::Microsoft.UI.Xaml.Media.Brush value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::Microsoft.UI.Xaml.Media.Brush) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::Microsoft.UI.Xaml.Media.Brush), targetNullValue);
                }
                obj.Background = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskStepsControl_obj6_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskStepsControl_Bindings
        {
            private global::HddtodoUI.BackendModels.TaskStep dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private global::Microsoft.UI.Xaml.ResourceDictionary localResources;
            private global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement> converterLookupRoot;
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj6;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj7;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj8;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj9;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj10;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj11;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj12;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj13;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj14;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj15;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj16;
            private global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem obj17;
            private global::Microsoft.UI.Xaml.Controls.CheckBox obj18;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj19;
            private global::Microsoft.UI.Xaml.Controls.Button obj20;
            private global::Microsoft.UI.Xaml.Controls.Border obj21;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj7TagDisabled = false;
            private static bool isobj8TagDisabled = false;
            private static bool isobj9TagDisabled = false;
            private static bool isobj10TagDisabled = false;
            private static bool isobj11TagDisabled = false;
            private static bool isobj12TagDisabled = false;
            private static bool isobj13TagDisabled = false;
            private static bool isobj14TagDisabled = false;
            private static bool isobj15TagDisabled = false;
            private static bool isobj16TagDisabled = false;
            private static bool isobj17TagDisabled = false;
            private static bool isobj18IsCheckedDisabled = false;
            private static bool isobj19TextDisabled = false;
            private static bool isobj19TextDecorationsDisabled = false;
            private static bool isobj20TagDisabled = false;
            private static bool isobj21BackgroundDisabled = false;

            private TaskStepsControl_obj6_BindingsTracking bindingsTracking;

            public TaskStepsControl_obj6_Bindings()
            {
                this.bindingsTracking = new TaskStepsControl_obj6_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 85 && columnNumber == 101)
                {
                    isobj7TagDisabled = true;
                }
                else if (lineNumber == 86 && columnNumber == 101)
                {
                    isobj8TagDisabled = true;
                }
                else if (lineNumber == 91 && columnNumber == 90)
                {
                    isobj9TagDisabled = true;
                }
                else if (lineNumber == 96 && columnNumber == 92)
                {
                    isobj10TagDisabled = true;
                }
                else if (lineNumber == 101 && columnNumber == 91)
                {
                    isobj11TagDisabled = true;
                }
                else if (lineNumber == 106 && columnNumber == 90)
                {
                    isobj12TagDisabled = true;
                }
                else if (lineNumber == 111 && columnNumber == 92)
                {
                    isobj13TagDisabled = true;
                }
                else if (lineNumber == 116 && columnNumber == 91)
                {
                    isobj14TagDisabled = true;
                }
                else if (lineNumber == 121 && columnNumber == 90)
                {
                    isobj15TagDisabled = true;
                }
                else if (lineNumber == 126 && columnNumber == 91)
                {
                    isobj16TagDisabled = true;
                }
                else if (lineNumber == 131 && columnNumber == 91)
                {
                    isobj17TagDisabled = true;
                }
                else if (lineNumber == 150 && columnNumber == 35)
                {
                    isobj18IsCheckedDisabled = true;
                }
                else if (lineNumber == 157 && columnNumber == 36)
                {
                    isobj19TextDisabled = true;
                }
                else if (lineNumber == 162 && columnNumber == 36)
                {
                    isobj19TextDecorationsDisabled = true;
                }
                else if (lineNumber == 172 && columnNumber == 33)
                {
                    isobj20TagDisabled = true;
                }
                else if (lineNumber == 175 && columnNumber == 59)
                {
                    isobj21BackgroundDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 6: // Controls\TaskStepsControl.xaml line 82
                        this.obj6 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target));
                        break;
                    case 7: // Controls\TaskStepsControl.xaml line 85
                        this.obj7 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 8: // Controls\TaskStepsControl.xaml line 86
                        this.obj8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 9: // Controls\TaskStepsControl.xaml line 91
                        this.obj9 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 10: // Controls\TaskStepsControl.xaml line 96
                        this.obj10 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 11: // Controls\TaskStepsControl.xaml line 101
                        this.obj11 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 12: // Controls\TaskStepsControl.xaml line 106
                        this.obj12 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 13: // Controls\TaskStepsControl.xaml line 111
                        this.obj13 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 14: // Controls\TaskStepsControl.xaml line 116
                        this.obj14 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 15: // Controls\TaskStepsControl.xaml line 121
                        this.obj15 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 16: // Controls\TaskStepsControl.xaml line 126
                        this.obj16 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 17: // Controls\TaskStepsControl.xaml line 131
                        this.obj17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                        break;
                    case 18: // Controls\TaskStepsControl.xaml line 149
                        this.obj18 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CheckBox>(target);
                        this.bindingsTracking.RegisterTwoWayListener_18(this.obj18);
                        break;
                    case 19: // Controls\TaskStepsControl.xaml line 156
                        this.obj19 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 20: // Controls\TaskStepsControl.xaml line 165
                        this.obj20 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                        break;
                    case 21: // Controls\TaskStepsControl.xaml line 175
                        this.obj21 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj6.Target as global::Microsoft.UI.Xaml.Controls.Grid);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.TaskStep>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITaskStepsControl_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.TaskStep>(newDataRoot);
                    return true;
                }
                return false;
            }
            public void SetConverterLookupRoot(global::Microsoft.UI.Xaml.FrameworkElement rootElement)
            {
                this.converterLookupRoot = new global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement>(rootElement);
            }

            public global::Microsoft.UI.Xaml.Data.IValueConverter LookupConverter(string key)
            {
                if (this.localResources == null)
                {
                    global::Microsoft.UI.Xaml.FrameworkElement rootElement;
                    this.converterLookupRoot.TryGetTarget(out rootElement);
                    this.localResources = rootElement.Resources;
                    this.converterLookupRoot = null;
                }
                return (global::Microsoft.UI.Xaml.Data.IValueConverter) (this.localResources.ContainsKey(key) ? this.localResources[key] : global::Microsoft.UI.Xaml.Application.Current.Resources[key]);
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.BackendModels.TaskStep obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | (1 << 0))) != 0)
                    {
                        this.Update_StepId(obj.StepId, phase);
                    }
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_StepCompleteTime(obj.StepCompleteTime, phase);
                        this.Update_Title(obj.Title, phase);
                        this.Update_Color(obj.Color, phase);
                    }
                }
            }
            private void Update_StepId(global::System.Int64 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskStepsControl.xaml line 85
                    if (!isobj7TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj7, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 86
                    if (!isobj8TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj8, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 91
                    if (!isobj9TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj9, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 96
                    if (!isobj10TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj10, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 101
                    if (!isobj11TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj11, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 106
                    if (!isobj12TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj12, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 111
                    if (!isobj13TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj13, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 116
                    if (!isobj14TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj14, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 121
                    if (!isobj15TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj15, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 126
                    if (!isobj16TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj16, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 131
                    if (!isobj17TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj17, obj, null);
                    }
                    // Controls\TaskStepsControl.xaml line 165
                    if (!isobj20TagDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_FrameworkElement_Tag(this.obj20, obj, null);
                    }
                }
            }
            private void Update_StepCompleteTime(global::System.Nullable<global::System.DateTime> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskStepsControl.xaml line 149
                    if (!isobj18IsCheckedDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_Primitives_ToggleButton_IsChecked(this.obj18, (global::System.Nullable<global::System.Boolean>)this.LookupConverter("NullableDateTimeToCheckedConverter").Convert(obj, typeof(global::System.Nullable<global::System.Boolean>), null, null), null);
                    }
                    // Controls\TaskStepsControl.xaml line 156
                    if (!isobj19TextDecorationsDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_TextDecorations(this.obj19, (global::Windows.UI.Text.TextDecorations)this.LookupConverter("NullableDateTimeToStrikethroughConverter").Convert(obj, typeof(global::Windows.UI.Text.TextDecorations), null, null));
                    }
                }
            }
            private void Update_Title(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskStepsControl.xaml line 156
                    if (!isobj19TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj19, obj, null);
                    }
                }
            }
            private void Update_Color(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskStepsControl.xaml line 175
                    if (!isobj21BackgroundDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_Border_Background(this.obj21, (global::Microsoft.UI.Xaml.Media.Brush)this.LookupConverter("StringToBrushConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Media.Brush), null, null), null);
                    }
                }
            }
            private void UpdateTwoWay_18_IsChecked()
            {
                if (this.initialized)
                {
                    if (this.dataRoot != null)
                    {
                        this.dataRoot.StepCompleteTime = (global::System.Nullable<global::System.DateTime>)this.LookupConverter("NullableDateTimeToCheckedConverter").ConvertBack(this.obj18.IsChecked, typeof(global::System.Nullable<global::System.DateTime>), null, null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskStepsControl_obj6_BindingsTracking
            {
                private global::System.WeakReference<TaskStepsControl_obj6_Bindings> weakRefToBindingObj; 

                public TaskStepsControl_obj6_BindingsTracking(TaskStepsControl_obj6_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskStepsControl_obj6_Bindings>(obj);
                }

                public TaskStepsControl_obj6_Bindings TryGetBindingObject()
                {
                    TaskStepsControl_obj6_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

                public void RegisterTwoWayListener_18(global::Microsoft.UI.Xaml.Controls.CheckBox sourceObject)
                {
                    sourceObject.RegisterPropertyChangedCallback(global::Microsoft.UI.Xaml.Controls.Primitives.ToggleButton.IsCheckedProperty, (sender, prop) =>
                    {
                        var bindingObj = this.TryGetBindingObject();
                        if (bindingObj != null)
                        {
                            bindingObj.UpdateTwoWay_18_IsChecked();
                        }
                    });
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TaskStepsControl.xaml line 46
                {
                    this.TaskStepsListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskStepsListView).DragItemsStarting += this.TaskStepsListView_DragItemsStarting;
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskStepsListView).DragItemsCompleted += this.TaskStepsListView_DragItemsCompleted;
                }
                break;
            case 3: // Controls\TaskStepsControl.xaml line 62
                {
                    this.NewTaskStepTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.NewTaskStepTextBox).KeyDown += this.NewTaskStepTextBox_KeyDown;
                }
                break;
            case 4: // Controls\TaskStepsControl.xaml line 68
                {
                    this.AddTaskStepButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.AddTaskStepButton).Click += this.AddTaskStepButton_Click;
                }
                break;
            case 7: // Controls\TaskStepsControl.xaml line 85
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element7 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element7).Click += this.EditStepTitle_Click;
                }
                break;
            case 8: // Controls\TaskStepsControl.xaml line 86
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element8).Click += this.CopyStepTitle_Click;
                }
                break;
            case 9: // Controls\TaskStepsControl.xaml line 91
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element9 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element9).Click += this.SetColorPink_Click;
                }
                break;
            case 10: // Controls\TaskStepsControl.xaml line 96
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element10 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element10).Click += this.SetColorOrange_Click;
                }
                break;
            case 11: // Controls\TaskStepsControl.xaml line 101
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element11 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element11).Click += this.SetColorGreen_Click;
                }
                break;
            case 12: // Controls\TaskStepsControl.xaml line 106
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element12 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element12).Click += this.SetColorBlue_Click;
                }
                break;
            case 13: // Controls\TaskStepsControl.xaml line 111
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element13 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element13).Click += this.SetColorPurple_Click;
                }
                break;
            case 14: // Controls\TaskStepsControl.xaml line 116
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element14 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element14).Click += this.SetColorBrown_Click;
                }
                break;
            case 15: // Controls\TaskStepsControl.xaml line 121
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element15 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element15).Click += this.SetColorGold_Click;
                }
                break;
            case 16: // Controls\TaskStepsControl.xaml line 126
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element16 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element16).Click += this.SetColorBlack_Click;
                }
                break;
            case 17: // Controls\TaskStepsControl.xaml line 131
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element17).Click += this.SetColorNone_Click;
                }
                break;
            case 18: // Controls\TaskStepsControl.xaml line 149
                {
                    global::Microsoft.UI.Xaml.Controls.CheckBox element18 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CheckBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.CheckBox)element18).Checked += this.StepCheckBox_Checked;
                    ((global::Microsoft.UI.Xaml.Controls.CheckBox)element18).Unchecked += this.StepCheckBox_Unchecked;
                }
                break;
            case 20: // Controls\TaskStepsControl.xaml line 165
                {
                    global::Microsoft.UI.Xaml.Controls.Button element20 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element20).Click += this.DeleteStepButton_Click;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 6: // Controls\TaskStepsControl.xaml line 82
                {                    
                    global::Microsoft.UI.Xaml.Controls.Grid element6 = (global::Microsoft.UI.Xaml.Controls.Grid)target;
                    TaskStepsControl_obj6_Bindings bindings = new TaskStepsControl_obj6_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element6.DataContext);
                    bindings.SetConverterLookupRoot(this);
                    element6.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element6, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element6, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

