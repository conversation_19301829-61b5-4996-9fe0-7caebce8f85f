using System;
using System.Collections.Generic;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using System.Linq;

namespace HddtodoUI.Utilities
{
    public static class TaskCategpryParentPathHelper
    {
        /// <summary>
        /// 获取父级分类路径的名称数组（从根到当前）
        /// </summary>
        public static TaskCategory[] GetParentPath(string parentCategoryKey, long userId)
        {
            if (string.IsNullOrEmpty(parentCategoryKey))
                return Array.Empty<TaskCategory>();

            var path = new List<TaskCategory>();
            var store = StoreFactoryHolder.getTaskCategoryStore();
            string currentKey = parentCategoryKey;
            while (!string.IsNullOrEmpty(currentKey))
            {
                var parent = store.GetTaskCategoryByKey(currentKey, userId);
                if (parent == null) break;
                path.Insert(0, parent);
                currentKey = parent.ParentCategoryKey;
            }
            return path.ToArray();
        }

        /// <summary>
        /// 获取父级分类路径字符串（用“ / ”分隔）
        /// </summary>
        public static string GetParentPathString(string parentCategoryKey, long userId)
        {
            var categories = GetParentPath(parentCategoryKey, userId);
            return string.Join(" / ", categories.Select(c => c.Name));
        }
    }
}