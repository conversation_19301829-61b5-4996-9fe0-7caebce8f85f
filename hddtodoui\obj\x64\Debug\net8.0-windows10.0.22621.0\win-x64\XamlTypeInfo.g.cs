﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Diagnostics.CodeAnalysis;


namespace HddtodoUI
{
    public partial class App : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        private global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMetaDataProvider __appProvider;

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMetaDataProvider _AppProvider
        {
            get
            {
                if (__appProvider == null)
                {
                    __appProvider = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMetaDataProvider();
                }
                return __appProvider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return _AppProvider.GetXamlType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return _AppProvider.GetXamlType(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return _AppProvider.GetXmlnsDefinitions();
        }
    }
}

namespace HddtodoUI.HddtodoUI_XamlTypeInfo
{
    /// <summary>
    /// Main class for providing metadata for the app or library
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    public sealed partial class XamlMetaDataProvider : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        private global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider _provider = null;

        private global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider Provider
        {
            get
            {
                if (_provider == null)
                {
                    _provider = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider();
                }
                return _provider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::Windows.Foundation.Metadata.DefaultOverload]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return Provider.GetXamlTypeByType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return Provider.GetXamlTypeByName(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return new global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[0];
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlTypeInfoProvider
    {
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType) 
            { 
                if (_xamlTypeCacheByType.TryGetValue(type, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByType(type);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForType(type);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByName(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType)
            {
                if (_xamlTypeCacheByName.TryGetValue(typeName, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByName(typeName);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForName(typeName);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMemberByLongName(string longMemberName)
        {
            if (string.IsNullOrEmpty(longMemberName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlMember xamlMember;
            lock (_xamlMembers)
            {
                if (_xamlMembers.TryGetValue(longMemberName, out xamlMember))
                {
                    return xamlMember;
                }
                xamlMember = CreateXamlMember(longMemberName);
                if (xamlMember != null)
                {
                    _xamlMembers.Add(longMemberName, xamlMember);
                }
            }
            return xamlMember;
        }

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByName = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByType = new global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>
                _xamlMembers = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>();

        string[] _typeNameTable = null;
        global::System.Type[] _typeTable = null;
        
        private void InitTypeTables()
        {
            _typeNameTable = new string[125];
            _typeNameTable[0] = "Microsoft.UI.Xaml.Controls.XamlControlsResources";
            _typeNameTable[1] = "Microsoft.UI.Xaml.ResourceDictionary";
            _typeNameTable[2] = "Object";
            _typeNameTable[3] = "Boolean";
            _typeNameTable[4] = "HddtodoUI.Converters.DateToStringConverter";
            _typeNameTable[5] = "HddtodoUI.Converters.TaskStatusTooltipConverter";
            _typeNameTable[6] = "HddtodoUI.Converters.TaskStatusIconConverter";
            _typeNameTable[7] = "HddtodoUI.Converters.TaskStatusColorConverter";
            _typeNameTable[8] = "HddtodoUI.Converters.NullableDateTimeToCheckedConverter";
            _typeNameTable[9] = "HddtodoUI.Converters.NullableDateTimeToStrikethroughConverter";
            _typeNameTable[10] = "HddtodoUI.Converters.DateTimeConverter";
            _typeNameTable[11] = "HddtodoUI.Controls.CompletedTaskListsDialog";
            _typeNameTable[12] = "Microsoft.UI.Xaml.Controls.UserControl";
            _typeNameTable[13] = "Microsoft.UI.Xaml.Controls.ProgressRing";
            _typeNameTable[14] = "Microsoft.UI.Xaml.Controls.Control";
            _typeNameTable[15] = "Double";
            _typeNameTable[16] = "Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings";
            _typeNameTable[17] = "Microsoft.UI.Xaml.DependencyObject";
            _typeNameTable[18] = "HddtodoUI.Controls.LoginControl";
            _typeNameTable[19] = "HddtodoUI.Controls.TaskCategoryItemForPlanControl";
            _typeNameTable[20] = "HddtodoUI.BackendModels.TaskCategory";
            _typeNameTable[21] = "Int32";
            _typeNameTable[22] = "String";
            _typeNameTable[23] = "HddtodoUI.Controls.PlannedProjectsPanel";
            _typeNameTable[24] = "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>";
            _typeNameTable[25] = "System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>";
            _typeNameTable[26] = "HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount";
            _typeNameTable[27] = "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>";
            _typeNameTable[28] = "System.Collections.ObjectModel.Collection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>";
            _typeNameTable[29] = "HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>";
            _typeNameTable[30] = "HddtodoUI.Controls.QuickAddTaskDialog";
            _typeNameTable[31] = "HddtodoUI.Controls.SettingsDialog";
            _typeNameTable[32] = "HddtodoUI.Controls.TaskItemControl";
            _typeNameTable[33] = "HddtodoUI.Models.TodoTaskViewObject";
            _typeNameTable[34] = "HddtodoUI.Controls.SubtasksControl";
            _typeNameTable[35] = "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Models.TodoTaskViewObject>";
            _typeNameTable[36] = "System.Collections.ObjectModel.Collection`1<HddtodoUI.Models.TodoTaskViewObject>";
            _typeNameTable[37] = "Int64";
            _typeNameTable[38] = "System.Nullable`1<System.DateTime>";
            _typeNameTable[39] = "System.ValueType";
            _typeNameTable[40] = "System.DateTime";
            _typeNameTable[41] = "HddtodoUI.BackendModels.TaskPriority";
            _typeNameTable[42] = "System.Enum";
            _typeNameTable[43] = "HddtodoUI.Models.TaskStatus";
            _typeNameTable[44] = "HddtodoUI.BackendModels.TTask";
            _typeNameTable[45] = "Microsoft.UI.Xaml.Visibility";
            _typeNameTable[46] = "HddtodoUI.Converters.DueDateColorConverter";
            _typeNameTable[47] = "HddtodoUI.Converters.InboxVisibilityConverter";
            _typeNameTable[48] = "HddtodoUI.Converters.HasDateVisibilityConverter";
            _typeNameTable[49] = "Microsoft.UI.Xaml.Controls.TreeView";
            _typeNameTable[50] = "Microsoft.UI.Xaml.Controls.TreeViewSelectionMode";
            _typeNameTable[51] = "Microsoft.UI.Xaml.DataTemplate";
            _typeNameTable[52] = "Microsoft.UI.Xaml.Style";
            _typeNameTable[53] = "Microsoft.UI.Xaml.Controls.StyleSelector";
            _typeNameTable[54] = "Microsoft.UI.Xaml.Media.Animation.TransitionCollection";
            _typeNameTable[55] = "Microsoft.UI.Xaml.Controls.DataTemplateSelector";
            _typeNameTable[56] = "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>";
            _typeNameTable[57] = "Microsoft.UI.Xaml.Controls.TreeViewNode";
            _typeNameTable[58] = "System.Collections.Generic.IList`1<Object>";
            _typeNameTable[59] = "Microsoft.UI.Xaml.Controls.TreeViewItem";
            _typeNameTable[60] = "Microsoft.UI.Xaml.Controls.ListViewItem";
            _typeNameTable[61] = "Microsoft.UI.Xaml.Controls.ContentControl";
            _typeNameTable[62] = "Microsoft.UI.Xaml.Media.Brush";
            _typeNameTable[63] = "Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings";
            _typeNameTable[64] = "HddtodoUI.Controls.TaskCategoriesPanel";
            _typeNameTable[65] = "HddtodoUI.Converters.TreeNodeGlyphConverter";
            _typeNameTable[66] = "HddtodoUI.Controls.TaskCategoriesTreeViewPanel";
            _typeNameTable[67] = "HddtodoUI.Converters.StringToVisibilityConverter";
            _typeNameTable[68] = "HddtodoUI.Converters.DateTimeToBrushConverter";
            _typeNameTable[69] = "HddtodoUI.Converters.IntToVisibilityConverter";
            _typeNameTable[70] = "CommunityToolkit.WinUI.Controls.GridSplitter";
            _typeNameTable[71] = "CommunityToolkit.WinUI.Controls.SizerBase";
            _typeNameTable[72] = "CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior";
            _typeNameTable[73] = "CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection";
            _typeNameTable[74] = "Microsoft.UI.Input.InputSystemCursorShape";
            _typeNameTable[75] = "Microsoft.UI.Xaml.Controls.Orientation";
            _typeNameTable[76] = "HddtodoUI.Controls.TaskStepsControl";
            _typeNameTable[77] = "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.TaskStep>";
            _typeNameTable[78] = "System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.TaskStep>";
            _typeNameTable[79] = "HddtodoUI.BackendModels.TaskStep";
            _typeNameTable[80] = "Microsoft.UI.Xaml.Controls.BreadcrumbBar";
            _typeNameTable[81] = "Microsoft.UI.Xaml.Controls.BreadcrumbBarItem";
            _typeNameTable[82] = "HddtodoUI.Controls.TaskDetailsControl";
            _typeNameTable[83] = "Microsoft.UI.Xaml.Controls.NumberBox";
            _typeNameTable[84] = "Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode";
            _typeNameTable[85] = "Windows.Globalization.NumberFormatting.INumberFormatter2";
            _typeNameTable[86] = "Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase";
            _typeNameTable[87] = "Microsoft.UI.Xaml.Media.SolidColorBrush";
            _typeNameTable[88] = "Microsoft.UI.Xaml.TextReadingOrder";
            _typeNameTable[89] = "Microsoft.UI.Xaml.Controls.NumberBoxValidationMode";
            _typeNameTable[90] = "HddtodoUI.Controls.TaskReminderDialog";
            _typeNameTable[91] = "Microsoft.UI.Xaml.Controls.ContentDialog";
            _typeNameTable[92] = "HddtodoUI.BackendModels.TaskReminder";
            _typeNameTable[93] = "HddtodoUI.Controls.TaskRestartDialog";
            _typeNameTable[94] = "HddtodoUI.BackendModels.TaskRestart";
            _typeNameTable[95] = "HddtodoUI.Controls.TaskSearchDialog";
            _typeNameTable[96] = "HddtodoUI.Converters.CategoryConverter";
            _typeNameTable[97] = "HddtodoUI.Converters.EmptyCollectionToVisibilityConverter";
            _typeNameTable[98] = "HddtodoUI.Selectors.TaskListItemTemplateSelector";
            _typeNameTable[99] = "Microsoft.UI.Xaml.Controls.Expander";
            _typeNameTable[100] = "Microsoft.UI.Xaml.Controls.ExpandDirection";
            _typeNameTable[101] = "Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings";
            _typeNameTable[102] = "HddtodoUI.Controls.TasksPanel";
            _typeNameTable[103] = "HddtodoUI.Models.TaskCategoryViewObject";
            _typeNameTable[104] = "HddtodoUI.Converters.StringToBrushConverter";
            _typeNameTable[105] = "Microsoft.UI.Xaml.Controls.ProgressBar";
            _typeNameTable[106] = "Microsoft.UI.Xaml.Controls.Primitives.RangeBase";
            _typeNameTable[107] = "Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings";
            _typeNameTable[108] = "HddtodoUI.Controls.TaskTimeLogStatisticsControl";
            _typeNameTable[109] = "HddtodoUI.Views.MainView";
            _typeNameTable[110] = "HddtodoUI.MainWindow";
            _typeNameTable[111] = "Microsoft.UI.Xaml.Window";
            _typeNameTable[112] = "HddtodoUI.Windows.IndicatorWindow";
            _typeNameTable[113] = "HddtodoUI.Windows.LoginWindow";
            _typeNameTable[114] = "HddtodoUI.Windows.NotificationWindow";
            _typeNameTable[115] = "HddtodoUI.Windows.NotificationLevel";
            _typeNameTable[116] = "HddtodoUI.Windows.QuickAddWindow";
            _typeNameTable[117] = "HddtodoUI.Windows.TaskDetailsWindow";
            _typeNameTable[118] = "Microsoft.UI.Windowing.AppWindow";
            _typeNameTable[119] = "HddtodoUI.Windows.TaskSearchWindow";
            _typeNameTable[120] = "HddtodoUI.Windows.TaskStepWindow";
            _typeNameTable[121] = "Windows.UI.Xaml.Controls.TreeViewNode";
            _typeNameTable[122] = "Windows.UI.Xaml.DependencyObject";
            _typeNameTable[123] = "System.Collections.Generic.IList`1<Windows.UI.Xaml.Controls.TreeViewNode>";
            _typeNameTable[124] = "Windows.UI.Core.CoreDispatcher";

            _typeTable = new global::System.Type[125];
            _typeTable[0] = typeof(global::Microsoft.UI.Xaml.Controls.XamlControlsResources);
            _typeTable[1] = typeof(global::Microsoft.UI.Xaml.ResourceDictionary);
            _typeTable[2] = typeof(global::System.Object);
            _typeTable[3] = typeof(global::System.Boolean);
            _typeTable[4] = typeof(global::HddtodoUI.Converters.DateToStringConverter);
            _typeTable[5] = typeof(global::HddtodoUI.Converters.TaskStatusTooltipConverter);
            _typeTable[6] = typeof(global::HddtodoUI.Converters.TaskStatusIconConverter);
            _typeTable[7] = typeof(global::HddtodoUI.Converters.TaskStatusColorConverter);
            _typeTable[8] = typeof(global::HddtodoUI.Converters.NullableDateTimeToCheckedConverter);
            _typeTable[9] = typeof(global::HddtodoUI.Converters.NullableDateTimeToStrikethroughConverter);
            _typeTable[10] = typeof(global::HddtodoUI.Converters.DateTimeConverter);
            _typeTable[11] = typeof(global::HddtodoUI.Controls.CompletedTaskListsDialog);
            _typeTable[12] = typeof(global::Microsoft.UI.Xaml.Controls.UserControl);
            _typeTable[13] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressRing);
            _typeTable[14] = typeof(global::Microsoft.UI.Xaml.Controls.Control);
            _typeTable[15] = typeof(global::System.Double);
            _typeTable[16] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings);
            _typeTable[17] = typeof(global::Microsoft.UI.Xaml.DependencyObject);
            _typeTable[18] = typeof(global::HddtodoUI.Controls.LoginControl);
            _typeTable[19] = typeof(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl);
            _typeTable[20] = typeof(global::HddtodoUI.BackendModels.TaskCategory);
            _typeTable[21] = typeof(global::System.Int32);
            _typeTable[22] = typeof(global::System.String);
            _typeTable[23] = typeof(global::HddtodoUI.Controls.PlannedProjectsPanel);
            _typeTable[24] = typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>);
            _typeTable[25] = typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>);
            _typeTable[26] = typeof(global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount);
            _typeTable[27] = typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>);
            _typeTable[28] = typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>);
            _typeTable[29] = typeof(global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>);
            _typeTable[30] = typeof(global::HddtodoUI.Controls.QuickAddTaskDialog);
            _typeTable[31] = typeof(global::HddtodoUI.Controls.SettingsDialog);
            _typeTable[32] = typeof(global::HddtodoUI.Controls.TaskItemControl);
            _typeTable[33] = typeof(global::HddtodoUI.Models.TodoTaskViewObject);
            _typeTable[34] = typeof(global::HddtodoUI.Controls.SubtasksControl);
            _typeTable[35] = typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>);
            _typeTable[36] = typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Models.TodoTaskViewObject>);
            _typeTable[37] = typeof(global::System.Int64);
            _typeTable[38] = typeof(global::System.Nullable<global::System.DateTime>);
            _typeTable[39] = typeof(global::System.ValueType);
            _typeTable[40] = typeof(global::System.DateTime);
            _typeTable[41] = typeof(global::HddtodoUI.BackendModels.TaskPriority);
            _typeTable[42] = typeof(global::System.Enum);
            _typeTable[43] = typeof(global::HddtodoUI.Models.TaskStatus);
            _typeTable[44] = typeof(global::HddtodoUI.BackendModels.TTask);
            _typeTable[45] = typeof(global::Microsoft.UI.Xaml.Visibility);
            _typeTable[46] = typeof(global::HddtodoUI.Converters.DueDateColorConverter);
            _typeTable[47] = typeof(global::HddtodoUI.Converters.InboxVisibilityConverter);
            _typeTable[48] = typeof(global::HddtodoUI.Converters.HasDateVisibilityConverter);
            _typeTable[49] = typeof(global::Microsoft.UI.Xaml.Controls.TreeView);
            _typeTable[50] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode);
            _typeTable[51] = typeof(global::Microsoft.UI.Xaml.DataTemplate);
            _typeTable[52] = typeof(global::Microsoft.UI.Xaml.Style);
            _typeTable[53] = typeof(global::Microsoft.UI.Xaml.Controls.StyleSelector);
            _typeTable[54] = typeof(global::Microsoft.UI.Xaml.Media.Animation.TransitionCollection);
            _typeTable[55] = typeof(global::Microsoft.UI.Xaml.Controls.DataTemplateSelector);
            _typeTable[56] = typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>);
            _typeTable[57] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode);
            _typeTable[58] = typeof(global::System.Collections.Generic.IList<global::System.Object>);
            _typeTable[59] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewItem);
            _typeTable[60] = typeof(global::Microsoft.UI.Xaml.Controls.ListViewItem);
            _typeTable[61] = typeof(global::Microsoft.UI.Xaml.Controls.ContentControl);
            _typeTable[62] = typeof(global::Microsoft.UI.Xaml.Media.Brush);
            _typeTable[63] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings);
            _typeTable[64] = typeof(global::HddtodoUI.Controls.TaskCategoriesPanel);
            _typeTable[65] = typeof(global::HddtodoUI.Converters.TreeNodeGlyphConverter);
            _typeTable[66] = typeof(global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel);
            _typeTable[67] = typeof(global::HddtodoUI.Converters.StringToVisibilityConverter);
            _typeTable[68] = typeof(global::HddtodoUI.Converters.DateTimeToBrushConverter);
            _typeTable[69] = typeof(global::HddtodoUI.Converters.IntToVisibilityConverter);
            _typeTable[70] = typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter);
            _typeTable[71] = typeof(global::CommunityToolkit.WinUI.Controls.SizerBase);
            _typeTable[72] = typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior);
            _typeTable[73] = typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection);
            _typeTable[74] = typeof(global::Microsoft.UI.Input.InputSystemCursorShape);
            _typeTable[75] = typeof(global::Microsoft.UI.Xaml.Controls.Orientation);
            _typeTable[76] = typeof(global::HddtodoUI.Controls.TaskStepsControl);
            _typeTable[77] = typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.TaskStep>);
            _typeTable[78] = typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.TaskStep>);
            _typeTable[79] = typeof(global::HddtodoUI.BackendModels.TaskStep);
            _typeTable[80] = typeof(global::Microsoft.UI.Xaml.Controls.BreadcrumbBar);
            _typeTable[81] = typeof(global::Microsoft.UI.Xaml.Controls.BreadcrumbBarItem);
            _typeTable[82] = typeof(global::HddtodoUI.Controls.TaskDetailsControl);
            _typeTable[83] = typeof(global::Microsoft.UI.Xaml.Controls.NumberBox);
            _typeTable[84] = typeof(global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode);
            _typeTable[85] = typeof(global::Windows.Globalization.NumberFormatting.INumberFormatter2);
            _typeTable[86] = typeof(global::Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase);
            _typeTable[87] = typeof(global::Microsoft.UI.Xaml.Media.SolidColorBrush);
            _typeTable[88] = typeof(global::Microsoft.UI.Xaml.TextReadingOrder);
            _typeTable[89] = typeof(global::Microsoft.UI.Xaml.Controls.NumberBoxValidationMode);
            _typeTable[90] = typeof(global::HddtodoUI.Controls.TaskReminderDialog);
            _typeTable[91] = typeof(global::Microsoft.UI.Xaml.Controls.ContentDialog);
            _typeTable[92] = typeof(global::HddtodoUI.BackendModels.TaskReminder);
            _typeTable[93] = typeof(global::HddtodoUI.Controls.TaskRestartDialog);
            _typeTable[94] = typeof(global::HddtodoUI.BackendModels.TaskRestart);
            _typeTable[95] = typeof(global::HddtodoUI.Controls.TaskSearchDialog);
            _typeTable[96] = typeof(global::HddtodoUI.Converters.CategoryConverter);
            _typeTable[97] = typeof(global::HddtodoUI.Converters.EmptyCollectionToVisibilityConverter);
            _typeTable[98] = typeof(global::HddtodoUI.Selectors.TaskListItemTemplateSelector);
            _typeTable[99] = typeof(global::Microsoft.UI.Xaml.Controls.Expander);
            _typeTable[100] = typeof(global::Microsoft.UI.Xaml.Controls.ExpandDirection);
            _typeTable[101] = typeof(global::Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings);
            _typeTable[102] = typeof(global::HddtodoUI.Controls.TasksPanel);
            _typeTable[103] = typeof(global::HddtodoUI.Models.TaskCategoryViewObject);
            _typeTable[104] = typeof(global::HddtodoUI.Converters.StringToBrushConverter);
            _typeTable[105] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressBar);
            _typeTable[106] = typeof(global::Microsoft.UI.Xaml.Controls.Primitives.RangeBase);
            _typeTable[107] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings);
            _typeTable[108] = typeof(global::HddtodoUI.Controls.TaskTimeLogStatisticsControl);
            _typeTable[109] = typeof(global::HddtodoUI.Views.MainView);
            _typeTable[110] = typeof(global::HddtodoUI.MainWindow);
            _typeTable[111] = typeof(global::Microsoft.UI.Xaml.Window);
            _typeTable[112] = typeof(global::HddtodoUI.Windows.IndicatorWindow);
            _typeTable[113] = typeof(global::HddtodoUI.Windows.LoginWindow);
            _typeTable[114] = typeof(global::HddtodoUI.Windows.NotificationWindow);
            _typeTable[115] = typeof(global::HddtodoUI.Windows.NotificationLevel);
            _typeTable[116] = typeof(global::HddtodoUI.Windows.QuickAddWindow);
            _typeTable[117] = typeof(global::HddtodoUI.Windows.TaskDetailsWindow);
            _typeTable[118] = typeof(global::Microsoft.UI.Windowing.AppWindow);
            _typeTable[119] = typeof(global::HddtodoUI.Windows.TaskSearchWindow);
            _typeTable[120] = typeof(global::HddtodoUI.Windows.TaskStepWindow);
            _typeTable[121] = typeof(global::Windows.UI.Xaml.Controls.TreeViewNode);
            _typeTable[122] = typeof(global::Windows.UI.Xaml.DependencyObject);
            _typeTable[123] = typeof(global::System.Collections.Generic.IList<global::Windows.UI.Xaml.Controls.TreeViewNode>);
            _typeTable[124] = typeof(global::Windows.UI.Core.CoreDispatcher);
        }

        private int LookupTypeIndexByName(string typeName)
        {
            if (_typeNameTable == null)
            {
                InitTypeTables();
            }
            for (int i=0; i<_typeNameTable.Length; i++)
            {
                if(0 == string.CompareOrdinal(_typeNameTable[i], typeName))
                {
                    return i;
                }
            }
            return -1;
        }

        private int LookupTypeIndexByType(global::System.Type type)
        {
            if (_typeTable == null)
            {
                InitTypeTables();
            }
            for(int i=0; i<_typeTable.Length; i++)
            {
                if(type == _typeTable[i])
                {
                    return i;
                }
            }
            return -1;
        }

        private object Activate_0_XamlControlsResources() { return new global::Microsoft.UI.Xaml.Controls.XamlControlsResources(); }
        private object Activate_4_DateToStringConverter() { return new global::HddtodoUI.Converters.DateToStringConverter(); }
        private object Activate_5_TaskStatusTooltipConverter() { return new global::HddtodoUI.Converters.TaskStatusTooltipConverter(); }
        private object Activate_6_TaskStatusIconConverter() { return new global::HddtodoUI.Converters.TaskStatusIconConverter(); }
        private object Activate_7_TaskStatusColorConverter() { return new global::HddtodoUI.Converters.TaskStatusColorConverter(); }
        private object Activate_8_NullableDateTimeToCheckedConverter() { return new global::HddtodoUI.Converters.NullableDateTimeToCheckedConverter(); }
        private object Activate_9_NullableDateTimeToStrikethroughConverter() { return new global::HddtodoUI.Converters.NullableDateTimeToStrikethroughConverter(); }
        private object Activate_10_DateTimeConverter() { return new global::HddtodoUI.Converters.DateTimeConverter(); }
        private object Activate_11_CompletedTaskListsDialog() { return new global::HddtodoUI.Controls.CompletedTaskListsDialog(); }
        private object Activate_13_ProgressRing() { return new global::Microsoft.UI.Xaml.Controls.ProgressRing(); }
        private object Activate_18_LoginControl() { return new global::HddtodoUI.Controls.LoginControl(); }
        private object Activate_19_TaskCategoryItemForPlanControl() { return new global::HddtodoUI.Controls.TaskCategoryItemForPlanControl(); }
        private object Activate_20_TaskCategory() { return new global::HddtodoUI.BackendModels.TaskCategory(); }
        private object Activate_23_PlannedProjectsPanel() { return new global::HddtodoUI.Controls.PlannedProjectsPanel(); }
        private object Activate_24_ObservableCollection() { return new global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(); }
        private object Activate_25_Collection() { return new global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(); }
        private object Activate_26_TaskCategoryWithCount() { return new global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount(); }
        private object Activate_27_ObservableCollection() { return new global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>(); }
        private object Activate_28_Collection() { return new global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>(); }
        private object Activate_30_QuickAddTaskDialog() { return new global::HddtodoUI.Controls.QuickAddTaskDialog(); }
        private object Activate_31_SettingsDialog() { return new global::HddtodoUI.Controls.SettingsDialog(); }
        private object Activate_32_TaskItemControl() { return new global::HddtodoUI.Controls.TaskItemControl(); }
        private object Activate_33_TodoTaskViewObject() { return new global::HddtodoUI.Models.TodoTaskViewObject(); }
        private object Activate_34_SubtasksControl() { return new global::HddtodoUI.Controls.SubtasksControl(); }
        private object Activate_35_ObservableCollection() { return new global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>(); }
        private object Activate_36_Collection() { return new global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Models.TodoTaskViewObject>(); }
        private object Activate_44_TTask() { return new global::HddtodoUI.BackendModels.TTask(); }
        private object Activate_46_DueDateColorConverter() { return new global::HddtodoUI.Converters.DueDateColorConverter(); }
        private object Activate_47_InboxVisibilityConverter() { return new global::HddtodoUI.Converters.InboxVisibilityConverter(); }
        private object Activate_48_HasDateVisibilityConverter() { return new global::HddtodoUI.Converters.HasDateVisibilityConverter(); }
        private object Activate_49_TreeView() { return new global::Microsoft.UI.Xaml.Controls.TreeView(); }
        private object Activate_57_TreeViewNode() { return new global::Microsoft.UI.Xaml.Controls.TreeViewNode(); }
        private object Activate_59_TreeViewItem() { return new global::Microsoft.UI.Xaml.Controls.TreeViewItem(); }
        private object Activate_63_TreeViewItemTemplateSettings() { return new global::Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings(); }
        private object Activate_64_TaskCategoriesPanel() { return new global::HddtodoUI.Controls.TaskCategoriesPanel(); }
        private object Activate_65_TreeNodeGlyphConverter() { return new global::HddtodoUI.Converters.TreeNodeGlyphConverter(); }
        private object Activate_66_TaskCategoriesTreeViewPanel() { return new global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel(); }
        private object Activate_67_StringToVisibilityConverter() { return new global::HddtodoUI.Converters.StringToVisibilityConverter(); }
        private object Activate_68_DateTimeToBrushConverter() { return new global::HddtodoUI.Converters.DateTimeToBrushConverter(); }
        private object Activate_69_IntToVisibilityConverter() { return new global::HddtodoUI.Converters.IntToVisibilityConverter(); }
        private object Activate_70_GridSplitter() { return new global::CommunityToolkit.WinUI.Controls.GridSplitter(); }
        private object Activate_76_TaskStepsControl() { return new global::HddtodoUI.Controls.TaskStepsControl(); }
        private object Activate_77_ObservableCollection() { return new global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.TaskStep>(); }
        private object Activate_78_Collection() { return new global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.TaskStep>(); }
        private object Activate_80_BreadcrumbBar() { return new global::Microsoft.UI.Xaml.Controls.BreadcrumbBar(); }
        private object Activate_81_BreadcrumbBarItem() { return new global::Microsoft.UI.Xaml.Controls.BreadcrumbBarItem(); }
        private object Activate_82_TaskDetailsControl() { return new global::HddtodoUI.Controls.TaskDetailsControl(); }
        private object Activate_83_NumberBox() { return new global::Microsoft.UI.Xaml.Controls.NumberBox(); }
        private object Activate_90_TaskReminderDialog() { return new global::HddtodoUI.Controls.TaskReminderDialog(); }
        private object Activate_92_TaskReminder() { return new global::HddtodoUI.BackendModels.TaskReminder(); }
        private object Activate_93_TaskRestartDialog() { return new global::HddtodoUI.Controls.TaskRestartDialog(); }
        private object Activate_94_TaskRestart() { return new global::HddtodoUI.BackendModels.TaskRestart(); }
        private object Activate_95_TaskSearchDialog() { return new global::HddtodoUI.Controls.TaskSearchDialog(); }
        private object Activate_96_CategoryConverter() { return new global::HddtodoUI.Converters.CategoryConverter(); }
        private object Activate_97_EmptyCollectionToVisibilityConverter() { return new global::HddtodoUI.Converters.EmptyCollectionToVisibilityConverter(); }
        private object Activate_98_TaskListItemTemplateSelector() { return new global::HddtodoUI.Selectors.TaskListItemTemplateSelector(); }
        private object Activate_99_Expander() { return new global::Microsoft.UI.Xaml.Controls.Expander(); }
        private object Activate_102_TasksPanel() { return new global::HddtodoUI.Controls.TasksPanel(); }
        private object Activate_103_TaskCategoryViewObject() { return new global::HddtodoUI.Models.TaskCategoryViewObject(); }
        private object Activate_104_StringToBrushConverter() { return new global::HddtodoUI.Converters.StringToBrushConverter(); }
        private object Activate_105_ProgressBar() { return new global::Microsoft.UI.Xaml.Controls.ProgressBar(); }
        private object Activate_108_TaskTimeLogStatisticsControl() { return new global::HddtodoUI.Controls.TaskTimeLogStatisticsControl(); }
        private object Activate_109_MainView() { return new global::HddtodoUI.Views.MainView(); }
        private object Activate_110_MainWindow() { return new global::HddtodoUI.MainWindow(); }
        private object Activate_112_IndicatorWindow() { return new global::HddtodoUI.Windows.IndicatorWindow(); }
        private object Activate_113_LoginWindow() { return new global::HddtodoUI.Windows.LoginWindow(); }
        private object Activate_116_QuickAddWindow() { return new global::HddtodoUI.Windows.QuickAddWindow(); }
        private object Activate_117_TaskDetailsWindow() { return new global::HddtodoUI.Windows.TaskDetailsWindow(); }
        private object Activate_119_TaskSearchWindow() { return new global::HddtodoUI.Windows.TaskSearchWindow(); }
        private object Activate_120_TaskStepWindow() { return new global::HddtodoUI.Windows.TaskStepWindow(); }
        private object Activate_121_TreeViewNode() { return new global::Windows.UI.Xaml.Controls.TreeViewNode(); }
        private void StaticInitializer_0_XamlControlsResources() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.XamlControlsResources).TypeHandle);
        private void StaticInitializer_4_DateToStringConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.DateToStringConverter).TypeHandle);
        private void StaticInitializer_5_TaskStatusTooltipConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.TaskStatusTooltipConverter).TypeHandle);
        private void StaticInitializer_6_TaskStatusIconConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.TaskStatusIconConverter).TypeHandle);
        private void StaticInitializer_7_TaskStatusColorConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.TaskStatusColorConverter).TypeHandle);
        private void StaticInitializer_8_NullableDateTimeToCheckedConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.NullableDateTimeToCheckedConverter).TypeHandle);
        private void StaticInitializer_9_NullableDateTimeToStrikethroughConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.NullableDateTimeToStrikethroughConverter).TypeHandle);
        private void StaticInitializer_10_DateTimeConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.DateTimeConverter).TypeHandle);
        private void StaticInitializer_11_CompletedTaskListsDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.CompletedTaskListsDialog).TypeHandle);
        private void StaticInitializer_13_ProgressRing() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressRing).TypeHandle);
        private void StaticInitializer_16_ProgressRingTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings).TypeHandle);
        private void StaticInitializer_18_LoginControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.LoginControl).TypeHandle);
        private void StaticInitializer_19_TaskCategoryItemForPlanControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl).TypeHandle);
        private void StaticInitializer_20_TaskCategory() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TaskCategory).TypeHandle);
        private void StaticInitializer_23_PlannedProjectsPanel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.PlannedProjectsPanel).TypeHandle);
        private void StaticInitializer_24_ObservableCollection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>).TypeHandle);
        private void StaticInitializer_25_Collection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>).TypeHandle);
        private void StaticInitializer_26_TaskCategoryWithCount() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount).TypeHandle);
        private void StaticInitializer_27_ObservableCollection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>).TypeHandle);
        private void StaticInitializer_28_Collection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>).TypeHandle);
        private void StaticInitializer_29_Grouping() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>).TypeHandle);
        private void StaticInitializer_30_QuickAddTaskDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.QuickAddTaskDialog).TypeHandle);
        private void StaticInitializer_31_SettingsDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.SettingsDialog).TypeHandle);
        private void StaticInitializer_32_TaskItemControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskItemControl).TypeHandle);
        private void StaticInitializer_33_TodoTaskViewObject() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Models.TodoTaskViewObject).TypeHandle);
        private void StaticInitializer_34_SubtasksControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.SubtasksControl).TypeHandle);
        private void StaticInitializer_35_ObservableCollection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>).TypeHandle);
        private void StaticInitializer_36_Collection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.Models.TodoTaskViewObject>).TypeHandle);
        private void StaticInitializer_38_Nullable() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Nullable<global::System.DateTime>).TypeHandle);
        private void StaticInitializer_39_ValueType() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.ValueType).TypeHandle);
        private void StaticInitializer_40_DateTime() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.DateTime).TypeHandle);
        private void StaticInitializer_41_TaskPriority() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TaskPriority).TypeHandle);
        private void StaticInitializer_42_Enum() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Enum).TypeHandle);
        private void StaticInitializer_43_TaskStatus() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Models.TaskStatus).TypeHandle);
        private void StaticInitializer_44_TTask() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TTask).TypeHandle);
        private void StaticInitializer_46_DueDateColorConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.DueDateColorConverter).TypeHandle);
        private void StaticInitializer_47_InboxVisibilityConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.InboxVisibilityConverter).TypeHandle);
        private void StaticInitializer_48_HasDateVisibilityConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.HasDateVisibilityConverter).TypeHandle);
        private void StaticInitializer_49_TreeView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeView).TypeHandle);
        private void StaticInitializer_50_TreeViewSelectionMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode).TypeHandle);
        private void StaticInitializer_56_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>).TypeHandle);
        private void StaticInitializer_57_TreeViewNode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode).TypeHandle);
        private void StaticInitializer_58_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::System.Object>).TypeHandle);
        private void StaticInitializer_59_TreeViewItem() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewItem).TypeHandle);
        private void StaticInitializer_63_TreeViewItemTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings).TypeHandle);
        private void StaticInitializer_64_TaskCategoriesPanel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskCategoriesPanel).TypeHandle);
        private void StaticInitializer_65_TreeNodeGlyphConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.TreeNodeGlyphConverter).TypeHandle);
        private void StaticInitializer_66_TaskCategoriesTreeViewPanel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel).TypeHandle);
        private void StaticInitializer_67_StringToVisibilityConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.StringToVisibilityConverter).TypeHandle);
        private void StaticInitializer_68_DateTimeToBrushConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.DateTimeToBrushConverter).TypeHandle);
        private void StaticInitializer_69_IntToVisibilityConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.IntToVisibilityConverter).TypeHandle);
        private void StaticInitializer_70_GridSplitter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter).TypeHandle);
        private void StaticInitializer_71_SizerBase() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::CommunityToolkit.WinUI.Controls.SizerBase).TypeHandle);
        private void StaticInitializer_72_GridResizeBehavior() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior).TypeHandle);
        private void StaticInitializer_73_GridResizeDirection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection).TypeHandle);
        private void StaticInitializer_74_InputSystemCursorShape() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Input.InputSystemCursorShape).TypeHandle);
        private void StaticInitializer_76_TaskStepsControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskStepsControl).TypeHandle);
        private void StaticInitializer_77_ObservableCollection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.BackendModels.TaskStep>).TypeHandle);
        private void StaticInitializer_78_Collection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.TaskStep>).TypeHandle);
        private void StaticInitializer_79_TaskStep() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TaskStep).TypeHandle);
        private void StaticInitializer_80_BreadcrumbBar() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.BreadcrumbBar).TypeHandle);
        private void StaticInitializer_81_BreadcrumbBarItem() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.BreadcrumbBarItem).TypeHandle);
        private void StaticInitializer_82_TaskDetailsControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskDetailsControl).TypeHandle);
        private void StaticInitializer_83_NumberBox() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NumberBox).TypeHandle);
        private void StaticInitializer_84_NumberBoxSpinButtonPlacementMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode).TypeHandle);
        private void StaticInitializer_85_INumberFormatter2() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Windows.Globalization.NumberFormatting.INumberFormatter2).TypeHandle);
        private void StaticInitializer_89_NumberBoxValidationMode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.NumberBoxValidationMode).TypeHandle);
        private void StaticInitializer_90_TaskReminderDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskReminderDialog).TypeHandle);
        private void StaticInitializer_92_TaskReminder() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TaskReminder).TypeHandle);
        private void StaticInitializer_93_TaskRestartDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskRestartDialog).TypeHandle);
        private void StaticInitializer_94_TaskRestart() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.BackendModels.TaskRestart).TypeHandle);
        private void StaticInitializer_95_TaskSearchDialog() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskSearchDialog).TypeHandle);
        private void StaticInitializer_96_CategoryConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.CategoryConverter).TypeHandle);
        private void StaticInitializer_97_EmptyCollectionToVisibilityConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.EmptyCollectionToVisibilityConverter).TypeHandle);
        private void StaticInitializer_98_TaskListItemTemplateSelector() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Selectors.TaskListItemTemplateSelector).TypeHandle);
        private void StaticInitializer_99_Expander() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.Expander).TypeHandle);
        private void StaticInitializer_100_ExpandDirection() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ExpandDirection).TypeHandle);
        private void StaticInitializer_101_ExpanderTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings).TypeHandle);
        private void StaticInitializer_102_TasksPanel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TasksPanel).TypeHandle);
        private void StaticInitializer_103_TaskCategoryViewObject() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Models.TaskCategoryViewObject).TypeHandle);
        private void StaticInitializer_104_StringToBrushConverter() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Converters.StringToBrushConverter).TypeHandle);
        private void StaticInitializer_105_ProgressBar() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressBar).TypeHandle);
        private void StaticInitializer_107_ProgressBarTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings).TypeHandle);
        private void StaticInitializer_108_TaskTimeLogStatisticsControl() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Controls.TaskTimeLogStatisticsControl).TypeHandle);
        private void StaticInitializer_109_MainView() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Views.MainView).TypeHandle);
        private void StaticInitializer_110_MainWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.MainWindow).TypeHandle);
        private void StaticInitializer_112_IndicatorWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.IndicatorWindow).TypeHandle);
        private void StaticInitializer_113_LoginWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.LoginWindow).TypeHandle);
        private void StaticInitializer_114_NotificationWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.NotificationWindow).TypeHandle);
        private void StaticInitializer_115_NotificationLevel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.NotificationLevel).TypeHandle);
        private void StaticInitializer_116_QuickAddWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.QuickAddWindow).TypeHandle);
        private void StaticInitializer_117_TaskDetailsWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.TaskDetailsWindow).TypeHandle);
        private void StaticInitializer_118_AppWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Windowing.AppWindow).TypeHandle);
        private void StaticInitializer_119_TaskSearchWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.TaskSearchWindow).TypeHandle);
        private void StaticInitializer_120_TaskStepWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::HddtodoUI.Windows.TaskStepWindow).TypeHandle);
        private void StaticInitializer_121_TreeViewNode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Windows.UI.Xaml.Controls.TreeViewNode).TypeHandle);
        private void StaticInitializer_122_DependencyObject() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Windows.UI.Xaml.DependencyObject).TypeHandle);
        private void StaticInitializer_123_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::Windows.UI.Xaml.Controls.TreeViewNode>).TypeHandle);
        private void StaticInitializer_124_CoreDispatcher() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Windows.UI.Core.CoreDispatcher).TypeHandle);
        private void MapAdd_0_XamlControlsResources(object instance, object key, object item)
        {
            var collection = (global::System.Collections.Generic.IDictionary<global::System.Object, global::System.Object>)instance;
            var newKey = (global::System.Object)key;
            var newItem = (global::System.Object)item;
            collection.Add(newKey, newItem);
        }
        private void VectorAdd_24_ObservableCollection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)instance;
            var newItem = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)item;
            collection.Add(newItem);
        }
        private void VectorAdd_25_Collection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)instance;
            var newItem = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)item;
            collection.Add(newItem);
        }
        private void VectorAdd_27_ObservableCollection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>)instance;
            var newItem = (global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)item;
            collection.Add(newItem);
        }
        private void VectorAdd_28_Collection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>)instance;
            var newItem = (global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)item;
            collection.Add(newItem);
        }
        private void VectorAdd_29_Grouping(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)instance;
            var newItem = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)item;
            collection.Add(newItem);
        }
        private void VectorAdd_35_ObservableCollection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.Models.TodoTaskViewObject>)instance;
            var newItem = (global::HddtodoUI.Models.TodoTaskViewObject)item;
            collection.Add(newItem);
        }
        private void VectorAdd_36_Collection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.Models.TodoTaskViewObject>)instance;
            var newItem = (global::HddtodoUI.Models.TodoTaskViewObject)item;
            collection.Add(newItem);
        }
        private void VectorAdd_56_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::Microsoft.UI.Xaml.Controls.TreeViewNode>)instance;
            var newItem = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)item;
            collection.Add(newItem);
        }
        private void VectorAdd_58_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::System.Object>)instance;
            var newItem = (global::System.Object)item;
            collection.Add(newItem);
        }
        private void VectorAdd_77_ObservableCollection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.BackendModels.TaskStep>)instance;
            var newItem = (global::HddtodoUI.BackendModels.TaskStep)item;
            collection.Add(newItem);
        }
        private void VectorAdd_78_Collection(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::HddtodoUI.BackendModels.TaskStep>)instance;
            var newItem = (global::HddtodoUI.BackendModels.TaskStep)item;
            collection.Add(newItem);
        }
        private void VectorAdd_123_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::Windows.UI.Xaml.Controls.TreeViewNode>)instance;
            var newItem = (global::Windows.UI.Xaml.Controls.TreeViewNode)item;
            collection.Add(newItem);
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CreateXamlType(int typeIndex)
        {
            global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType xamlType = null;
            global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType userType;
            string typeName = _typeNameTable[typeIndex];
            global::System.Type type = _typeTable[typeIndex];

            switch (typeIndex)
            {

            case 0:   //  Microsoft.UI.Xaml.Controls.XamlControlsResources
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.ResourceDictionary"));
                userType.Activator = Activate_0_XamlControlsResources;
                userType.StaticInitializer = StaticInitializer_0_XamlControlsResources;
                userType.DictionaryAdd = MapAdd_0_XamlControlsResources;
                userType.AddMemberName("UseCompactResources");
                xamlType = userType;
                break;

            case 1:   //  Microsoft.UI.Xaml.ResourceDictionary
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 2:   //  Object
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 3:   //  Boolean
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 4:   //  HddtodoUI.Converters.DateToStringConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_4_DateToStringConverter;
                userType.StaticInitializer = StaticInitializer_4_DateToStringConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 5:   //  HddtodoUI.Converters.TaskStatusTooltipConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_5_TaskStatusTooltipConverter;
                userType.StaticInitializer = StaticInitializer_5_TaskStatusTooltipConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 6:   //  HddtodoUI.Converters.TaskStatusIconConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_6_TaskStatusIconConverter;
                userType.StaticInitializer = StaticInitializer_6_TaskStatusIconConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 7:   //  HddtodoUI.Converters.TaskStatusColorConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_7_TaskStatusColorConverter;
                userType.StaticInitializer = StaticInitializer_7_TaskStatusColorConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 8:   //  HddtodoUI.Converters.NullableDateTimeToCheckedConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_8_NullableDateTimeToCheckedConverter;
                userType.StaticInitializer = StaticInitializer_8_NullableDateTimeToCheckedConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 9:   //  HddtodoUI.Converters.NullableDateTimeToStrikethroughConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_9_NullableDateTimeToStrikethroughConverter;
                userType.StaticInitializer = StaticInitializer_9_NullableDateTimeToStrikethroughConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 10:   //  HddtodoUI.Converters.DateTimeConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_10_DateTimeConverter;
                userType.StaticInitializer = StaticInitializer_10_DateTimeConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 11:   //  HddtodoUI.Controls.CompletedTaskListsDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_11_CompletedTaskListsDialog;
                userType.StaticInitializer = StaticInitializer_11_CompletedTaskListsDialog;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 12:   //  Microsoft.UI.Xaml.Controls.UserControl
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 13:   //  Microsoft.UI.Xaml.Controls.ProgressRing
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Control"));
                userType.Activator = Activate_13_ProgressRing;
                userType.StaticInitializer = StaticInitializer_13_ProgressRing;
                userType.AddMemberName("IsActive");
                userType.AddMemberName("IsIndeterminate");
                userType.AddMemberName("Maximum");
                userType.AddMemberName("Minimum");
                userType.AddMemberName("TemplateSettings");
                userType.AddMemberName("Value");
                xamlType = userType;
                break;

            case 14:   //  Microsoft.UI.Xaml.Controls.Control
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 15:   //  Double
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 16:   //  Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_16_ProgressRingTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 17:   //  Microsoft.UI.Xaml.DependencyObject
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 18:   //  HddtodoUI.Controls.LoginControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_18_LoginControl;
                userType.StaticInitializer = StaticInitializer_18_LoginControl;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 19:   //  HddtodoUI.Controls.TaskCategoryItemForPlanControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_19_TaskCategoryItemForPlanControl;
                userType.StaticInitializer = StaticInitializer_19_TaskCategoryItemForPlanControl;
                userType.AddMemberName("Category");
                userType.AddMemberName("CategoryCount");
                userType.AddMemberName("TaskCount");
                userType.AddMemberName("ParentPath");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 20:   //  HddtodoUI.BackendModels.TaskCategory
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_20_TaskCategory;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 21:   //  Int32
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 22:   //  String
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 23:   //  HddtodoUI.Controls.PlannedProjectsPanel
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_23_PlannedProjectsPanel;
                userType.StaticInitializer = StaticInitializer_23_PlannedProjectsPanel;
                userType.AddMemberName("PlannedCategories");
                userType.AddMemberName("GroupedPlannedCategories");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 24:   //  System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>"));
                userType.Activator = Activate_24_ObservableCollection;
                userType.StaticInitializer = StaticInitializer_24_ObservableCollection;
                userType.CollectionAdd = VectorAdd_24_ObservableCollection;
                xamlType = userType;
                break;

            case 25:   //  System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_25_Collection;
                userType.StaticInitializer = StaticInitializer_25_Collection;
                userType.CollectionAdd = VectorAdd_25_Collection;
                userType.AddMemberName("Count");
                xamlType = userType;
                break;

            case 26:   //  HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_26_TaskCategoryWithCount;
                userType.StaticInitializer = StaticInitializer_26_TaskCategoryWithCount;
                userType.AddMemberName("Category");
                userType.AddMemberName("SubcategoryCount");
                userType.AddMemberName("TaskCount");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 27:   //  System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Collections.ObjectModel.Collection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>"));
                userType.StaticInitializer = StaticInitializer_27_ObservableCollection;
                userType.CollectionAdd = VectorAdd_27_ObservableCollection;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 28:   //  System.Collections.ObjectModel.Collection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_28_Collection;
                userType.StaticInitializer = StaticInitializer_28_Collection;
                userType.CollectionAdd = VectorAdd_28_Collection;
                xamlType = userType;
                break;

            case 29:   //  HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>"));
                userType.StaticInitializer = StaticInitializer_29_Grouping;
                userType.CollectionAdd = VectorAdd_29_Grouping;
                userType.AddMemberName("Key");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 30:   //  HddtodoUI.Controls.QuickAddTaskDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_30_QuickAddTaskDialog;
                userType.StaticInitializer = StaticInitializer_30_QuickAddTaskDialog;
                userType.AddMemberName("IsStartTask");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 31:   //  HddtodoUI.Controls.SettingsDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_31_SettingsDialog;
                userType.StaticInitializer = StaticInitializer_31_SettingsDialog;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 32:   //  HddtodoUI.Controls.TaskItemControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_32_TaskItemControl;
                userType.StaticInitializer = StaticInitializer_32_TaskItemControl;
                userType.AddMemberName("TaskVO");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 33:   //  HddtodoUI.Models.TodoTaskViewObject
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_33_TodoTaskViewObject;
                userType.StaticInitializer = StaticInitializer_33_TodoTaskViewObject;
                userType.AddMemberName("TaskID");
                userType.AddMemberName("Title");
                userType.AddMemberName("DueDate");
                userType.AddMemberName("Category");
                userType.AddMemberName("CategoryName");
                userType.AddMemberName("Priority");
                userType.AddMemberName("Notes");
                userType.AddMemberName("StartTime");
                userType.AddMemberName("Status");
                userType.AddMemberName("TaskOrder");
                userType.AddMemberName("GroupOrder");
                userType.AddMemberName("Color");
                userType.AddMemberName("Task");
                userType.AddMemberName("DeletedStatus");
                userType.AddMemberName("ParentTaskIds");
                userType.AddMemberName("DirectSubTaskCount");
                userType.AddMemberName("IsCompleted");
                userType.AddMemberName("CategoryOrderPath");
                userType.AddMemberName("CompletedDate");
                userType.AddMemberName("DueDateVisibility");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 34:   //  HddtodoUI.Controls.SubtasksControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_34_SubtasksControl;
                userType.StaticInitializer = StaticInitializer_34_SubtasksControl;
                userType.AddMemberName("Subtasks");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 35:   //  System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Models.TodoTaskViewObject>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Collections.ObjectModel.Collection`1<HddtodoUI.Models.TodoTaskViewObject>"));
                userType.StaticInitializer = StaticInitializer_35_ObservableCollection;
                userType.CollectionAdd = VectorAdd_35_ObservableCollection;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 36:   //  System.Collections.ObjectModel.Collection`1<HddtodoUI.Models.TodoTaskViewObject>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_36_Collection;
                userType.StaticInitializer = StaticInitializer_36_Collection;
                userType.CollectionAdd = VectorAdd_36_Collection;
                xamlType = userType;
                break;

            case 37:   //  Int64
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 38:   //  System.Nullable`1<System.DateTime>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.SetBoxedType(GetXamlTypeByName("System.DateTime"));
                userType.BoxInstance = userType.BoxType<global::System.DateTime>;
                userType.StaticInitializer = StaticInitializer_38_Nullable;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 39:   //  System.ValueType
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_39_ValueType;
                xamlType = userType;
                break;

            case 40:   //  System.DateTime
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.StaticInitializer = StaticInitializer_40_DateTime;
                xamlType = userType;
                break;

            case 41:   //  HddtodoUI.BackendModels.TaskPriority
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_41_TaskPriority;
                userType.AddEnumValue("high", global::HddtodoUI.BackendModels.TaskPriority.high);
                userType.AddEnumValue("normal", global::HddtodoUI.BackendModels.TaskPriority.normal);
                userType.AddEnumValue("low", global::HddtodoUI.BackendModels.TaskPriority.low);
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 42:   //  System.Enum
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.StaticInitializer = StaticInitializer_42_Enum;
                xamlType = userType;
                break;

            case 43:   //  HddtodoUI.Models.TaskStatus
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_43_TaskStatus;
                userType.AddEnumValue("NotStarted", global::HddtodoUI.Models.TaskStatus.NotStarted);
                userType.AddEnumValue("InProgress", global::HddtodoUI.Models.TaskStatus.InProgress);
                userType.AddEnumValue("Completed", global::HddtodoUI.Models.TaskStatus.Completed);
                userType.AddEnumValue("Paused", global::HddtodoUI.Models.TaskStatus.Paused);
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 44:   //  HddtodoUI.BackendModels.TTask
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_44_TTask;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 45:   //  Microsoft.UI.Xaml.Visibility
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 46:   //  HddtodoUI.Converters.DueDateColorConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_46_DueDateColorConverter;
                userType.StaticInitializer = StaticInitializer_46_DueDateColorConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 47:   //  HddtodoUI.Converters.InboxVisibilityConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_47_InboxVisibilityConverter;
                userType.StaticInitializer = StaticInitializer_47_InboxVisibilityConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 48:   //  HddtodoUI.Converters.HasDateVisibilityConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_48_HasDateVisibilityConverter;
                userType.StaticInitializer = StaticInitializer_48_HasDateVisibilityConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 49:   //  Microsoft.UI.Xaml.Controls.TreeView
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Control"));
                userType.Activator = Activate_49_TreeView;
                userType.StaticInitializer = StaticInitializer_49_TreeView;
                userType.AddMemberName("SelectionMode");
                userType.AddMemberName("ItemsSource");
                userType.AddMemberName("ItemTemplate");
                userType.AddMemberName("CanDragItems");
                userType.AddMemberName("CanReorderItems");
                userType.AddMemberName("ItemContainerStyle");
                userType.AddMemberName("ItemContainerStyleSelector");
                userType.AddMemberName("ItemContainerTransitions");
                userType.AddMemberName("ItemTemplateSelector");
                userType.AddMemberName("RootNodes");
                userType.AddMemberName("SelectedItem");
                userType.AddMemberName("SelectedItems");
                userType.AddMemberName("SelectedNode");
                userType.AddMemberName("SelectedNodes");
                xamlType = userType;
                break;

            case 50:   //  Microsoft.UI.Xaml.Controls.TreeViewSelectionMode
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_50_TreeViewSelectionMode;
                userType.AddEnumValue("None", global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.None);
                userType.AddEnumValue("Single", global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.Single);
                userType.AddEnumValue("Multiple", global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.Multiple);
                xamlType = userType;
                break;

            case 51:   //  Microsoft.UI.Xaml.DataTemplate
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 52:   //  Microsoft.UI.Xaml.Style
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 53:   //  Microsoft.UI.Xaml.Controls.StyleSelector
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 54:   //  Microsoft.UI.Xaml.Media.Animation.TransitionCollection
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 55:   //  Microsoft.UI.Xaml.Controls.DataTemplateSelector
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 56:   //  System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_56_IList;
                userType.CollectionAdd = VectorAdd_56_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 57:   //  Microsoft.UI.Xaml.Controls.TreeViewNode
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.Activator = Activate_57_TreeViewNode;
                userType.StaticInitializer = StaticInitializer_57_TreeViewNode;
                userType.AddMemberName("Children");
                userType.AddMemberName("Content");
                userType.AddMemberName("Depth");
                userType.AddMemberName("HasChildren");
                userType.AddMemberName("HasUnrealizedChildren");
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("Parent");
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 58:   //  System.Collections.Generic.IList`1<Object>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_58_IList;
                userType.CollectionAdd = VectorAdd_58_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 59:   //  Microsoft.UI.Xaml.Controls.TreeViewItem
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ListViewItem"));
                userType.Activator = Activate_59_TreeViewItem;
                userType.StaticInitializer = StaticInitializer_59_TreeViewItem;
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("CollapsedGlyph");
                userType.AddMemberName("ExpandedGlyph");
                userType.AddMemberName("GlyphBrush");
                userType.AddMemberName("GlyphOpacity");
                userType.AddMemberName("GlyphSize");
                userType.AddMemberName("HasUnrealizedChildren");
                userType.AddMemberName("ItemsSource");
                userType.AddMemberName("TreeViewItemTemplateSettings");
                xamlType = userType;
                break;

            case 60:   //  Microsoft.UI.Xaml.Controls.ListViewItem
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 61:   //  Microsoft.UI.Xaml.Controls.ContentControl
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 62:   //  Microsoft.UI.Xaml.Media.Brush
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 63:   //  Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_63_TreeViewItemTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 64:   //  HddtodoUI.Controls.TaskCategoriesPanel
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_64_TaskCategoriesPanel;
                userType.StaticInitializer = StaticInitializer_64_TaskCategoriesPanel;
                userType.AddMemberName("IsAddListNameValid");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 65:   //  HddtodoUI.Converters.TreeNodeGlyphConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_65_TreeNodeGlyphConverter;
                userType.StaticInitializer = StaticInitializer_65_TreeNodeGlyphConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 66:   //  HddtodoUI.Controls.TaskCategoriesTreeViewPanel
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_66_TaskCategoriesTreeViewPanel;
                userType.StaticInitializer = StaticInitializer_66_TaskCategoriesTreeViewPanel;
                userType.AddMemberName("IsAddListNameValid");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 67:   //  HddtodoUI.Converters.StringToVisibilityConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_67_StringToVisibilityConverter;
                userType.StaticInitializer = StaticInitializer_67_StringToVisibilityConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 68:   //  HddtodoUI.Converters.DateTimeToBrushConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_68_DateTimeToBrushConverter;
                userType.StaticInitializer = StaticInitializer_68_DateTimeToBrushConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 69:   //  HddtodoUI.Converters.IntToVisibilityConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_69_IntToVisibilityConverter;
                userType.StaticInitializer = StaticInitializer_69_IntToVisibilityConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 70:   //  CommunityToolkit.WinUI.Controls.GridSplitter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase"));
                userType.Activator = Activate_70_GridSplitter;
                userType.StaticInitializer = StaticInitializer_70_GridSplitter;
                userType.AddMemberName("ResizeBehavior");
                userType.AddMemberName("ResizeDirection");
                userType.AddMemberName("ParentLevel");
                xamlType = userType;
                break;

            case 71:   //  CommunityToolkit.WinUI.Controls.SizerBase
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Control"));
                userType.StaticInitializer = StaticInitializer_71_SizerBase;
                userType.AddMemberName("Cursor");
                userType.AddMemberName("DragIncrement");
                userType.AddMemberName("KeyboardIncrement");
                userType.AddMemberName("Orientation");
                userType.AddMemberName("IsThumbVisible");
                xamlType = userType;
                break;

            case 72:   //  CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_72_GridResizeBehavior;
                userType.AddEnumValue("BasedOnAlignment", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior.BasedOnAlignment);
                userType.AddEnumValue("CurrentAndNext", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior.CurrentAndNext);
                userType.AddEnumValue("PreviousAndCurrent", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior.PreviousAndCurrent);
                userType.AddEnumValue("PreviousAndNext", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior.PreviousAndNext);
                xamlType = userType;
                break;

            case 73:   //  CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_73_GridResizeDirection;
                userType.AddEnumValue("Auto", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection.Auto);
                userType.AddEnumValue("Columns", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection.Columns);
                userType.AddEnumValue("Rows", global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection.Rows);
                xamlType = userType;
                break;

            case 74:   //  Microsoft.UI.Input.InputSystemCursorShape
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_74_InputSystemCursorShape;
                userType.AddEnumValue("Arrow", global::Microsoft.UI.Input.InputSystemCursorShape.Arrow);
                userType.AddEnumValue("Cross", global::Microsoft.UI.Input.InputSystemCursorShape.Cross);
                userType.AddEnumValue("Hand", global::Microsoft.UI.Input.InputSystemCursorShape.Hand);
                userType.AddEnumValue("Help", global::Microsoft.UI.Input.InputSystemCursorShape.Help);
                userType.AddEnumValue("IBeam", global::Microsoft.UI.Input.InputSystemCursorShape.IBeam);
                userType.AddEnumValue("SizeAll", global::Microsoft.UI.Input.InputSystemCursorShape.SizeAll);
                userType.AddEnumValue("SizeNortheastSouthwest", global::Microsoft.UI.Input.InputSystemCursorShape.SizeNortheastSouthwest);
                userType.AddEnumValue("SizeNorthSouth", global::Microsoft.UI.Input.InputSystemCursorShape.SizeNorthSouth);
                userType.AddEnumValue("SizeNorthwestSoutheast", global::Microsoft.UI.Input.InputSystemCursorShape.SizeNorthwestSoutheast);
                userType.AddEnumValue("SizeWestEast", global::Microsoft.UI.Input.InputSystemCursorShape.SizeWestEast);
                userType.AddEnumValue("UniversalNo", global::Microsoft.UI.Input.InputSystemCursorShape.UniversalNo);
                userType.AddEnumValue("UpArrow", global::Microsoft.UI.Input.InputSystemCursorShape.UpArrow);
                userType.AddEnumValue("Wait", global::Microsoft.UI.Input.InputSystemCursorShape.Wait);
                userType.AddEnumValue("Pin", global::Microsoft.UI.Input.InputSystemCursorShape.Pin);
                userType.AddEnumValue("Person", global::Microsoft.UI.Input.InputSystemCursorShape.Person);
                userType.AddEnumValue("AppStarting", global::Microsoft.UI.Input.InputSystemCursorShape.AppStarting);
                xamlType = userType;
                break;

            case 75:   //  Microsoft.UI.Xaml.Controls.Orientation
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 76:   //  HddtodoUI.Controls.TaskStepsControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_76_TaskStepsControl;
                userType.StaticInitializer = StaticInitializer_76_TaskStepsControl;
                userType.AddMemberName("TaskSteps");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 77:   //  System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.TaskStep>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.TaskStep>"));
                userType.StaticInitializer = StaticInitializer_77_ObservableCollection;
                userType.CollectionAdd = VectorAdd_77_ObservableCollection;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 78:   //  System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.TaskStep>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_78_Collection;
                userType.StaticInitializer = StaticInitializer_78_Collection;
                userType.CollectionAdd = VectorAdd_78_Collection;
                xamlType = userType;
                break;

            case 79:   //  HddtodoUI.BackendModels.TaskStep
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_79_TaskStep;
                userType.AddMemberName("StepId");
                userType.AddMemberName("TaskId");
                userType.AddMemberName("UserId");
                userType.AddMemberName("StepOrder");
                userType.AddMemberName("Title");
                userType.AddMemberName("Color");
                userType.AddMemberName("StepCompleteTime");
                userType.AddMemberName("DeletedStatus");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 80:   //  Microsoft.UI.Xaml.Controls.BreadcrumbBar
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Control"));
                userType.Activator = Activate_80_BreadcrumbBar;
                userType.StaticInitializer = StaticInitializer_80_BreadcrumbBar;
                userType.AddMemberName("ItemTemplate");
                userType.AddMemberName("ItemsSource");
                xamlType = userType;
                break;

            case 81:   //  Microsoft.UI.Xaml.Controls.BreadcrumbBarItem
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ContentControl"));
                userType.Activator = Activate_81_BreadcrumbBarItem;
                userType.StaticInitializer = StaticInitializer_81_BreadcrumbBarItem;
                xamlType = userType;
                break;

            case 82:   //  HddtodoUI.Controls.TaskDetailsControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_82_TaskDetailsControl;
                userType.StaticInitializer = StaticInitializer_82_TaskDetailsControl;
                userType.AddMemberName("CurrentDateTime");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 83:   //  Microsoft.UI.Xaml.Controls.NumberBox
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Control"));
                userType.Activator = Activate_83_NumberBox;
                userType.StaticInitializer = StaticInitializer_83_NumberBox;
                userType.AddMemberName("Minimum");
                userType.AddMemberName("Value");
                userType.AddMemberName("SpinButtonPlacementMode");
                userType.AddMemberName("AcceptsExpression");
                userType.AddMemberName("Description");
                userType.AddMemberName("Header");
                userType.AddMemberName("HeaderTemplate");
                userType.AddMemberName("IsWrapEnabled");
                userType.AddMemberName("LargeChange");
                userType.AddMemberName("Maximum");
                userType.AddMemberName("NumberFormatter");
                userType.AddMemberName("PlaceholderText");
                userType.AddMemberName("PreventKeyboardDisplayOnProgrammaticFocus");
                userType.AddMemberName("SelectionFlyout");
                userType.AddMemberName("SelectionHighlightColor");
                userType.AddMemberName("SmallChange");
                userType.AddMemberName("Text");
                userType.AddMemberName("TextReadingOrder");
                userType.AddMemberName("ValidationMode");
                xamlType = userType;
                break;

            case 84:   //  Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_84_NumberBoxSpinButtonPlacementMode;
                userType.AddEnumValue("Hidden", global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Hidden);
                userType.AddEnumValue("Compact", global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Compact);
                userType.AddEnumValue("Inline", global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Inline);
                xamlType = userType;
                break;

            case 85:   //  Windows.Globalization.NumberFormatting.INumberFormatter2
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_85_INumberFormatter2;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 86:   //  Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 87:   //  Microsoft.UI.Xaml.Media.SolidColorBrush
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 88:   //  Microsoft.UI.Xaml.TextReadingOrder
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 89:   //  Microsoft.UI.Xaml.Controls.NumberBoxValidationMode
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_89_NumberBoxValidationMode;
                userType.AddEnumValue("InvalidInputOverwritten", global::Microsoft.UI.Xaml.Controls.NumberBoxValidationMode.InvalidInputOverwritten);
                userType.AddEnumValue("Disabled", global::Microsoft.UI.Xaml.Controls.NumberBoxValidationMode.Disabled);
                xamlType = userType;
                break;

            case 90:   //  HddtodoUI.Controls.TaskReminderDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ContentDialog"));
                userType.Activator = Activate_90_TaskReminderDialog;
                userType.StaticInitializer = StaticInitializer_90_TaskReminderDialog;
                userType.AddMemberName("Reminder");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 91:   //  Microsoft.UI.Xaml.Controls.ContentDialog
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 92:   //  HddtodoUI.BackendModels.TaskReminder
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_92_TaskReminder;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 93:   //  HddtodoUI.Controls.TaskRestartDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ContentDialog"));
                userType.Activator = Activate_93_TaskRestartDialog;
                userType.StaticInitializer = StaticInitializer_93_TaskRestartDialog;
                userType.AddMemberName("Restart");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 94:   //  HddtodoUI.BackendModels.TaskRestart
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_94_TaskRestart;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 95:   //  HddtodoUI.Controls.TaskSearchDialog
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_95_TaskSearchDialog;
                userType.StaticInitializer = StaticInitializer_95_TaskSearchDialog;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 96:   //  HddtodoUI.Converters.CategoryConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_96_CategoryConverter;
                userType.StaticInitializer = StaticInitializer_96_CategoryConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 97:   //  HddtodoUI.Converters.EmptyCollectionToVisibilityConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_97_EmptyCollectionToVisibilityConverter;
                userType.StaticInitializer = StaticInitializer_97_EmptyCollectionToVisibilityConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 98:   //  HddtodoUI.Selectors.TaskListItemTemplateSelector
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.DataTemplateSelector"));
                userType.Activator = Activate_98_TaskListItemTemplateSelector;
                userType.StaticInitializer = StaticInitializer_98_TaskListItemTemplateSelector;
                userType.AddMemberName("TaskTemplate");
                userType.AddMemberName("SubCategoryTemplate");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 99:   //  Microsoft.UI.Xaml.Controls.Expander
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ContentControl"));
                userType.Activator = Activate_99_Expander;
                userType.StaticInitializer = StaticInitializer_99_Expander;
                userType.AddMemberName("HeaderTemplate");
                userType.AddMemberName("ExpandDirection");
                userType.AddMemberName("Header");
                userType.AddMemberName("HeaderTemplateSelector");
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("TemplateSettings");
                xamlType = userType;
                break;

            case 100:   //  Microsoft.UI.Xaml.Controls.ExpandDirection
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_100_ExpandDirection;
                userType.AddEnumValue("Down", global::Microsoft.UI.Xaml.Controls.ExpandDirection.Down);
                userType.AddEnumValue("Up", global::Microsoft.UI.Xaml.Controls.ExpandDirection.Up);
                xamlType = userType;
                break;

            case 101:   //  Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_101_ExpanderTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 102:   //  HddtodoUI.Controls.TasksPanel
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_102_TasksPanel;
                userType.StaticInitializer = StaticInitializer_102_TasksPanel;
                userType.AddMemberName("CurrentCategory");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 103:   //  HddtodoUI.Models.TaskCategoryViewObject
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_103_TaskCategoryViewObject;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 104:   //  HddtodoUI.Converters.StringToBrushConverter
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.Activator = Activate_104_StringToBrushConverter;
                userType.StaticInitializer = StaticInitializer_104_StringToBrushConverter;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 105:   //  Microsoft.UI.Xaml.Controls.ProgressBar
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Primitives.RangeBase"));
                userType.Activator = Activate_105_ProgressBar;
                userType.StaticInitializer = StaticInitializer_105_ProgressBar;
                userType.AddMemberName("IsIndeterminate");
                userType.AddMemberName("ShowError");
                userType.AddMemberName("ShowPaused");
                userType.AddMemberName("TemplateSettings");
                xamlType = userType;
                break;

            case 106:   //  Microsoft.UI.Xaml.Controls.Primitives.RangeBase
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 107:   //  Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_107_ProgressBarTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 108:   //  HddtodoUI.Controls.TaskTimeLogStatisticsControl
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_108_TaskTimeLogStatisticsControl;
                userType.StaticInitializer = StaticInitializer_108_TaskTimeLogStatisticsControl;
                userType.AddMemberName("TotalWorkTime");
                userType.AddMemberName("WorkTimePercentage");
                userType.AddMemberName("TotalWorkTimeText");
                userType.AddMemberName("IsNextDayButtonEnable");
                userType.AddMemberName("CompletedTasksCount");
                userType.AddMemberName("UncompletedTasksCount");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 109:   //  HddtodoUI.Views.MainView
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.UserControl"));
                userType.Activator = Activate_109_MainView;
                userType.StaticInitializer = StaticInitializer_109_MainView;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 110:   //  HddtodoUI.MainWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_110_MainWindow;
                userType.StaticInitializer = StaticInitializer_110_MainWindow;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 111:   //  Microsoft.UI.Xaml.Window
                xamlType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 112:   //  HddtodoUI.Windows.IndicatorWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_112_IndicatorWindow;
                userType.StaticInitializer = StaticInitializer_112_IndicatorWindow;
                userType.AddMemberName("Status");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 113:   //  HddtodoUI.Windows.LoginWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_113_LoginWindow;
                userType.StaticInitializer = StaticInitializer_113_LoginWindow;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 114:   //  HddtodoUI.Windows.NotificationWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.StaticInitializer = StaticInitializer_114_NotificationWindow;
                userType.AddMemberName("Level");
                userType.AddMemberName("Height");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 115:   //  HddtodoUI.Windows.NotificationLevel
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_115_NotificationLevel;
                userType.AddEnumValue("Info", global::HddtodoUI.Windows.NotificationLevel.Info);
                userType.AddEnumValue("Warning", global::HddtodoUI.Windows.NotificationLevel.Warning);
                userType.AddEnumValue("Danger", global::HddtodoUI.Windows.NotificationLevel.Danger);
                userType.AddEnumValue("Success", global::HddtodoUI.Windows.NotificationLevel.Success);
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 116:   //  HddtodoUI.Windows.QuickAddWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_116_QuickAddWindow;
                userType.StaticInitializer = StaticInitializer_116_QuickAddWindow;
                userType.AddMemberName("IsStartTask");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 117:   //  HddtodoUI.Windows.TaskDetailsWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_117_TaskDetailsWindow;
                userType.StaticInitializer = StaticInitializer_117_TaskDetailsWindow;
                userType.AddMemberName("FromParent");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 118:   //  Microsoft.UI.Windowing.AppWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_118_AppWindow;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 119:   //  HddtodoUI.Windows.TaskSearchWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_119_TaskSearchWindow;
                userType.StaticInitializer = StaticInitializer_119_TaskSearchWindow;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 120:   //  HddtodoUI.Windows.TaskStepWindow
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_120_TaskStepWindow;
                userType.StaticInitializer = StaticInitializer_120_TaskStepWindow;
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 121:   //  Windows.UI.Xaml.Controls.TreeViewNode
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Windows.UI.Xaml.DependencyObject"));
                userType.Activator = Activate_121_TreeViewNode;
                userType.StaticInitializer = StaticInitializer_121_TreeViewNode;
                userType.AddMemberName("Children");
                userType.AddMemberName("Content");
                userType.AddMemberName("Depth");
                userType.AddMemberName("HasChildren");
                userType.AddMemberName("HasUnrealizedChildren");
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("Parent");
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 122:   //  Windows.UI.Xaml.DependencyObject
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_122_DependencyObject;
                userType.AddMemberName("Dispatcher");
                xamlType = userType;
                break;

            case 123:   //  System.Collections.Generic.IList`1<Windows.UI.Xaml.Controls.TreeViewNode>
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_123_IList;
                userType.CollectionAdd = VectorAdd_123_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 124:   //  Windows.UI.Core.CoreDispatcher
                userType = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_124_CoreDispatcher;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;
            }
            return xamlType;
        }

        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> _otherProviders;
        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> OtherProviders
        {
            get
            {
                if(_otherProviders == null)
                {
                    var otherProviders = new global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider>();
                    global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider provider;
                    provider = new global::Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    provider = new global::CommunityToolkit.WinUI.Controls.SizersRns.CommunityToolkit_WinUI_Controls_Sizers_XamlTypeInfo.XamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    _otherProviders = otherProviders;
                }
                return _otherProviders;
            }
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForName(string typeName)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(typeName);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(type);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private object get_0_XamlControlsResources_UseCompactResources(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.XamlControlsResources)instance;
            return that.UseCompactResources;
        }
        private void set_0_XamlControlsResources_UseCompactResources(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.XamlControlsResources)instance;
            that.UseCompactResources = (global::System.Boolean)Value;
        }
        private object get_1_ProgressRing_IsActive(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.IsActive;
        }
        private void set_1_ProgressRing_IsActive(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            that.IsActive = (global::System.Boolean)Value;
        }
        private object get_2_ProgressRing_IsIndeterminate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.IsIndeterminate;
        }
        private void set_2_ProgressRing_IsIndeterminate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            that.IsIndeterminate = (global::System.Boolean)Value;
        }
        private object get_3_ProgressRing_Maximum(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.Maximum;
        }
        private void set_3_ProgressRing_Maximum(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            that.Maximum = (global::System.Double)Value;
        }
        private object get_4_ProgressRing_Minimum(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.Minimum;
        }
        private void set_4_ProgressRing_Minimum(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            that.Minimum = (global::System.Double)Value;
        }
        private object get_5_ProgressRing_TemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.TemplateSettings;
        }
        private object get_6_ProgressRing_Value(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            return that.Value;
        }
        private void set_6_ProgressRing_Value(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressRing)instance;
            that.Value = (global::System.Double)Value;
        }
        private object get_7_TaskCategoryItemForPlanControl_Category(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            return that.Category;
        }
        private void set_7_TaskCategoryItemForPlanControl_Category(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            that.Category = (global::HddtodoUI.BackendModels.TaskCategory)Value;
        }
        private object get_8_TaskCategoryItemForPlanControl_CategoryCount(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            return that.CategoryCount;
        }
        private void set_8_TaskCategoryItemForPlanControl_CategoryCount(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            that.CategoryCount = (global::System.Int32)Value;
        }
        private object get_9_TaskCategoryItemForPlanControl_TaskCount(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            return that.TaskCount;
        }
        private void set_9_TaskCategoryItemForPlanControl_TaskCount(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            that.TaskCount = (global::System.Int32)Value;
        }
        private object get_10_TaskCategoryItemForPlanControl_ParentPath(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)instance;
            return that.ParentPath;
        }
        private object get_11_PlannedProjectsPanel_PlannedCategories(object instance)
        {
            var that = (global::HddtodoUI.Controls.PlannedProjectsPanel)instance;
            return that.PlannedCategories;
        }
        private object get_12_TaskCategoryWithCount_Category(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            return that.Category;
        }
        private void set_12_TaskCategoryWithCount_Category(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            that.Category = (global::HddtodoUI.BackendModels.TaskCategory)Value;
        }
        private object get_13_TaskCategoryWithCount_SubcategoryCount(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            return that.SubcategoryCount;
        }
        private void set_13_TaskCategoryWithCount_SubcategoryCount(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            that.SubcategoryCount = (global::System.Int32)Value;
        }
        private object get_14_TaskCategoryWithCount_TaskCount(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            return that.TaskCount;
        }
        private void set_14_TaskCategoryWithCount_TaskCount(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount)instance;
            that.TaskCount = (global::System.Int32)Value;
        }
        private object get_15_PlannedProjectsPanel_GroupedPlannedCategories(object instance)
        {
            var that = (global::HddtodoUI.Controls.PlannedProjectsPanel)instance;
            return that.GroupedPlannedCategories;
        }
        private object get_16_Grouping_Key(object instance)
        {
            var that = (global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)instance;
            return that.Key;
        }
        private object get_17_Collection_Count(object instance)
        {
            var that = (global::System.Collections.ObjectModel.Collection<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>)instance;
            return that.Count;
        }
        private object get_18_QuickAddTaskDialog_IsStartTask(object instance)
        {
            var that = (global::HddtodoUI.Controls.QuickAddTaskDialog)instance;
            return that.IsStartTask;
        }
        private void set_18_QuickAddTaskDialog_IsStartTask(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.QuickAddTaskDialog)instance;
            that.IsStartTask = (global::System.Boolean)Value;
        }
        private object get_19_TaskItemControl_TaskVO(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskItemControl)instance;
            return that.TaskVO;
        }
        private void set_19_TaskItemControl_TaskVO(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskItemControl)instance;
            that.TaskVO = (global::HddtodoUI.Models.TodoTaskViewObject)Value;
        }
        private object get_20_SubtasksControl_Subtasks(object instance)
        {
            var that = (global::HddtodoUI.Controls.SubtasksControl)instance;
            return that.Subtasks;
        }
        private object get_21_TodoTaskViewObject_TaskID(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.TaskID;
        }
        private void set_21_TodoTaskViewObject_TaskID(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.TaskID = (global::System.Int64)Value;
        }
        private object get_22_TodoTaskViewObject_Title(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Title;
        }
        private void set_22_TodoTaskViewObject_Title(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Title = (global::System.String)Value;
        }
        private object get_23_TodoTaskViewObject_DueDate(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.DueDate;
        }
        private void set_23_TodoTaskViewObject_DueDate(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.DueDate = (global::System.Nullable<global::System.DateTime>)Value;
        }
        private object get_24_TodoTaskViewObject_Category(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Category;
        }
        private void set_24_TodoTaskViewObject_Category(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Category = (global::System.String)Value;
        }
        private object get_25_TodoTaskViewObject_CategoryName(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.CategoryName;
        }
        private void set_25_TodoTaskViewObject_CategoryName(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.CategoryName = (global::System.String)Value;
        }
        private object get_26_TodoTaskViewObject_Priority(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Priority;
        }
        private void set_26_TodoTaskViewObject_Priority(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Priority = (global::HddtodoUI.BackendModels.TaskPriority)Value;
        }
        private object get_27_TodoTaskViewObject_Notes(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Notes;
        }
        private void set_27_TodoTaskViewObject_Notes(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Notes = (global::System.String)Value;
        }
        private object get_28_TodoTaskViewObject_StartTime(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.StartTime;
        }
        private void set_28_TodoTaskViewObject_StartTime(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.StartTime = (global::System.Nullable<global::System.DateTime>)Value;
        }
        private object get_29_TodoTaskViewObject_Status(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Status;
        }
        private void set_29_TodoTaskViewObject_Status(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Status = (global::HddtodoUI.Models.TaskStatus)Value;
        }
        private object get_30_TodoTaskViewObject_TaskOrder(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.TaskOrder;
        }
        private void set_30_TodoTaskViewObject_TaskOrder(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.TaskOrder = (global::System.Int64)Value;
        }
        private object get_31_TodoTaskViewObject_GroupOrder(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.GroupOrder;
        }
        private void set_31_TodoTaskViewObject_GroupOrder(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.GroupOrder = (global::System.Int64)Value;
        }
        private object get_32_TodoTaskViewObject_Color(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Color;
        }
        private void set_32_TodoTaskViewObject_Color(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Color = (global::System.String)Value;
        }
        private object get_33_TodoTaskViewObject_Task(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.Task;
        }
        private void set_33_TodoTaskViewObject_Task(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.Task = (global::HddtodoUI.BackendModels.TTask)Value;
        }
        private object get_34_TodoTaskViewObject_DeletedStatus(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.DeletedStatus;
        }
        private void set_34_TodoTaskViewObject_DeletedStatus(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.DeletedStatus = (global::System.Boolean)Value;
        }
        private object get_35_TodoTaskViewObject_ParentTaskIds(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.ParentTaskIds;
        }
        private void set_35_TodoTaskViewObject_ParentTaskIds(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.ParentTaskIds = (global::System.String)Value;
        }
        private object get_36_TodoTaskViewObject_DirectSubTaskCount(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.DirectSubTaskCount;
        }
        private void set_36_TodoTaskViewObject_DirectSubTaskCount(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.DirectSubTaskCount = (global::System.Int32)Value;
        }
        private object get_37_TodoTaskViewObject_IsCompleted(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.IsCompleted;
        }
        private object get_38_TodoTaskViewObject_CategoryOrderPath(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.CategoryOrderPath;
        }
        private void set_38_TodoTaskViewObject_CategoryOrderPath(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.CategoryOrderPath = (global::System.String)Value;
        }
        private object get_39_TodoTaskViewObject_CompletedDate(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.CompletedDate;
        }
        private void set_39_TodoTaskViewObject_CompletedDate(object instance, object Value)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            that.CompletedDate = (global::System.Nullable<global::System.DateTime>)Value;
        }
        private object get_40_TodoTaskViewObject_DueDateVisibility(object instance)
        {
            var that = (global::HddtodoUI.Models.TodoTaskViewObject)instance;
            return that.DueDateVisibility;
        }
        private object get_41_TreeView_SelectionMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.SelectionMode;
        }
        private void set_41_TreeView_SelectionMode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.SelectionMode = (global::Microsoft.UI.Xaml.Controls.TreeViewSelectionMode)Value;
        }
        private object get_42_TreeView_ItemsSource(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemsSource;
        }
        private void set_42_TreeView_ItemsSource(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemsSource = (global::System.Object)Value;
        }
        private object get_43_TreeView_ItemTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemTemplate;
        }
        private void set_43_TreeView_ItemTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_44_TreeView_CanDragItems(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.CanDragItems;
        }
        private void set_44_TreeView_CanDragItems(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.CanDragItems = (global::System.Boolean)Value;
        }
        private object get_45_TreeView_CanReorderItems(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.CanReorderItems;
        }
        private void set_45_TreeView_CanReorderItems(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.CanReorderItems = (global::System.Boolean)Value;
        }
        private object get_46_TreeView_ItemContainerStyle(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemContainerStyle;
        }
        private void set_46_TreeView_ItemContainerStyle(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemContainerStyle = (global::Microsoft.UI.Xaml.Style)Value;
        }
        private object get_47_TreeView_ItemContainerStyleSelector(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemContainerStyleSelector;
        }
        private void set_47_TreeView_ItemContainerStyleSelector(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemContainerStyleSelector = (global::Microsoft.UI.Xaml.Controls.StyleSelector)Value;
        }
        private object get_48_TreeView_ItemContainerTransitions(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemContainerTransitions;
        }
        private void set_48_TreeView_ItemContainerTransitions(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemContainerTransitions = (global::Microsoft.UI.Xaml.Media.Animation.TransitionCollection)Value;
        }
        private object get_49_TreeView_ItemTemplateSelector(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.ItemTemplateSelector;
        }
        private void set_49_TreeView_ItemTemplateSelector(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.ItemTemplateSelector = (global::Microsoft.UI.Xaml.Controls.DataTemplateSelector)Value;
        }
        private object get_50_TreeView_RootNodes(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.RootNodes;
        }
        private object get_51_TreeViewNode_Children(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Children;
        }
        private object get_52_TreeViewNode_Content(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Content;
        }
        private void set_52_TreeViewNode_Content(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.Content = (global::System.Object)Value;
        }
        private object get_53_TreeViewNode_Depth(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Depth;
        }
        private object get_54_TreeViewNode_HasChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasChildren;
        }
        private object get_55_TreeViewNode_HasUnrealizedChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasUnrealizedChildren;
        }
        private void set_55_TreeViewNode_HasUnrealizedChildren(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.HasUnrealizedChildren = (global::System.Boolean)Value;
        }
        private object get_56_TreeViewNode_IsExpanded(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.IsExpanded;
        }
        private void set_56_TreeViewNode_IsExpanded(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_57_TreeViewNode_Parent(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Parent;
        }
        private object get_58_TreeView_SelectedItem(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.SelectedItem;
        }
        private void set_58_TreeView_SelectedItem(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.SelectedItem = (global::System.Object)Value;
        }
        private object get_59_TreeView_SelectedItems(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.SelectedItems;
        }
        private object get_60_TreeView_SelectedNode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.SelectedNode;
        }
        private void set_60_TreeView_SelectedNode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            that.SelectedNode = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)Value;
        }
        private object get_61_TreeView_SelectedNodes(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeView)instance;
            return that.SelectedNodes;
        }
        private object get_62_TreeViewItem_IsExpanded(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.IsExpanded;
        }
        private void set_62_TreeViewItem_IsExpanded(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_63_TreeViewItem_CollapsedGlyph(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.CollapsedGlyph;
        }
        private void set_63_TreeViewItem_CollapsedGlyph(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.CollapsedGlyph = (global::System.String)Value;
        }
        private object get_64_TreeViewItem_ExpandedGlyph(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.ExpandedGlyph;
        }
        private void set_64_TreeViewItem_ExpandedGlyph(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.ExpandedGlyph = (global::System.String)Value;
        }
        private object get_65_TreeViewItem_GlyphBrush(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.GlyphBrush;
        }
        private void set_65_TreeViewItem_GlyphBrush(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.GlyphBrush = (global::Microsoft.UI.Xaml.Media.Brush)Value;
        }
        private object get_66_TreeViewItem_GlyphOpacity(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.GlyphOpacity;
        }
        private void set_66_TreeViewItem_GlyphOpacity(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.GlyphOpacity = (global::System.Double)Value;
        }
        private object get_67_TreeViewItem_GlyphSize(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.GlyphSize;
        }
        private void set_67_TreeViewItem_GlyphSize(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.GlyphSize = (global::System.Double)Value;
        }
        private object get_68_TreeViewItem_HasUnrealizedChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.HasUnrealizedChildren;
        }
        private void set_68_TreeViewItem_HasUnrealizedChildren(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.HasUnrealizedChildren = (global::System.Boolean)Value;
        }
        private object get_69_TreeViewItem_ItemsSource(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.ItemsSource;
        }
        private void set_69_TreeViewItem_ItemsSource(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            that.ItemsSource = (global::System.Object)Value;
        }
        private object get_70_TreeViewItem_TreeViewItemTemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)instance;
            return that.TreeViewItemTemplateSettings;
        }
        private object get_71_TaskCategoriesPanel_IsAddListNameValid(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoriesPanel)instance;
            return that.IsAddListNameValid;
        }
        private void set_71_TaskCategoriesPanel_IsAddListNameValid(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoriesPanel)instance;
            that.IsAddListNameValid = (global::System.Boolean)Value;
        }
        private object get_72_TaskCategoriesTreeViewPanel_IsAddListNameValid(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel)instance;
            return that.IsAddListNameValid;
        }
        private void set_72_TaskCategoriesTreeViewPanel_IsAddListNameValid(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TaskCategoriesTreeViewPanel)instance;
            that.IsAddListNameValid = (global::System.Boolean)Value;
        }
        private object get_73_GridSplitter_ResizeBehavior(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            return that.ResizeBehavior;
        }
        private void set_73_GridSplitter_ResizeBehavior(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            that.ResizeBehavior = (global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior)Value;
        }
        private object get_74_GridSplitter_ResizeDirection(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            return that.ResizeDirection;
        }
        private void set_74_GridSplitter_ResizeDirection(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            that.ResizeDirection = (global::CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection)Value;
        }
        private object get_75_GridSplitter_ParentLevel(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            return that.ParentLevel;
        }
        private void set_75_GridSplitter_ParentLevel(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.GridSplitter)instance;
            that.ParentLevel = (global::System.Int32)Value;
        }
        private object get_76_SizerBase_Cursor(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            return that.Cursor;
        }
        private void set_76_SizerBase_Cursor(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            that.Cursor = (global::Microsoft.UI.Input.InputSystemCursorShape)Value;
        }
        private object get_77_SizerBase_DragIncrement(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            return that.DragIncrement;
        }
        private void set_77_SizerBase_DragIncrement(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            that.DragIncrement = (global::System.Double)Value;
        }
        private object get_78_SizerBase_KeyboardIncrement(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            return that.KeyboardIncrement;
        }
        private void set_78_SizerBase_KeyboardIncrement(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            that.KeyboardIncrement = (global::System.Double)Value;
        }
        private object get_79_SizerBase_Orientation(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            return that.Orientation;
        }
        private void set_79_SizerBase_Orientation(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            that.Orientation = (global::Microsoft.UI.Xaml.Controls.Orientation)Value;
        }
        private object get_80_SizerBase_IsThumbVisible(object instance)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            return that.IsThumbVisible;
        }
        private void set_80_SizerBase_IsThumbVisible(object instance, object Value)
        {
            var that = (global::CommunityToolkit.WinUI.Controls.SizerBase)instance;
            that.IsThumbVisible = (global::System.Boolean)Value;
        }
        private object get_81_TaskStepsControl_TaskSteps(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskStepsControl)instance;
            return that.TaskSteps;
        }
        private object get_82_TaskStep_StepId(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.StepId;
        }
        private void set_82_TaskStep_StepId(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.StepId = (global::System.Int64)Value;
        }
        private object get_83_TaskStep_TaskId(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.TaskId;
        }
        private void set_83_TaskStep_TaskId(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.TaskId = (global::System.Int64)Value;
        }
        private object get_84_TaskStep_UserId(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.UserId;
        }
        private void set_84_TaskStep_UserId(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.UserId = (global::System.Int64)Value;
        }
        private object get_85_TaskStep_StepOrder(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.StepOrder;
        }
        private void set_85_TaskStep_StepOrder(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.StepOrder = (global::System.Int32)Value;
        }
        private object get_86_TaskStep_Title(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.Title;
        }
        private void set_86_TaskStep_Title(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.Title = (global::System.String)Value;
        }
        private object get_87_TaskStep_Color(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.Color;
        }
        private void set_87_TaskStep_Color(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.Color = (global::System.String)Value;
        }
        private object get_88_TaskStep_StepCompleteTime(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.StepCompleteTime;
        }
        private void set_88_TaskStep_StepCompleteTime(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.StepCompleteTime = (global::System.Nullable<global::System.DateTime>)Value;
        }
        private object get_89_TaskStep_DeletedStatus(object instance)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            return that.DeletedStatus;
        }
        private void set_89_TaskStep_DeletedStatus(object instance, object Value)
        {
            var that = (global::HddtodoUI.BackendModels.TaskStep)instance;
            that.DeletedStatus = (global::System.Boolean)Value;
        }
        private object get_90_BreadcrumbBar_ItemTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.BreadcrumbBar)instance;
            return that.ItemTemplate;
        }
        private void set_90_BreadcrumbBar_ItemTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.BreadcrumbBar)instance;
            that.ItemTemplate = (global::System.Object)Value;
        }
        private object get_91_BreadcrumbBar_ItemsSource(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.BreadcrumbBar)instance;
            return that.ItemsSource;
        }
        private void set_91_BreadcrumbBar_ItemsSource(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.BreadcrumbBar)instance;
            that.ItemsSource = (global::System.Object)Value;
        }
        private object get_92_TaskDetailsControl_CurrentDateTime(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskDetailsControl)instance;
            return that.CurrentDateTime;
        }
        private object get_93_NumberBox_Minimum(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Minimum;
        }
        private void set_93_NumberBox_Minimum(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Minimum = (global::System.Double)Value;
        }
        private object get_94_NumberBox_Value(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Value;
        }
        private void set_94_NumberBox_Value(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Value = (global::System.Double)Value;
        }
        private object get_95_NumberBox_SpinButtonPlacementMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.SpinButtonPlacementMode;
        }
        private void set_95_NumberBox_SpinButtonPlacementMode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.SpinButtonPlacementMode = (global::Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode)Value;
        }
        private object get_96_NumberBox_AcceptsExpression(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.AcceptsExpression;
        }
        private void set_96_NumberBox_AcceptsExpression(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.AcceptsExpression = (global::System.Boolean)Value;
        }
        private object get_97_NumberBox_Description(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Description;
        }
        private void set_97_NumberBox_Description(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Description = (global::System.Object)Value;
        }
        private object get_98_NumberBox_Header(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Header;
        }
        private void set_98_NumberBox_Header(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Header = (global::System.Object)Value;
        }
        private object get_99_NumberBox_HeaderTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.HeaderTemplate;
        }
        private void set_99_NumberBox_HeaderTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.HeaderTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_100_NumberBox_IsWrapEnabled(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.IsWrapEnabled;
        }
        private void set_100_NumberBox_IsWrapEnabled(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.IsWrapEnabled = (global::System.Boolean)Value;
        }
        private object get_101_NumberBox_LargeChange(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.LargeChange;
        }
        private void set_101_NumberBox_LargeChange(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.LargeChange = (global::System.Double)Value;
        }
        private object get_102_NumberBox_Maximum(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Maximum;
        }
        private void set_102_NumberBox_Maximum(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Maximum = (global::System.Double)Value;
        }
        private object get_103_NumberBox_NumberFormatter(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.NumberFormatter;
        }
        private void set_103_NumberBox_NumberFormatter(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.NumberFormatter = (global::Windows.Globalization.NumberFormatting.INumberFormatter2)Value;
        }
        private object get_104_NumberBox_PlaceholderText(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.PlaceholderText;
        }
        private void set_104_NumberBox_PlaceholderText(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.PlaceholderText = (global::System.String)Value;
        }
        private object get_105_NumberBox_PreventKeyboardDisplayOnProgrammaticFocus(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.PreventKeyboardDisplayOnProgrammaticFocus;
        }
        private void set_105_NumberBox_PreventKeyboardDisplayOnProgrammaticFocus(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.PreventKeyboardDisplayOnProgrammaticFocus = (global::System.Boolean)Value;
        }
        private object get_106_NumberBox_SelectionFlyout(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.SelectionFlyout;
        }
        private void set_106_NumberBox_SelectionFlyout(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.SelectionFlyout = (global::Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase)Value;
        }
        private object get_107_NumberBox_SelectionHighlightColor(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.SelectionHighlightColor;
        }
        private void set_107_NumberBox_SelectionHighlightColor(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.SelectionHighlightColor = (global::Microsoft.UI.Xaml.Media.SolidColorBrush)Value;
        }
        private object get_108_NumberBox_SmallChange(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.SmallChange;
        }
        private void set_108_NumberBox_SmallChange(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.SmallChange = (global::System.Double)Value;
        }
        private object get_109_NumberBox_Text(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.Text;
        }
        private void set_109_NumberBox_Text(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.Text = (global::System.String)Value;
        }
        private object get_110_NumberBox_TextReadingOrder(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.TextReadingOrder;
        }
        private void set_110_NumberBox_TextReadingOrder(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.TextReadingOrder = (global::Microsoft.UI.Xaml.TextReadingOrder)Value;
        }
        private object get_111_NumberBox_ValidationMode(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            return that.ValidationMode;
        }
        private void set_111_NumberBox_ValidationMode(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.NumberBox)instance;
            that.ValidationMode = (global::Microsoft.UI.Xaml.Controls.NumberBoxValidationMode)Value;
        }
        private object get_112_TaskReminderDialog_Reminder(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskReminderDialog)instance;
            return that.Reminder;
        }
        private object get_113_TaskRestartDialog_Restart(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskRestartDialog)instance;
            return that.Restart;
        }
        private object get_114_TaskListItemTemplateSelector_TaskTemplate(object instance)
        {
            var that = (global::HddtodoUI.Selectors.TaskListItemTemplateSelector)instance;
            return that.TaskTemplate;
        }
        private void set_114_TaskListItemTemplateSelector_TaskTemplate(object instance, object Value)
        {
            var that = (global::HddtodoUI.Selectors.TaskListItemTemplateSelector)instance;
            that.TaskTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_115_TaskListItemTemplateSelector_SubCategoryTemplate(object instance)
        {
            var that = (global::HddtodoUI.Selectors.TaskListItemTemplateSelector)instance;
            return that.SubCategoryTemplate;
        }
        private void set_115_TaskListItemTemplateSelector_SubCategoryTemplate(object instance, object Value)
        {
            var that = (global::HddtodoUI.Selectors.TaskListItemTemplateSelector)instance;
            that.SubCategoryTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_116_Expander_HeaderTemplate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.HeaderTemplate;
        }
        private void set_116_Expander_HeaderTemplate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            that.HeaderTemplate = (global::Microsoft.UI.Xaml.DataTemplate)Value;
        }
        private object get_117_Expander_ExpandDirection(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.ExpandDirection;
        }
        private void set_117_Expander_ExpandDirection(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            that.ExpandDirection = (global::Microsoft.UI.Xaml.Controls.ExpandDirection)Value;
        }
        private object get_118_Expander_Header(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.Header;
        }
        private void set_118_Expander_Header(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            that.Header = (global::System.Object)Value;
        }
        private object get_119_Expander_HeaderTemplateSelector(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.HeaderTemplateSelector;
        }
        private void set_119_Expander_HeaderTemplateSelector(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            that.HeaderTemplateSelector = (global::Microsoft.UI.Xaml.Controls.DataTemplateSelector)Value;
        }
        private object get_120_Expander_IsExpanded(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.IsExpanded;
        }
        private void set_120_Expander_IsExpanded(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_121_Expander_TemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.Expander)instance;
            return that.TemplateSettings;
        }
        private object get_122_TasksPanel_CurrentCategory(object instance)
        {
            var that = (global::HddtodoUI.Controls.TasksPanel)instance;
            return that.CurrentCategory;
        }
        private void set_122_TasksPanel_CurrentCategory(object instance, object Value)
        {
            var that = (global::HddtodoUI.Controls.TasksPanel)instance;
            that.CurrentCategory = (global::HddtodoUI.Models.TaskCategoryViewObject)Value;
        }
        private object get_123_ProgressBar_IsIndeterminate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.IsIndeterminate;
        }
        private void set_123_ProgressBar_IsIndeterminate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.IsIndeterminate = (global::System.Boolean)Value;
        }
        private object get_124_ProgressBar_ShowError(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.ShowError;
        }
        private void set_124_ProgressBar_ShowError(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.ShowError = (global::System.Boolean)Value;
        }
        private object get_125_ProgressBar_ShowPaused(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.ShowPaused;
        }
        private void set_125_ProgressBar_ShowPaused(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.ShowPaused = (global::System.Boolean)Value;
        }
        private object get_126_ProgressBar_TemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.TemplateSettings;
        }
        private object get_127_TaskTimeLogStatisticsControl_TotalWorkTime(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.TotalWorkTime;
        }
        private object get_128_TaskTimeLogStatisticsControl_WorkTimePercentage(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.WorkTimePercentage;
        }
        private object get_129_TaskTimeLogStatisticsControl_TotalWorkTimeText(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.TotalWorkTimeText;
        }
        private object get_130_TaskTimeLogStatisticsControl_IsNextDayButtonEnable(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.IsNextDayButtonEnable;
        }
        private object get_131_TaskTimeLogStatisticsControl_CompletedTasksCount(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.CompletedTasksCount;
        }
        private object get_132_TaskTimeLogStatisticsControl_UncompletedTasksCount(object instance)
        {
            var that = (global::HddtodoUI.Controls.TaskTimeLogStatisticsControl)instance;
            return that.UncompletedTasksCount;
        }
        private object get_133_IndicatorWindow_Status(object instance)
        {
            var that = (global::HddtodoUI.Windows.IndicatorWindow)instance;
            return that.Status;
        }
        private void set_133_IndicatorWindow_Status(object instance, object Value)
        {
            var that = (global::HddtodoUI.Windows.IndicatorWindow)instance;
            that.Status = (global::HddtodoUI.Models.TaskStatus)Value;
        }
        private object get_134_NotificationWindow_Level(object instance)
        {
            var that = (global::HddtodoUI.Windows.NotificationWindow)instance;
            return that.Level;
        }
        private void set_134_NotificationWindow_Level(object instance, object Value)
        {
            var that = (global::HddtodoUI.Windows.NotificationWindow)instance;
            that.Level = (global::HddtodoUI.Windows.NotificationLevel)Value;
        }
        private object get_135_NotificationWindow_Height(object instance)
        {
            var that = (global::HddtodoUI.Windows.NotificationWindow)instance;
            return that.Height;
        }
        private object get_136_QuickAddWindow_IsStartTask(object instance)
        {
            var that = (global::HddtodoUI.Windows.QuickAddWindow)instance;
            return that.IsStartTask;
        }
        private void set_136_QuickAddWindow_IsStartTask(object instance, object Value)
        {
            var that = (global::HddtodoUI.Windows.QuickAddWindow)instance;
            that.IsStartTask = (global::System.Boolean)Value;
        }
        private object get_137_TaskDetailsWindow_FromParent(object instance)
        {
            var that = (global::HddtodoUI.Windows.TaskDetailsWindow)instance;
            return that.FromParent;
        }
        private void set_137_TaskDetailsWindow_FromParent(object instance, object Value)
        {
            var that = (global::HddtodoUI.Windows.TaskDetailsWindow)instance;
            that.FromParent = (global::Microsoft.UI.Windowing.AppWindow)Value;
        }
        private object get_138_TreeViewNode_Children(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Children;
        }
        private object get_139_TreeViewNode_Content(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Content;
        }
        private void set_139_TreeViewNode_Content(object instance, object Value)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            that.Content = (global::System.Object)Value;
        }
        private object get_140_TreeViewNode_Depth(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Depth;
        }
        private object get_141_TreeViewNode_HasChildren(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasChildren;
        }
        private object get_142_TreeViewNode_HasUnrealizedChildren(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasUnrealizedChildren;
        }
        private void set_142_TreeViewNode_HasUnrealizedChildren(object instance, object Value)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            that.HasUnrealizedChildren = (global::System.Boolean)Value;
        }
        private object get_143_TreeViewNode_IsExpanded(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.IsExpanded;
        }
        private void set_143_TreeViewNode_IsExpanded(object instance, object Value)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_144_TreeViewNode_Parent(object instance)
        {
            var that = (global::Windows.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Parent;
        }
        private object get_145_DependencyObject_Dispatcher(object instance)
        {
            var that = (global::Windows.UI.Xaml.DependencyObject)instance;
            return that.Dispatcher;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlMember CreateXamlMember(string longMemberName)
        {
            global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember xamlMember = null;
            global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType userType;

            switch (longMemberName)
            {
            case "Microsoft.UI.Xaml.Controls.XamlControlsResources.UseCompactResources":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.XamlControlsResources");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "UseCompactResources", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_0_XamlControlsResources_UseCompactResources;
                xamlMember.Setter = set_0_XamlControlsResources_UseCompactResources;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.IsActive":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsActive", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_1_ProgressRing_IsActive;
                xamlMember.Setter = set_1_ProgressRing_IsActive;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.IsIndeterminate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsIndeterminate", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_2_ProgressRing_IsIndeterminate;
                xamlMember.Setter = set_2_ProgressRing_IsIndeterminate;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.Maximum":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Maximum", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_3_ProgressRing_Maximum;
                xamlMember.Setter = set_3_ProgressRing_Maximum;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.Minimum":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Minimum", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_4_ProgressRing_Minimum;
                xamlMember.Setter = set_4_ProgressRing_Minimum;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.TemplateSettings":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TemplateSettings", "Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings");
                xamlMember.Getter = get_5_ProgressRing_TemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressRing.Value":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressRing");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Value", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_6_ProgressRing_Value;
                xamlMember.Setter = set_6_ProgressRing_Value;
                break;
            case "HddtodoUI.Controls.TaskCategoryItemForPlanControl.Category":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoryItemForPlanControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Category", "HddtodoUI.BackendModels.TaskCategory");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_7_TaskCategoryItemForPlanControl_Category;
                xamlMember.Setter = set_7_TaskCategoryItemForPlanControl_Category;
                break;
            case "HddtodoUI.Controls.TaskCategoryItemForPlanControl.CategoryCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoryItemForPlanControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CategoryCount", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_8_TaskCategoryItemForPlanControl_CategoryCount;
                xamlMember.Setter = set_8_TaskCategoryItemForPlanControl_CategoryCount;
                break;
            case "HddtodoUI.Controls.TaskCategoryItemForPlanControl.TaskCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoryItemForPlanControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskCount", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_9_TaskCategoryItemForPlanControl_TaskCount;
                xamlMember.Setter = set_9_TaskCategoryItemForPlanControl_TaskCount;
                break;
            case "HddtodoUI.Controls.TaskCategoryItemForPlanControl.ParentPath":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoryItemForPlanControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ParentPath", "String");
                xamlMember.Getter = get_10_TaskCategoryItemForPlanControl_ParentPath;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.PlannedProjectsPanel.PlannedCategories":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.PlannedProjectsPanel");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "PlannedCategories", "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>");
                xamlMember.Getter = get_11_PlannedProjectsPanel_PlannedCategories;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount.Category":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Category", "HddtodoUI.BackendModels.TaskCategory");
                xamlMember.Getter = get_12_TaskCategoryWithCount_Category;
                xamlMember.Setter = set_12_TaskCategoryWithCount_Category;
                break;
            case "HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount.SubcategoryCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SubcategoryCount", "Int32");
                xamlMember.Getter = get_13_TaskCategoryWithCount_SubcategoryCount;
                xamlMember.Setter = set_13_TaskCategoryWithCount_SubcategoryCount;
                break;
            case "HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount.TaskCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskCount", "Int32");
                xamlMember.Getter = get_14_TaskCategoryWithCount_TaskCount;
                xamlMember.Setter = set_14_TaskCategoryWithCount_TaskCount;
                break;
            case "HddtodoUI.Controls.PlannedProjectsPanel.GroupedPlannedCategories":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.PlannedProjectsPanel");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "GroupedPlannedCategories", "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>");
                xamlMember.Getter = get_15_PlannedProjectsPanel_GroupedPlannedCategories;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>.Key":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.Grouping`2<String, HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Key", "String");
                xamlMember.Getter = get_16_Grouping_Key;
                xamlMember.SetIsReadOnly();
                break;
            case "System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>.Count":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("System.Collections.ObjectModel.Collection`1<HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Count", "Int32");
                xamlMember.Getter = get_17_Collection_Count;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.QuickAddTaskDialog.IsStartTask":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.QuickAddTaskDialog");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsStartTask", "Boolean");
                xamlMember.Getter = get_18_QuickAddTaskDialog_IsStartTask;
                xamlMember.Setter = set_18_QuickAddTaskDialog_IsStartTask;
                break;
            case "HddtodoUI.Controls.TaskItemControl.TaskVO":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskItemControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskVO", "HddtodoUI.Models.TodoTaskViewObject");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_19_TaskItemControl_TaskVO;
                xamlMember.Setter = set_19_TaskItemControl_TaskVO;
                break;
            case "HddtodoUI.Controls.SubtasksControl.Subtasks":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.SubtasksControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Subtasks", "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.Models.TodoTaskViewObject>");
                xamlMember.Getter = get_20_SubtasksControl_Subtasks;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.TaskID":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskID", "Int64");
                xamlMember.Getter = get_21_TodoTaskViewObject_TaskID;
                xamlMember.Setter = set_21_TodoTaskViewObject_TaskID;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Title":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Title", "String");
                xamlMember.Getter = get_22_TodoTaskViewObject_Title;
                xamlMember.Setter = set_22_TodoTaskViewObject_Title;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.DueDate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DueDate", "System.Nullable`1<System.DateTime>");
                xamlMember.Getter = get_23_TodoTaskViewObject_DueDate;
                xamlMember.Setter = set_23_TodoTaskViewObject_DueDate;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Category":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Category", "String");
                xamlMember.Getter = get_24_TodoTaskViewObject_Category;
                xamlMember.Setter = set_24_TodoTaskViewObject_Category;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.CategoryName":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CategoryName", "String");
                xamlMember.Getter = get_25_TodoTaskViewObject_CategoryName;
                xamlMember.Setter = set_25_TodoTaskViewObject_CategoryName;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Priority":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Priority", "HddtodoUI.BackendModels.TaskPriority");
                xamlMember.Getter = get_26_TodoTaskViewObject_Priority;
                xamlMember.Setter = set_26_TodoTaskViewObject_Priority;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Notes":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Notes", "String");
                xamlMember.Getter = get_27_TodoTaskViewObject_Notes;
                xamlMember.Setter = set_27_TodoTaskViewObject_Notes;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.StartTime":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "StartTime", "System.Nullable`1<System.DateTime>");
                xamlMember.Getter = get_28_TodoTaskViewObject_StartTime;
                xamlMember.Setter = set_28_TodoTaskViewObject_StartTime;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Status":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Status", "HddtodoUI.Models.TaskStatus");
                xamlMember.Getter = get_29_TodoTaskViewObject_Status;
                xamlMember.Setter = set_29_TodoTaskViewObject_Status;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.TaskOrder":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskOrder", "Int64");
                xamlMember.Getter = get_30_TodoTaskViewObject_TaskOrder;
                xamlMember.Setter = set_30_TodoTaskViewObject_TaskOrder;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.GroupOrder":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "GroupOrder", "Int64");
                xamlMember.Getter = get_31_TodoTaskViewObject_GroupOrder;
                xamlMember.Setter = set_31_TodoTaskViewObject_GroupOrder;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Color":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Color", "String");
                xamlMember.Getter = get_32_TodoTaskViewObject_Color;
                xamlMember.Setter = set_32_TodoTaskViewObject_Color;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.Task":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Task", "HddtodoUI.BackendModels.TTask");
                xamlMember.Getter = get_33_TodoTaskViewObject_Task;
                xamlMember.Setter = set_33_TodoTaskViewObject_Task;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.DeletedStatus":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DeletedStatus", "Boolean");
                xamlMember.Getter = get_34_TodoTaskViewObject_DeletedStatus;
                xamlMember.Setter = set_34_TodoTaskViewObject_DeletedStatus;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.ParentTaskIds":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ParentTaskIds", "String");
                xamlMember.Getter = get_35_TodoTaskViewObject_ParentTaskIds;
                xamlMember.Setter = set_35_TodoTaskViewObject_ParentTaskIds;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.DirectSubTaskCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DirectSubTaskCount", "Int32");
                xamlMember.Getter = get_36_TodoTaskViewObject_DirectSubTaskCount;
                xamlMember.Setter = set_36_TodoTaskViewObject_DirectSubTaskCount;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.IsCompleted":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsCompleted", "Boolean");
                xamlMember.Getter = get_37_TodoTaskViewObject_IsCompleted;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.CategoryOrderPath":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CategoryOrderPath", "String");
                xamlMember.Getter = get_38_TodoTaskViewObject_CategoryOrderPath;
                xamlMember.Setter = set_38_TodoTaskViewObject_CategoryOrderPath;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.CompletedDate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CompletedDate", "System.Nullable`1<System.DateTime>");
                xamlMember.Getter = get_39_TodoTaskViewObject_CompletedDate;
                xamlMember.Setter = set_39_TodoTaskViewObject_CompletedDate;
                break;
            case "HddtodoUI.Models.TodoTaskViewObject.DueDateVisibility":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Models.TodoTaskViewObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DueDateVisibility", "Microsoft.UI.Xaml.Visibility");
                xamlMember.Getter = get_40_TodoTaskViewObject_DueDateVisibility;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.SelectionMode":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectionMode", "Microsoft.UI.Xaml.Controls.TreeViewSelectionMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_41_TreeView_SelectionMode;
                xamlMember.Setter = set_41_TreeView_SelectionMode;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemsSource":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemsSource", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_42_TreeView_ItemsSource;
                xamlMember.Setter = set_42_TreeView_ItemsSource;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_43_TreeView_ItemTemplate;
                xamlMember.Setter = set_43_TreeView_ItemTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.CanDragItems":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CanDragItems", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_44_TreeView_CanDragItems;
                xamlMember.Setter = set_44_TreeView_CanDragItems;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.CanReorderItems":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CanReorderItems", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_45_TreeView_CanReorderItems;
                xamlMember.Setter = set_45_TreeView_CanReorderItems;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyle":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemContainerStyle", "Microsoft.UI.Xaml.Style");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_46_TreeView_ItemContainerStyle;
                xamlMember.Setter = set_46_TreeView_ItemContainerStyle;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyleSelector":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemContainerStyleSelector", "Microsoft.UI.Xaml.Controls.StyleSelector");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_47_TreeView_ItemContainerStyleSelector;
                xamlMember.Setter = set_47_TreeView_ItemContainerStyleSelector;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemContainerTransitions":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemContainerTransitions", "Microsoft.UI.Xaml.Media.Animation.TransitionCollection");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_48_TreeView_ItemContainerTransitions;
                xamlMember.Setter = set_48_TreeView_ItemContainerTransitions;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.ItemTemplateSelector":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemTemplateSelector", "Microsoft.UI.Xaml.Controls.DataTemplateSelector");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_49_TreeView_ItemTemplateSelector;
                xamlMember.Setter = set_49_TreeView_ItemTemplateSelector;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.RootNodes":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "RootNodes", "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_50_TreeView_RootNodes;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Children":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Children", "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_51_TreeViewNode_Children;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Content":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Content", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_52_TreeViewNode_Content;
                xamlMember.Setter = set_52_TreeViewNode_Content;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Depth":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Depth", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_53_TreeViewNode_Depth;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HasChildren", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_54_TreeViewNode_HasChildren;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HasUnrealizedChildren", "Boolean");
                xamlMember.Getter = get_55_TreeViewNode_HasUnrealizedChildren;
                xamlMember.Setter = set_55_TreeViewNode_HasUnrealizedChildren;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_56_TreeViewNode_IsExpanded;
                xamlMember.Setter = set_56_TreeViewNode_IsExpanded;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Parent":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Parent", "Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember.Getter = get_57_TreeViewNode_Parent;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.SelectedItem":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectedItem", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_58_TreeView_SelectedItem;
                xamlMember.Setter = set_58_TreeView_SelectedItem;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.SelectedItems":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectedItems", "System.Collections.Generic.IList`1<Object>");
                xamlMember.Getter = get_59_TreeView_SelectedItems;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.SelectedNode":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectedNode", "Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember.Getter = get_60_TreeView_SelectedNode;
                xamlMember.Setter = set_60_TreeView_SelectedNode;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeView.SelectedNodes":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeView");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectedNodes", "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_61_TreeView_SelectedNodes;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.IsExpanded":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_62_TreeViewItem_IsExpanded;
                xamlMember.Setter = set_62_TreeViewItem_IsExpanded;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.CollapsedGlyph":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CollapsedGlyph", "String");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_63_TreeViewItem_CollapsedGlyph;
                xamlMember.Setter = set_63_TreeViewItem_CollapsedGlyph;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.ExpandedGlyph":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ExpandedGlyph", "String");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_64_TreeViewItem_ExpandedGlyph;
                xamlMember.Setter = set_64_TreeViewItem_ExpandedGlyph;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphBrush":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "GlyphBrush", "Microsoft.UI.Xaml.Media.Brush");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_65_TreeViewItem_GlyphBrush;
                xamlMember.Setter = set_65_TreeViewItem_GlyphBrush;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphOpacity":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "GlyphOpacity", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_66_TreeViewItem_GlyphOpacity;
                xamlMember.Setter = set_66_TreeViewItem_GlyphOpacity;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphSize":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "GlyphSize", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_67_TreeViewItem_GlyphSize;
                xamlMember.Setter = set_67_TreeViewItem_GlyphSize;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.HasUnrealizedChildren":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HasUnrealizedChildren", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_68_TreeViewItem_HasUnrealizedChildren;
                xamlMember.Setter = set_68_TreeViewItem_HasUnrealizedChildren;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.ItemsSource":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemsSource", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_69_TreeViewItem_ItemsSource;
                xamlMember.Setter = set_69_TreeViewItem_ItemsSource;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewItem.TreeViewItemTemplateSettings":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewItem");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TreeViewItemTemplateSettings", "Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_70_TreeViewItem_TreeViewItemTemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskCategoriesPanel.IsAddListNameValid":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoriesPanel");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsAddListNameValid", "Boolean");
                xamlMember.Getter = get_71_TaskCategoriesPanel_IsAddListNameValid;
                xamlMember.Setter = set_71_TaskCategoriesPanel_IsAddListNameValid;
                break;
            case "HddtodoUI.Controls.TaskCategoriesTreeViewPanel.IsAddListNameValid":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskCategoriesTreeViewPanel");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsAddListNameValid", "Boolean");
                xamlMember.Getter = get_72_TaskCategoriesTreeViewPanel_IsAddListNameValid;
                xamlMember.Setter = set_72_TaskCategoriesTreeViewPanel_IsAddListNameValid;
                break;
            case "CommunityToolkit.WinUI.Controls.GridSplitter.ResizeBehavior":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.GridSplitter");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ResizeBehavior", "CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeBehavior");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_73_GridSplitter_ResizeBehavior;
                xamlMember.Setter = set_73_GridSplitter_ResizeBehavior;
                break;
            case "CommunityToolkit.WinUI.Controls.GridSplitter.ResizeDirection":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.GridSplitter");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ResizeDirection", "CommunityToolkit.WinUI.Controls.GridSplitter.GridResizeDirection");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_74_GridSplitter_ResizeDirection;
                xamlMember.Setter = set_74_GridSplitter_ResizeDirection;
                break;
            case "CommunityToolkit.WinUI.Controls.GridSplitter.ParentLevel":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.GridSplitter");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ParentLevel", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_75_GridSplitter_ParentLevel;
                xamlMember.Setter = set_75_GridSplitter_ParentLevel;
                break;
            case "CommunityToolkit.WinUI.Controls.SizerBase.Cursor":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Cursor", "Microsoft.UI.Input.InputSystemCursorShape");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_76_SizerBase_Cursor;
                xamlMember.Setter = set_76_SizerBase_Cursor;
                break;
            case "CommunityToolkit.WinUI.Controls.SizerBase.DragIncrement":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DragIncrement", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_77_SizerBase_DragIncrement;
                xamlMember.Setter = set_77_SizerBase_DragIncrement;
                break;
            case "CommunityToolkit.WinUI.Controls.SizerBase.KeyboardIncrement":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "KeyboardIncrement", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_78_SizerBase_KeyboardIncrement;
                xamlMember.Setter = set_78_SizerBase_KeyboardIncrement;
                break;
            case "CommunityToolkit.WinUI.Controls.SizerBase.Orientation":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Orientation", "Microsoft.UI.Xaml.Controls.Orientation");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_79_SizerBase_Orientation;
                xamlMember.Setter = set_79_SizerBase_Orientation;
                break;
            case "CommunityToolkit.WinUI.Controls.SizerBase.IsThumbVisible":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("CommunityToolkit.WinUI.Controls.SizerBase");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsThumbVisible", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_80_SizerBase_IsThumbVisible;
                xamlMember.Setter = set_80_SizerBase_IsThumbVisible;
                break;
            case "HddtodoUI.Controls.TaskStepsControl.TaskSteps":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskStepsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskSteps", "System.Collections.ObjectModel.ObservableCollection`1<HddtodoUI.BackendModels.TaskStep>");
                xamlMember.Getter = get_81_TaskStepsControl_TaskSteps;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.BackendModels.TaskStep.StepId":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "StepId", "Int64");
                xamlMember.Getter = get_82_TaskStep_StepId;
                xamlMember.Setter = set_82_TaskStep_StepId;
                break;
            case "HddtodoUI.BackendModels.TaskStep.TaskId":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskId", "Int64");
                xamlMember.Getter = get_83_TaskStep_TaskId;
                xamlMember.Setter = set_83_TaskStep_TaskId;
                break;
            case "HddtodoUI.BackendModels.TaskStep.UserId":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "UserId", "Int64");
                xamlMember.Getter = get_84_TaskStep_UserId;
                xamlMember.Setter = set_84_TaskStep_UserId;
                break;
            case "HddtodoUI.BackendModels.TaskStep.StepOrder":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "StepOrder", "Int32");
                xamlMember.Getter = get_85_TaskStep_StepOrder;
                xamlMember.Setter = set_85_TaskStep_StepOrder;
                break;
            case "HddtodoUI.BackendModels.TaskStep.Title":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Title", "String");
                xamlMember.Getter = get_86_TaskStep_Title;
                xamlMember.Setter = set_86_TaskStep_Title;
                break;
            case "HddtodoUI.BackendModels.TaskStep.Color":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Color", "String");
                xamlMember.Getter = get_87_TaskStep_Color;
                xamlMember.Setter = set_87_TaskStep_Color;
                break;
            case "HddtodoUI.BackendModels.TaskStep.StepCompleteTime":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "StepCompleteTime", "System.Nullable`1<System.DateTime>");
                xamlMember.Getter = get_88_TaskStep_StepCompleteTime;
                xamlMember.Setter = set_88_TaskStep_StepCompleteTime;
                break;
            case "HddtodoUI.BackendModels.TaskStep.DeletedStatus":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.BackendModels.TaskStep");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "DeletedStatus", "Boolean");
                xamlMember.Getter = get_89_TaskStep_DeletedStatus;
                xamlMember.Setter = set_89_TaskStep_DeletedStatus;
                break;
            case "Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.BreadcrumbBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemTemplate", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_90_BreadcrumbBar_ItemTemplate;
                xamlMember.Setter = set_90_BreadcrumbBar_ItemTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemsSource":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.BreadcrumbBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ItemsSource", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_91_BreadcrumbBar_ItemsSource;
                xamlMember.Setter = set_91_BreadcrumbBar_ItemsSource;
                break;
            case "HddtodoUI.Controls.TaskDetailsControl.CurrentDateTime":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskDetailsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CurrentDateTime", "System.DateTime");
                xamlMember.Getter = get_92_TaskDetailsControl_CurrentDateTime;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Minimum":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Minimum", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_93_NumberBox_Minimum;
                xamlMember.Setter = set_93_NumberBox_Minimum;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Value":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Value", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_94_NumberBox_Value;
                xamlMember.Setter = set_94_NumberBox_Value;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.SpinButtonPlacementMode":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SpinButtonPlacementMode", "Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_95_NumberBox_SpinButtonPlacementMode;
                xamlMember.Setter = set_95_NumberBox_SpinButtonPlacementMode;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.AcceptsExpression":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "AcceptsExpression", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_96_NumberBox_AcceptsExpression;
                xamlMember.Setter = set_96_NumberBox_AcceptsExpression;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Description":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Description", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_97_NumberBox_Description;
                xamlMember.Setter = set_97_NumberBox_Description;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Header":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Header", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_98_NumberBox_Header;
                xamlMember.Setter = set_98_NumberBox_Header;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.HeaderTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HeaderTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_99_NumberBox_HeaderTemplate;
                xamlMember.Setter = set_99_NumberBox_HeaderTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.IsWrapEnabled":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsWrapEnabled", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_100_NumberBox_IsWrapEnabled;
                xamlMember.Setter = set_100_NumberBox_IsWrapEnabled;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.LargeChange":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "LargeChange", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_101_NumberBox_LargeChange;
                xamlMember.Setter = set_101_NumberBox_LargeChange;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Maximum":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Maximum", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_102_NumberBox_Maximum;
                xamlMember.Setter = set_102_NumberBox_Maximum;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.NumberFormatter":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "NumberFormatter", "Windows.Globalization.NumberFormatting.INumberFormatter2");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_103_NumberBox_NumberFormatter;
                xamlMember.Setter = set_103_NumberBox_NumberFormatter;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.PlaceholderText":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "PlaceholderText", "String");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_104_NumberBox_PlaceholderText;
                xamlMember.Setter = set_104_NumberBox_PlaceholderText;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.PreventKeyboardDisplayOnProgrammaticFocus":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "PreventKeyboardDisplayOnProgrammaticFocus", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_105_NumberBox_PreventKeyboardDisplayOnProgrammaticFocus;
                xamlMember.Setter = set_105_NumberBox_PreventKeyboardDisplayOnProgrammaticFocus;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.SelectionFlyout":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectionFlyout", "Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_106_NumberBox_SelectionFlyout;
                xamlMember.Setter = set_106_NumberBox_SelectionFlyout;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.SelectionHighlightColor":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SelectionHighlightColor", "Microsoft.UI.Xaml.Media.SolidColorBrush");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_107_NumberBox_SelectionHighlightColor;
                xamlMember.Setter = set_107_NumberBox_SelectionHighlightColor;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.SmallChange":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SmallChange", "Double");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_108_NumberBox_SmallChange;
                xamlMember.Setter = set_108_NumberBox_SmallChange;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.Text":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Text", "String");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_109_NumberBox_Text;
                xamlMember.Setter = set_109_NumberBox_Text;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.TextReadingOrder":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TextReadingOrder", "Microsoft.UI.Xaml.TextReadingOrder");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_110_NumberBox_TextReadingOrder;
                xamlMember.Setter = set_110_NumberBox_TextReadingOrder;
                break;
            case "Microsoft.UI.Xaml.Controls.NumberBox.ValidationMode":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.NumberBox");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ValidationMode", "Microsoft.UI.Xaml.Controls.NumberBoxValidationMode");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_111_NumberBox_ValidationMode;
                xamlMember.Setter = set_111_NumberBox_ValidationMode;
                break;
            case "HddtodoUI.Controls.TaskReminderDialog.Reminder":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskReminderDialog");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Reminder", "HddtodoUI.BackendModels.TaskReminder");
                xamlMember.Getter = get_112_TaskReminderDialog_Reminder;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskRestartDialog.Restart":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskRestartDialog");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Restart", "HddtodoUI.BackendModels.TaskRestart");
                xamlMember.Getter = get_113_TaskRestartDialog_Restart;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Selectors.TaskListItemTemplateSelector.TaskTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Selectors.TaskListItemTemplateSelector");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TaskTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.Getter = get_114_TaskListItemTemplateSelector_TaskTemplate;
                xamlMember.Setter = set_114_TaskListItemTemplateSelector_TaskTemplate;
                break;
            case "HddtodoUI.Selectors.TaskListItemTemplateSelector.SubCategoryTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Selectors.TaskListItemTemplateSelector");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "SubCategoryTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.Getter = get_115_TaskListItemTemplateSelector_SubCategoryTemplate;
                xamlMember.Setter = set_115_TaskListItemTemplateSelector_SubCategoryTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.HeaderTemplate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HeaderTemplate", "Microsoft.UI.Xaml.DataTemplate");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_116_Expander_HeaderTemplate;
                xamlMember.Setter = set_116_Expander_HeaderTemplate;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.ExpandDirection":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ExpandDirection", "Microsoft.UI.Xaml.Controls.ExpandDirection");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_117_Expander_ExpandDirection;
                xamlMember.Setter = set_117_Expander_ExpandDirection;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.Header":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Header", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_118_Expander_Header;
                xamlMember.Setter = set_118_Expander_Header;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.HeaderTemplateSelector":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HeaderTemplateSelector", "Microsoft.UI.Xaml.Controls.DataTemplateSelector");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_119_Expander_HeaderTemplateSelector;
                xamlMember.Setter = set_119_Expander_HeaderTemplateSelector;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.IsExpanded":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_120_Expander_IsExpanded;
                xamlMember.Setter = set_120_Expander_IsExpanded;
                break;
            case "Microsoft.UI.Xaml.Controls.Expander.TemplateSettings":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Expander");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TemplateSettings", "Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings");
                xamlMember.Getter = get_121_Expander_TemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TasksPanel.CurrentCategory":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TasksPanel");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CurrentCategory", "HddtodoUI.Models.TaskCategoryViewObject");
                xamlMember.Getter = get_122_TasksPanel_CurrentCategory;
                xamlMember.Setter = set_122_TasksPanel_CurrentCategory;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.IsIndeterminate":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsIndeterminate", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_123_ProgressBar_IsIndeterminate;
                xamlMember.Setter = set_123_ProgressBar_IsIndeterminate;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.ShowError":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ShowError", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_124_ProgressBar_ShowError;
                xamlMember.Setter = set_124_ProgressBar_ShowError;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.ShowPaused":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "ShowPaused", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_125_ProgressBar_ShowPaused;
                xamlMember.Setter = set_125_ProgressBar_ShowPaused;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.TemplateSettings":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TemplateSettings", "Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings");
                xamlMember.Getter = get_126_ProgressBar_TemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.TotalWorkTime":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TotalWorkTime", "Double");
                xamlMember.Getter = get_127_TaskTimeLogStatisticsControl_TotalWorkTime;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.WorkTimePercentage":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "WorkTimePercentage", "Double");
                xamlMember.Getter = get_128_TaskTimeLogStatisticsControl_WorkTimePercentage;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.TotalWorkTimeText":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "TotalWorkTimeText", "String");
                xamlMember.Getter = get_129_TaskTimeLogStatisticsControl_TotalWorkTimeText;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.IsNextDayButtonEnable":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsNextDayButtonEnable", "Boolean");
                xamlMember.Getter = get_130_TaskTimeLogStatisticsControl_IsNextDayButtonEnable;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.CompletedTasksCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "CompletedTasksCount", "Int32");
                xamlMember.Getter = get_131_TaskTimeLogStatisticsControl_CompletedTasksCount;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Controls.TaskTimeLogStatisticsControl.UncompletedTasksCount":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Controls.TaskTimeLogStatisticsControl");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "UncompletedTasksCount", "Int32");
                xamlMember.Getter = get_132_TaskTimeLogStatisticsControl_UncompletedTasksCount;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Windows.IndicatorWindow.Status":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Windows.IndicatorWindow");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Status", "HddtodoUI.Models.TaskStatus");
                xamlMember.Getter = get_133_IndicatorWindow_Status;
                xamlMember.Setter = set_133_IndicatorWindow_Status;
                break;
            case "HddtodoUI.Windows.NotificationWindow.Level":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Windows.NotificationWindow");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Level", "HddtodoUI.Windows.NotificationLevel");
                xamlMember.Getter = get_134_NotificationWindow_Level;
                xamlMember.Setter = set_134_NotificationWindow_Level;
                break;
            case "HddtodoUI.Windows.NotificationWindow.Height":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Windows.NotificationWindow");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Height", "Int32");
                xamlMember.Getter = get_135_NotificationWindow_Height;
                xamlMember.SetIsReadOnly();
                break;
            case "HddtodoUI.Windows.QuickAddWindow.IsStartTask":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Windows.QuickAddWindow");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsStartTask", "Boolean");
                xamlMember.Getter = get_136_QuickAddWindow_IsStartTask;
                xamlMember.Setter = set_136_QuickAddWindow_IsStartTask;
                break;
            case "HddtodoUI.Windows.TaskDetailsWindow.FromParent":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("HddtodoUI.Windows.TaskDetailsWindow");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "FromParent", "Microsoft.UI.Windowing.AppWindow");
                xamlMember.Getter = get_137_TaskDetailsWindow_FromParent;
                xamlMember.Setter = set_137_TaskDetailsWindow_FromParent;
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.Children":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Children", "System.Collections.Generic.IList`1<Windows.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_138_TreeViewNode_Children;
                xamlMember.SetIsReadOnly();
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.Content":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Content", "Object");
                xamlMember.Getter = get_139_TreeViewNode_Content;
                xamlMember.Setter = set_139_TreeViewNode_Content;
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.Depth":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Depth", "Int32");
                xamlMember.Getter = get_140_TreeViewNode_Depth;
                xamlMember.SetIsReadOnly();
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.HasChildren":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HasChildren", "Boolean");
                xamlMember.Getter = get_141_TreeViewNode_HasChildren;
                xamlMember.SetIsReadOnly();
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "HasUnrealizedChildren", "Boolean");
                xamlMember.Getter = get_142_TreeViewNode_HasUnrealizedChildren;
                xamlMember.Setter = set_142_TreeViewNode_HasUnrealizedChildren;
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.IsExpanded":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.Getter = get_143_TreeViewNode_IsExpanded;
                xamlMember.Setter = set_143_TreeViewNode_IsExpanded;
                break;
            case "Windows.UI.Xaml.Controls.TreeViewNode.Parent":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Parent", "Windows.UI.Xaml.Controls.TreeViewNode");
                xamlMember.Getter = get_144_TreeViewNode_Parent;
                xamlMember.SetIsReadOnly();
                break;
            case "Windows.UI.Xaml.DependencyObject.Dispatcher":
                userType = (global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Windows.UI.Xaml.DependencyObject");
                xamlMember = new global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlMember(this, "Dispatcher", "Windows.UI.Core.CoreDispatcher");
                xamlMember.Getter = get_145_DependencyObject_Dispatcher;
                xamlMember.SetIsReadOnly();
                break;
            }
            return xamlMember;
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlSystemBaseType : global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        string _fullName;
        global::System.Type _underlyingType;

        public XamlSystemBaseType(string fullName, 
            global::System.Type underlyingType)
        {
            _fullName = fullName;
            _underlyingType = underlyingType;
        }

        public string FullName { get { return _fullName; } }

        public global::System.Type UnderlyingType
        {
            get
            {
                return _underlyingType;
            }
        }

        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name) { throw new global::System.NotImplementedException(); }
        virtual public bool IsArray { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsCollection { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsConstructible { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsDictionary { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsMarkupExtension { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsBindable { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsReturnTypeStub { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsLocalType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { throw new global::System.NotImplementedException(); } }
        virtual public object ActivateInstance() { throw new global::System.NotImplementedException(); }
        virtual public void AddToMap(object instance, object key, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void AddToVector(object instance, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void RunInitializer()   { throw new global::System.NotImplementedException(); }
        virtual public object CreateFromString(string input)   { throw new global::System.NotImplementedException(); }
    }
    
    internal delegate object Activator();
    internal delegate void StaticInitializer();
    internal delegate void AddToCollection(object instance, object item);
    internal delegate void AddToDictionary(object instance, object key, object item);
    internal delegate object CreateFromStringMethod(string args);
    internal delegate object BoxInstanceMethod(object instance);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlUserType : global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlSystemBaseType
        , global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider _provider;
        global::Microsoft.UI.Xaml.Markup.IXamlType _baseType;
        global::Microsoft.UI.Xaml.Markup.IXamlType _boxedType;
        bool _isArray;
        bool _isMarkupExtension;
        bool _isBindable;
        bool _isReturnTypeStub;
        bool _isLocalType;

        string _contentPropertyName;
        string _itemTypeName;
        string _keyTypeName;
        global::System.Collections.Generic.Dictionary<string, string> _memberNames;
        global::System.Collections.Generic.Dictionary<string, object> _enumValues;

        public XamlUserType(global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider provider, string fullName, 
            global::System.Type fullType, global::Microsoft.UI.Xaml.Markup.IXamlType baseType)
            :base(fullName, fullType)
        {
            _provider = provider;
            _baseType = baseType;
        }

        // --- Interface methods ----

        override public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { return _baseType; } }
        override public bool IsArray { get { return _isArray; } }
        override public bool IsCollection { get { return (CollectionAdd != null); } }
        override public bool IsConstructible { get { return (Activator != null); } }
        override public bool IsDictionary { get { return (DictionaryAdd != null); } }
        override public bool IsMarkupExtension { get { return _isMarkupExtension; } }
        override public bool IsBindable { get { return _isBindable; } }
        override public bool IsReturnTypeStub { get { return _isReturnTypeStub; } }
        override public bool IsLocalType { get { return _isLocalType; } }
        override public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { return _boxedType; } }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty
        {
            get { return _provider.GetMemberByLongName(_contentPropertyName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType
        {
            get { return _provider.GetXamlTypeByName(_itemTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType
        {
            get { return _provider.GetXamlTypeByName(_keyTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name)
        {
            if (_memberNames == null)
            {
                return null;
            }
            string longName;
            if (_memberNames.TryGetValue(name, out longName))
            {
                return _provider.GetMemberByLongName(longName);
            }
            return null;
        }

        override public object ActivateInstance()
        {
            return Activator(); 
        }

        override public void AddToMap(object instance, object key, object item) 
        {
            DictionaryAdd(instance, key, item);
        }

        override public void AddToVector(object instance, object item)
        {
            CollectionAdd(instance, item);
        }

        override public void RunInitializer() 
        {
            StaticInitializer();
        }

        override public object CreateFromString(string input)
        {
            if (BoxedType != null)
            {
                return BoxInstance(BoxedType.CreateFromString(input));
            }

            if (CreateFromStringMethod != null)
            {
                return this.CreateFromStringMethod(input);
            }
            else if (_enumValues != null)
            {
                long value = 0;

                string[] valueParts = input.Split(',');

                foreach (string valuePart in valueParts) 
                {
                    object partValue;
                    long enumFieldValue = 0;
                    try
                    {
                        if (_enumValues.TryGetValue(valuePart.Trim(), out partValue))
                        {
                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                        }
                        else
                        {
                            try
                            {
                                enumFieldValue = global::System.Convert.ToInt64(valuePart.Trim());
                            }
                            catch( global::System.FormatException )
                            {
                                foreach( string key in _enumValues.Keys )
                                {
                                    if( string.Compare(valuePart.Trim(), key, global::System.StringComparison.OrdinalIgnoreCase) == 0 )
                                    {
                                        if( _enumValues.TryGetValue(key.Trim(), out partValue) )
                                        {
                                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        value |= enumFieldValue; 
                    }
                    catch( global::System.FormatException )
                    {
                        throw new global::System.ArgumentException(input, FullName);
                    }
                }

                return global::System.Convert.ChangeType(value, global::System.Enum.GetUnderlyingType(this.UnderlyingType));
            }
            throw new global::System.ArgumentException(input, FullName);
        }

        // --- End of Interface methods

        public Activator Activator { get; set; }
        public StaticInitializer StaticInitializer { get; set; }
        public AddToCollection CollectionAdd { get; set; }
        public AddToDictionary DictionaryAdd { get; set; }
        public CreateFromStringMethod CreateFromStringMethod {get; set; }
        public BoxInstanceMethod BoxInstance {get; set; }

        public void SetContentPropertyName(string contentPropertyName)
        {
            _contentPropertyName = contentPropertyName;
        }

        public void SetIsArray()
        {
            _isArray = true; 
        }

        public void SetIsMarkupExtension()
        {
            _isMarkupExtension = true;
        }

        public void SetIsBindable()
        {
            _isBindable = true;
        }

        public void SetIsReturnTypeStub()
        {
            _isReturnTypeStub = true;
        }

        public void SetIsLocalType()
        {
            _isLocalType = true;
        }

        public void SetItemTypeName(string itemTypeName)
        {
            _itemTypeName = itemTypeName;
        }

        public void SetKeyTypeName(string keyTypeName)
        {
            _keyTypeName = keyTypeName;
        }

        public void SetBoxedType(global::Microsoft.UI.Xaml.Markup.IXamlType boxedType)
        {
            _boxedType = boxedType;
        }

        public object BoxType<T>(object instance) where T: struct
        {
            T unwrapped = (T)instance;
            return new global::System.Nullable<T>(unwrapped);
        }

        public void AddMemberName(string shortName)
        {
            if(_memberNames == null)
            {
                _memberNames =  new global::System.Collections.Generic.Dictionary<string,string>();
            }
            _memberNames.Add(shortName, FullName + "." + shortName);
        }

        public void AddEnumValue(string name, object value)
        {
            if (_enumValues == null)
            {
                _enumValues = new global::System.Collections.Generic.Dictionary<string, object>();
            }
            _enumValues.Add(name, value);
        }
    }

    internal delegate object Getter(object instance);
    internal delegate void Setter(object instance, object value);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlMember : global::Microsoft.UI.Xaml.Markup.IXamlMember
    {
        global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider _provider;
        string _name;
        bool _isAttachable;
        bool _isDependencyProperty;
        bool _isReadOnly;

        string _typeName;
        string _targetTypeName;

        public XamlMember(global::HddtodoUI.HddtodoUI_XamlTypeInfo.XamlTypeInfoProvider provider, string name, string typeName)
        {
            _name = name;
            _typeName = typeName;
            _provider = provider;
        }

        public string Name { get { return _name; } }

        public global::Microsoft.UI.Xaml.Markup.IXamlType Type
        {
            get { return _provider.GetXamlTypeByName(_typeName); }
        }

        public void SetTargetTypeName(string targetTypeName)
        {
            _targetTypeName = targetTypeName;
        }
        public global::Microsoft.UI.Xaml.Markup.IXamlType TargetType
        {
            get { return _provider.GetXamlTypeByName(_targetTypeName); }
        }

        public void SetIsAttachable() { _isAttachable = true; }
        public bool IsAttachable { get { return _isAttachable; } }

        public void SetIsDependencyProperty() { _isDependencyProperty = true; }
        public bool IsDependencyProperty { get { return _isDependencyProperty; } }

        public void SetIsReadOnly() { _isReadOnly = true; }
        public bool IsReadOnly { get { return _isReadOnly; } }

        public Getter Getter { get; set; }
        public object GetValue(object instance)
        {
            if (Getter != null)
                return Getter(instance);
            else
                throw new global::System.InvalidOperationException("GetValue");
        }

        public Setter Setter { get; set; }
        public void SetValue(object instance, object value)
        {
            if (Setter != null)
                Setter(instance, value);
            else
                throw new global::System.InvalidOperationException("SetValue");
        }
    }
}

