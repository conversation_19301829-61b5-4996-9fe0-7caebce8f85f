using System;
using HddtodoUI.Models;
using Microsoft.UI.Xaml.Data;

namespace HddtodoUI.Converters
{
    public class TaskStatusIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is TaskStatus status)
            {
                return status switch
                {
                    TaskStatus.NotStarted => "\uE768", // Play
                    TaskStatus.InProgress => "\uE769", // Pause
                    TaskStatus.Paused => "\uE768",     // Play
                    TaskStatus.Completed => "\uE73E",  // CheckMark
                    _ => "\uE768"                      // Play
                };
            }
            return "\uE768"; // Play
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
