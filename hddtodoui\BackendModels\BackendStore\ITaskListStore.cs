using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskListStore
    {
        public List<TaskListWithCount> getUncompleteTaskListsSourceWithCount(long userId);
        List<TaskList> getUncompleteTaskListsSource(long userId);
        TaskList getTaskListByKey(string key, long userId);
        TaskList createEmptyTaskList(string listName, string key, long userId);
        long getTaskListCount(long userId);
        bool hasTaskListByKey(string key, long userId);
        void removeTaskList(TaskList taskList, long userId);
        TaskList saveTaskListChange(TaskList taskList, long userId);
        void InsertTaskListTo(TaskList from, TaskList target, long userId);
        long getTaskListUnCompleteCount(string key, long userId);
        long getTaskListCompleteCount(string key, long userId);

        public List<TaskList> GetCompletedTaskListsPaged(long userId, int page, int pageSize);

        public long GetCompletedTaskListCount(long userId);

        /// <summary>
        /// 根据父任务列表的Key获取子任务列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="parentKey">父任务列表的Key，如果为null则获取顶级任务列表</param>
        /// <param name="includeDeleted">是否包含已删除的任务列表</param>
        /// <returns>子任务列表集合</returns>
        Task<List<TaskList>> GetTaskListsByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false);

        /// <summary>
        /// 根据父任务列表的Key获取子任务列表（包含未完成任务数量）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="parentKey">父任务列表的Key，如果为null则获取顶级任务列表</param>
        /// <param name="includeDeleted">是否包含已删除的任务列表</param>
        /// <returns>子任务列表集合（包含未完成任务数量）</returns>
        Task<List<TaskListWithCount>> GetTaskListsWithCountByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false);
    }
}
