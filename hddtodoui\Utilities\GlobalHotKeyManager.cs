using System;
using System.Runtime.InteropServices;

namespace HddtodoUI.Utilities
{
    public class GlobalHotKeyManager : IDisposable
    {
        private readonly IntPtr _windowHandle;
        private readonly int WM_HOTKEY = 0x0312;
        private delegate IntPtr WndProcDelegate(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam);
        private WndProcDelegate _newWndProc;
        private IntPtr _oldWndProc;
        private bool _isDisposed;

        public event EventHandler HotKeyPressed;
        public event EventHandler SearchHotKeyPressed;

        // Win32 API
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        // 针对64位平台的SetWindowLongPtr
        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr", SetLastError = true)]
        private static extern IntPtr SetWindowLongPtr64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        // 针对32位平台的SetWindowLong
        [DllImport("user32.dll", EntryPoint = "SetWindowLong", SetLastError = true)]
        private static extern IntPtr SetWindowLong32(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        // 根据平台选择合适的函数
        private static IntPtr SetWindowLongPtrCompat(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            if (IntPtr.Size == 8) // 64位平台
                return SetWindowLongPtr64(hWnd, nIndex, dwNewLong);
            else // 32位平台
                return SetWindowLong32(hWnd, nIndex, dwNewLong);
        }

        [DllImport("user32.dll")]
        private static extern IntPtr CallWindowProc(IntPtr lpPrevWndFunc, IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        private const int GWLP_WNDPROC = -4;
        private const int HOTKEY_ID = 9000;
        private const int SEARCH_HOTKEY_ID = 9001;
        private const uint MOD_ALT = 0x0001;
        private const uint MOD_CONTROL = 0x0002;
        private const uint MOD_SHIFT = 0x0004;
        private const uint VK_N = 0x4E;
        private const uint VK_S = 0x53;
        private const uint VK_INSERT = 0x2D;

        public GlobalHotKeyManager(IntPtr windowHandle)
        {
            _windowHandle = windowHandle;
            _newWndProc = new WndProcDelegate(WndProc);
            _oldWndProc = SetWindowLongPtrCompat(windowHandle, GWLP_WNDPROC, Marshal.GetFunctionPointerForDelegate(_newWndProc));

            // Register Ctrl+Alt+N as the global hotkey
            RegisterHotKey(_windowHandle, HOTKEY_ID, MOD_CONTROL | MOD_SHIFT, VK_INSERT);
            
            // Register Ctrl+Shift+S as the search hotkey
            RegisterHotKey(_windowHandle, SEARCH_HOTKEY_ID, MOD_CONTROL | MOD_SHIFT, VK_S);
        }

        private IntPtr WndProc(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam)
        {
            if (msg == WM_HOTKEY)
            {
                int id = wParam.ToInt32();
                if (id == HOTKEY_ID)
                {
                    HotKeyPressed?.Invoke(this, EventArgs.Empty);
                }
                else if (id == SEARCH_HOTKEY_ID)
                {
                    SearchHotKeyPressed?.Invoke(this, EventArgs.Empty);
                }
            }
            return CallWindowProc(_oldWndProc, hWnd, msg, wParam, lParam);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                }

                // Unregister the hotkey
                UnregisterHotKey(_windowHandle, HOTKEY_ID);
                UnregisterHotKey(_windowHandle, SEARCH_HOTKEY_ID);

                // Restore the original window procedure
                if (_oldWndProc != IntPtr.Zero)
                {
                    SetWindowLongPtrCompat(_windowHandle, GWLP_WNDPROC, _oldWndProc);
                    _oldWndProc = IntPtr.Zero;
                }

                _isDisposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~GlobalHotKeyManager()
        {
            Dispose(false);
        }
    }
}
