using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using HddtodoUI.Views;
using HddtodoUI.Windows;
using Microsoft.UI;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Documents;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Media.Imaging;
using WinRT.Interop;

namespace HddtodoUI
{
    public sealed partial class MainWindow : Window
    {
        private GlobalHotKeyManager _hotKeyManager;
        private TrayIconService _trayIconService;
        private MainView mainView;
        private bool _isClosingForReal = false;

        public MainWindow()
        {
            this.InitializeComponent();

            // Get the window handle
            WindowsUtils.ResizeWindow(this, 1280, 1000);
            WindowsUtils.CenterWindow(this);

            // Set the title bar
            ExtendsContentIntoTitleBar = true;
            SetTitleBar(AppTitleBar);

            // Get reference to MainView
            mainView = this.Content as Grid != null
                ? (this.Content as Grid).FindName("MainViewControl") as MainView
                : null;

            // Initialize hot key manager
            _hotKeyManager = new GlobalHotKeyManager(WindowNative.GetWindowHandle(this));
            _hotKeyManager.HotKeyPressed += HotKeyManager_HotKeyPressed;
            _hotKeyManager.SearchHotKeyPressed += HotKeyManager_SearchHotKeyPressed;

            // Initialize tray icon service
            _trayIconService = new TrayIconService(this);

            // Register for window closing and closed events
            this.AppWindow.Closing += Window_Closing;
            this.Closed += Window_Closed;
            
            this.AppWindow.SetIcon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets/HddTodoIcon.ico"));
            
            ReminderBackgroundService.Instance.Start();
            TaskActivityMonitorService.Instance.Start();


            var ret = StoreFactoryHolder.getTaskStore().getLastExecutionUncompletedTask(UserInfoHolder.getUserId());
            if (ret != null)
            {
                NotificationUiHelper.ShowTaskAddNotification(ret,"上次未完成的任务，是否继续？");
            }

        }

        private void Window_Closing(AppWindow sender, AppWindowClosingEventArgs args)
        {
            if (!_isClosingForReal)
            {
                args.Cancel = true; // 取消关闭操作
                
                // 最小化窗口
                var hwnd = WindowNative.GetWindowHandle(this);
                var windowId = Win32Interop.GetWindowIdFromWindow(hwnd);
                var appWindow = AppWindow.GetFromWindowId(windowId);
                var presenter = appWindow.Presenter as OverlappedPresenter;
                presenter?.Minimize();
            }
        }
        

        private void HotKeyManager_HotKeyPressed(object sender, EventArgs e)
        {
            // Use DispatcherQueue to show dialog from non-UI thread
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, () =>
            {
                var quickAddWindow = new QuickAddWindow();
                quickAddWindow.TaskSaved += (s, taskVO) =>
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(taskVO.Category, UserInfoHolder.getUserId());
                    var task = TTaskFactory.CreateTaskInTaskList(taskVO.Title, taskVO.DueDate,taskVO.Priority, list);
                    TaskUICoordinatorFactory.Instance(task).OnTaskAdded(task, list);
                    if (quickAddWindow.IsStartTask)
                    {
                        var ttm=TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                        TaskUICoordinatorFactory.Instance(task).StartOrPauseButtonClick(ttm);
                    }
                        
                };
                quickAddWindow.ShowAndActivate();
            });
        }

        private void HotKeyManager_SearchHotKeyPressed(object sender, EventArgs e)
        {
            // 使用 DispatcherQueue 从非 UI 线程显示对话框
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, () =>
            {
                var searchWindow = new TaskSearchWindow();
                searchWindow.ShowAndActivate();
            });
        }

        private void Window_Closed(object sender, WindowEventArgs args)
        {
            _hotKeyManager.Dispose();
            _trayIconService.Dispose();
            
            // 停止后台服务
            ReminderBackgroundService.Instance.Stop();
            TaskActivityMonitorService.Instance.Stop();
        }
        
        public MainView GetMainView() => mainView;

        // 添加一个方法，用于真正关闭窗口
        public void CloseForReal()
        {
            _isClosingForReal = true;
            
            // 释放资源
            _hotKeyManager?.Dispose();
            _trayIconService?.Dispose();
            
            // 停止后台服务
            ReminderBackgroundService.Instance.Stop();
            TaskActivityMonitorService.Instance.Stop();
            FocusServer.FocusServer.Instance.Stop();
            // 关闭窗口
            this.Close();
            
            // 确保完全退出应用程序
            Application.Current.Exit();
            Environment.Exit(0);
        }

        // 测试通知功能
        private void TestNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示一个信息级别通知
            NotificationService.Instance.ShowNotification("这是一个信息级别的通知示例", NotificationLevel.Info, "信息通知");

            // 延迟1秒后显示警告级别通知
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, async () =>
            {
                await System.Threading.Tasks.Task.Delay(1000);
                NotificationService.Instance.ShowNotification("这是一个警告级别的通知示例", NotificationLevel.Warning, "警告通知");
            });

            // 延迟2秒后显示危险级别通知
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, async () =>
            {
                await System.Threading.Tasks.Task.Delay(2000);
                NotificationService.Instance.ShowNotification("这是一个危险级别的通知示例", NotificationLevel.Danger, "危险通知");
            });

            // 延迟3秒后显示成功级别通知
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, async () =>
            {
                await System.Threading.Tasks.Task.Delay(3000);
                NotificationService.Instance.ShowNotification("这是一个成功级别的通知示例", NotificationLevel.Success, "成功通知");
            });
            
            // 延迟4秒后显示自定义内容通知
            DispatcherQueue.TryEnqueue(DispatcherQueuePriority.Normal, async () =>
            {
                await System.Threading.Tasks.Task.Delay(4000);
                var customContent = CreateCustomNotificationContent();
                NotificationService.Instance.ShowNotification(customContent, NotificationLevel.Info, "自定义内容通知",20000);
            });
        }
        
        /// <summary>
        /// 创建自定义通知内容
        /// </summary>
        private UIElement CreateCustomNotificationContent()
        {
            // 创建一个包含多种控件的面板
            var panel = new StackPanel
            {
                Spacing = 10
            };

            // 添加一个文本块
            panel.Children.Add(new TextBlock
            {
                Text = "这是一个自定义内容通知，包含以下内容：",
                TextWrapping = TextWrapping.Wrap
            });

            // 添加一个超链接
            var hyperlinkTextBlock = new TextBlock
            {
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 5, 0, 5)
            };

            var hyperlink = new Hyperlink();
            hyperlink.NavigateUri = new Uri("https://www.microsoft.com");
            hyperlink.Inlines.Add(new Run { Text = "点击访问Microsoft官网" });
            hyperlink.Click += Hyperlink_Click;

            hyperlinkTextBlock.Inlines.Add(hyperlink);
            panel.Children.Add(hyperlinkTextBlock);

            // 添加一个进度条
            panel.Children.Add(new ProgressBar
            {
                Value = 75,
                Height = 10,
                Margin = new Thickness(0, 5, 0, 5)
            });

            // 添加一个按钮
            var button = new Button
            {
                Content = "点击我",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(0, 5, 0, 5)
            };
            button.Click += CustomButton_Click;
            panel.Children.Add(button);

            return panel;
        }

        /// <summary>
        /// 超链接点击事件
        /// </summary>
        private  void Hyperlink_Click(Hyperlink sender, HyperlinkClickEventArgs args)
        {
            // 使用默认浏览器打开链接
            
        }

        /// <summary>
        /// 自定义按钮点击事件
        /// </summary>
        private void CustomButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示一个对话框
            var dialog = new ContentDialog
            {
                Title = "按钮点击",
                Content = "你点击了通知中的按钮！",
                CloseButtonText = "确定",
                XamlRoot = Content.XamlRoot
            };
            _ = dialog.ShowAsync();
        }

        private void ListButton_Click(object sender, RoutedEventArgs e)
        {
            mainView.ToggleLeftSidePanel();
        }
    }
}