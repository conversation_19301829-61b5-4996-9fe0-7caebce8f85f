{"Version": 1, "WorkspaceRootPath": "D:\\netProject\\newhddtodoui\\hddtodoui\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|d:\\netproject\\newhddtodoui\\hddtodoui\\controls\\taskspanel.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|solutionrelative:controls\\taskspanel.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|d:\\netproject\\newhddtodoui\\hddtodoui\\controls\\plannedprojectspanel.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|solutionrelative:controls\\plannedprojectspanel.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|d:\\netproject\\newhddtodoui\\hddtodoui\\controls\\plannedprojectspanel.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|solutionrelative:controls\\plannedprojectspanel.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|d:\\netproject\\newhddtodoui\\hddtodoui\\views\\mainview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{8D3C8FED-99F2-4358-80EC-92A5A925DF86}|HddtodoUI.csproj|solutionrelative:views\\mainview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "TasksPanel.xaml", "DocumentMoniker": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\TasksPanel.xaml", "RelativeDocumentMoniker": "Controls\\TasksPanel.xaml", "ToolTip": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\TasksPanel.xaml", "RelativeToolTip": "Controls\\TasksPanel.xaml", "ViewState": "AgIAAEUAAAAAAAAAAAAowFIAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-25T14:27:42.861Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PlannedProjectsPanel.xaml", "DocumentMoniker": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\PlannedProjectsPanel.xaml", "RelativeDocumentMoniker": "Controls\\PlannedProjectsPanel.xaml", "ToolTip": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\PlannedProjectsPanel.xaml", "RelativeToolTip": "Controls\\PlannedProjectsPanel.xaml", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T15:20:32.846Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "PlannedProjectsPanel.xaml.cs", "DocumentMoniker": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\PlannedProjectsPanel.xaml.cs", "RelativeDocumentMoniker": "Controls\\PlannedProjectsPanel.xaml.cs", "ToolTip": "D:\\netProject\\newhddtodoui\\hddtodoui\\Controls\\PlannedProjectsPanel.xaml.cs", "RelativeToolTip": "Controls\\PlannedProjectsPanel.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:20:31.555Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainView.xaml", "DocumentMoniker": "D:\\netProject\\newhddtodoui\\hddtodoui\\Views\\MainView.xaml", "RelativeDocumentMoniker": "Views\\MainView.xaml", "ToolTip": "D:\\netProject\\newhddtodoui\\hddtodoui\\Views\\MainView.xaml", "RelativeToolTip": "Views\\MainView.xaml", "ViewState": "AgIAACEAAAAAAAAAAAAAAEUAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-24T15:00:30.537Z"}]}]}]}