﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\PlannedProjectsPanel.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4E35D14E5C3AFBBA1FE64400A09C017BD157382D4B3FD46381CDEA2E70053CC5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class PlannedProjectsPanel : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Data_CollectionViewSource_Source(global::Microsoft.UI.Xaml.Data.CollectionViewSource obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.Source = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::HddtodoUI.BackendModels.TaskCategory value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::HddtodoUI.BackendModels.TaskCategory) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::HddtodoUI.BackendModels.TaskCategory), targetNullValue);
                }
                obj.Category = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.CategoryCount = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.TaskCount = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class PlannedProjectsPanel_obj7_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            IPlannedProjectsPanel_Bindings
        {
            private global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj7;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj7CategoryDisabled = false;
            private static bool isobj7CategoryCountDisabled = false;
            private static bool isobj7TaskCountDisabled = false;

            public PlannedProjectsPanel_obj7_Bindings()
            {
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 78 && columnNumber == 25)
                {
                    isobj7CategoryDisabled = true;
                }
                else if (lineNumber == 79 && columnNumber == 25)
                {
                    isobj7CategoryCountDisabled = true;
                }
                else if (lineNumber == 80 && columnNumber == 25)
                {
                    isobj7TaskCountDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 7: // Controls\PlannedProjectsPanel.xaml line 77
                        this.obj7 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoryItemForPlanControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(item), 1 << phase);
            }

            public void Recycle()
            {
            }

            // IPlannedProjectsPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | (1 << 0))) != 0)
                    {
                        this.Update_Category(obj.Category, phase);
                        this.Update_SubcategoryCount(obj.SubcategoryCount, phase);
                        this.Update_TaskCount(obj.TaskCount, phase);
                    }
                }
            }
            private void Update_Category(global::HddtodoUI.BackendModels.TaskCategory obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\PlannedProjectsPanel.xaml line 77
                    if (!isobj7CategoryDisabled)
                    {
                        if ((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj, null);
                        }
                    }
                }
            }
            private void Update_SubcategoryCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\PlannedProjectsPanel.xaml line 77
                    if (!isobj7CategoryCountDisabled)
                    {
                        if ((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }
            private void Update_TaskCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\PlannedProjectsPanel.xaml line 77
                    if (!isobj7TaskCountDisabled)
                    {
                        if ((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount((this.obj7.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class PlannedProjectsPanel_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            IPlannedProjectsPanel_Bindings
        {
            private global::HddtodoUI.Controls.PlannedProjectsPanel dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Data.CollectionViewSource obj3;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj3SourceDisabled = false;

            private PlannedProjectsPanel_obj1_BindingsTracking bindingsTracking;

            public PlannedProjectsPanel_obj1_Bindings()
            {
                this.bindingsTracking = new PlannedProjectsPanel_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 26 && columnNumber == 63)
                {
                    isobj3SourceDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 3: // Controls\PlannedProjectsPanel.xaml line 26
                        this.obj3 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Data.CollectionViewSource>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // IPlannedProjectsPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.PlannedProjectsPanel>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.PlannedProjectsPanel obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_GroupedPlannedCategories(obj.GroupedPlannedCategories, phase);
                    }
                }
            }
            private void Update_GroupedPlannedCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_GroupedPlannedCategories(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\PlannedProjectsPanel.xaml line 26
                    if (!isobj3SourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Data_CollectionViewSource_Source(this.obj3, obj, null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class PlannedProjectsPanel_obj1_BindingsTracking
            {
                private global::System.WeakReference<PlannedProjectsPanel_obj1_Bindings> weakRefToBindingObj; 

                public PlannedProjectsPanel_obj1_BindingsTracking(PlannedProjectsPanel_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<PlannedProjectsPanel_obj1_Bindings>(obj);
                }

                public PlannedProjectsPanel_obj1_Bindings TryGetBindingObject()
                {
                    PlannedProjectsPanel_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_GroupedPlannedCategories(null);
                }

                public void PropertyChanged_GroupedPlannedCategories(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    PlannedProjectsPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_GroupedPlannedCategories(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    PlannedProjectsPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>> cache_GroupedPlannedCategories = null;
                public void UpdateChildListeners_GroupedPlannedCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Controls.Grouping<global::System.String, global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount>> obj)
                {
                    if (obj != cache_GroupedPlannedCategories)
                    {
                        if (cache_GroupedPlannedCategories != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_GroupedPlannedCategories).PropertyChanged -= PropertyChanged_GroupedPlannedCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_GroupedPlannedCategories).CollectionChanged -= CollectionChanged_GroupedPlannedCategories;
                            cache_GroupedPlannedCategories = null;
                        }
                        if (obj != null)
                        {
                            cache_GroupedPlannedCategories = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_GroupedPlannedCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_GroupedPlannedCategories;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\PlannedProjectsPanel.xaml line 23
                {
                    this.mainGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 3: // Controls\PlannedProjectsPanel.xaml line 26
                {
                    this.PlannedProjectsCVS = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Data.CollectionViewSource>(target);
                }
                break;
            case 4: // Controls\PlannedProjectsPanel.xaml line 46
                {
                    this.ProjectsListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 8: // Controls\PlannedProjectsPanel.xaml line 40
                {
                    this.HeaderText = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\PlannedProjectsPanel.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    PlannedProjectsPanel_obj1_Bindings bindings = new PlannedProjectsPanel_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            case 7: // Controls\PlannedProjectsPanel.xaml line 77
                {                    
                    global::HddtodoUI.Controls.TaskCategoryItemForPlanControl element7 = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)target;
                    PlannedProjectsPanel_obj7_Bindings bindings = new PlannedProjectsPanel_obj7_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element7.DataContext);
                    element7.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element7, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element7, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

