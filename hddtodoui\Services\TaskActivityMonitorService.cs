using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Utilities;
using System.Timers;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Dispatching;

namespace HddtodoUI.Services
{
    /// <summary>
    /// 监控其他客户端上的活动任务服务
    /// </summary>
    public class TaskActivityMonitorService
    {
        private static TaskActivityMonitorService _instance;
        private readonly Timer _monitorTimer;
        private readonly DispatcherQueue _dispatcherQueue;
        private bool _isRunning = false;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(2); // 每2分钟检查一次
      

        /// <summary>
        /// 获取TaskActivityMonitorService的单例实例
        /// </summary>
        public static TaskActivityMonitorService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new TaskActivityMonitorService();
                }
                return _instance;
            }
        }

        private TaskActivityMonitorService()
        {
            _dispatcherQueue = DispatcherQueue.GetForCurrentThread() ?? DispatcherQueue.GetForCurrentThread();
            _monitorTimer = new Timer(_checkInterval.TotalMilliseconds);
            _monitorTimer.Elapsed += MonitorTimer_Elapsed;
        }

        /// <summary>
        /// 启动任务活动监控服务
        /// </summary>
        public void Start()
        {
            if (_isRunning) return;

            _isRunning = true;
            _monitorTimer.Start();
            LogService.Instance.Info("任务活动监控服务已启动");
            
            // 立即执行一次检查
            _ = CheckForActiveTasksAsync();
        }

        /// <summary>
        /// 停止任务活动监控服务
        /// </summary>
        public void Stop()
        {
            if (!_isRunning) return;

            _monitorTimer.Stop();
            _isRunning = false;
          
            LogService.Instance.Info("任务活动监控服务已停止");
        }

        private async void MonitorTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                await CheckForActiveTasksAsync();
            }
            catch (Exception ex)
            {
                LogService.Instance.Error($"检查活动任务时出错: {ex.Message}", ex);
            }
        }

        private async Task CheckForActiveTasksAsync()
        {
            try
            {
                var userId = UserInfoHolder.getUserId();
                var clientId = ClientInfoProvider.ClientId;
                
                // 从服务器获取其他客户端的活动任务
                var activeTasks = await StoreFactoryHolder.getTaskActivityStore()
                    .GetActiveTasksAsync(userId.ToString(), clientId);

                foreach (var activeTask in activeTasks)
                {
                    
                    var task = StoreFactoryHolder.getTaskStore().getTaskById(activeTask.TaskId, UserInfoHolder.getUserId());
                
                    
                    _dispatcherQueue.TryEnqueue(() =>
                    {
                        NotificationService.Instance.ShowNotification(
                            $"任务 '{task.Title}' 正在其他设备上执行",
                            "任务正在其他设备上执行",
                            duration: 5000);
                    });

                    LogService.Instance.Info($"检测到其他设备上正在执行的任务: {activeTask.TaskId}: {task.Title}");
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Error($"检查活动任务时发生错误: {ex.Message}", ex);
            }
        }
    }
}
