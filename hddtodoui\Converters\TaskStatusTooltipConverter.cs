using System;
using HddtodoUI.Models;
using Microsoft.UI.Xaml.Data;

namespace HddtodoUI.Converters
{
    public class TaskStatusTooltipConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is TaskStatus status)
            {
                return status switch
                {
                    TaskStatus.NotStarted => "开始任务",
                    TaskStatus.InProgress => "暂停任务",
                    TaskStatus.Paused => "继续任务",
                    TaskStatus.Completed => "任务已完成",
                    _ => "开始任务"
                };
            }
            return "开始任务";
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
