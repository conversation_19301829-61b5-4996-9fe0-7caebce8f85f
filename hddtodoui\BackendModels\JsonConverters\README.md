# DateTime JSON 转换器使用说明

## 概述

项目采用统一的DateTime格式管理架构，支持灵活配置和国际化。所有DateTime格式定义集中在 `DateTimeFormats` 类中，实现了单点修改、全局生效。

## 核心架构

### 统一格式管理
- **DateTimeFormats.cs**: 定义所有格式常量和选择逻辑
- **DateTimeFormatConfiguration.cs**: 支持动态配置和环境变量
- **转换器类**: 自动使用统一格式，无需修改现有代码

### 当前格式

**ISO 8601格式**: `yyyy-MM-dd'T'HH:mm:ss.fffzzz` (国际化标准)

示例：
- `2025-07-02T09:39:00.123+08:00` (当前使用格式，东八区)
- `2024-12-25T15:30:45.000Z` (UTC时间格式)

### 国际化格式 (推荐)

**ISO 8601格式**: `yyyy-MM-dd'T'HH:mm:ss.fffzzz`

示例：
- `2025-07-02T09:39:00.123+08:00` (带时区的完整格式)
- `2025-07-02T01:39:00.123Z` (UTC格式)

## 转换器类

### 1. DateTimeJsonConverter
用于非空DateTime属性的JSON转换。

### 2. NullableDateTimeJsonConverter  
用于可空DateTime?属性的JSON转换。

### 3. JsonSerializerOptionsProvider
提供预配置的JsonSerializerOptions，包含统一的DateTime转换器。

## 使用方法

### 在实体类中使用

```csharp
public class MyEntity
{
    [JsonConverter(typeof(DateTimeJsonConverter))]
    public DateTime CreatedTime { get; set; }
    
    [JsonConverter(typeof(NullableDateTimeJsonConverter))]
    public DateTime? UpdatedTime { get; set; }
}
```

### 在序列化/反序列化时使用

```csharp
// 使用统一的JsonSerializerOptions
var json = JsonSerializer.Serialize(entity, JsonSerializerOptionsProvider.DefaultOptions);
var entity = JsonSerializer.Deserialize<MyEntity>(json, JsonSerializerOptionsProvider.DefaultOptions);
```

### 使用工具类转换

```csharp
// 转换DateTime为字符串
string dateString = JsonDateTimeStringConverter.Convert(DateTime.Now);
string nullableDateString = JsonDateTimeStringConverter.Convert((DateTime?)null);
```

## 格式切换

### 方法1: 修改常量 (推荐)
```csharp
// 在 DateTimeFormats.cs 中修改 Current 常量
public const string Current = "yyyy-MM-dd'T'HH:mm:ss.fffzzz"; // 切换到国际化格式
```

### 方法2: 环境变量
```bash
set HDDTODO_DATETIME_FORMAT=yyyy-MM-dd'T'HH:mm:ss.fffzzz
```

### 方法3: 临时覆盖 (测试用)
```csharp
DateTimeFormatConfiguration.SetFormatOverride("yyyy-MM-dd'T'HH:mm:ss.fffzzz");
```

## 兼容性

转换器支持向后兼容，可以解析以下格式：
- `yyyy-MM-dd'T'HH:mm:ss.fffzzz` (当前ISO 8601格式)
- `yyyy-MM-dd'T'HH:mm:ss` (简化格式)
- `yyyy-MM-ddTHH:mm:ss.fffZ` (原UTC格式)
- `yyyy-MM-ddTHH:mm:ss.fff` (原本地格式)
- `yyyy-MM-ddTHH:mm:ss`
- `yyyy-MM-dd`

## 使用指南

### 基本使用
现有代码无需修改，自动使用统一格式：
```csharp
var task = new TTask
{
    TaskCreatTime = DateTime.Now  // 自动使用当前配置的格式
};
```

### 场景化使用
```csharp
// 根据场景选择格式
var format = DateTimeFormats.GetFormatByScenario(DateTimeScenario.International);
var dateString = DateTime.Now.ToString(format);
```

## 注意事项

1. **统一管理**: 所有格式定义集中在 `DateTimeFormats` 类中，避免硬编码。

2. **单点修改**: 只需修改 `DateTimeFormats.Current` 即可全局生效。

3. **国际化支持**: 推荐使用ISO 8601格式进行国际化。

4. **配置灵活**: 支持环境变量、配置文件动态切换格式。

## 已更新的实体类

以下实体类的DateTime属性已经添加了统一的JsonConverter：

- `TTask.cs`
- `TaskReminder.cs`
- `TaskTimeLog.cs`
- `TaskRestart.cs`
- `ActiveTask.cs`

## 规范

遵循项目中的JSON转换规范注释：
1. 所有共享枚举/日期使用 JsonPropertyName 或 JsonConverters 目录统一实现
2. 字段名必须与后端 JSON 字段完全一致，包括大小写/拼写
3. 日期统一使用 yyyy-MM-dd'T'HH:mm:ss 格式；使用统一的JsonConverter
4. 若需特殊序列化逻辑，仅在此文件或 JsonConverters 中写一次
5. 其它文件只引用现有 Converter/注解，禁止重复实现
