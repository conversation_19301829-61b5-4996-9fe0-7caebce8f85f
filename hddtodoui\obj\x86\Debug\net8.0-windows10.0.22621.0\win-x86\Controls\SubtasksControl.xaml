﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.SubtasksControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:models="using:HddtodoUI.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    d:DesignHeight="300"
    d:DesignWidth="400">

    <UserControl.Resources>
        <Style x:Key="CustomSubTaskTextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="35" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="HorizontalAlignment" Value="Stretch" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Subtasks Header -->
        <TextBlock Grid.Row="0"
                   Text="子任务"
                   FontWeight="SemiBold"
                   Margin="0,0,0,8" />

        <!-- Subtasks ListView with Drag and Drop -->
        <ListView x:ConnectionId='2' Grid.Row="1" 
                  x:Name="SubtasksListView"
                  Background="Transparent"
                  CanDragItems="True"
                  CanReorderItems="True"
                  AllowDrop="True"
                  SelectionMode="None">
            <ListView.Header>
                <Grid Padding="12,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBox x:ConnectionId='3' Grid.Column="0"
                             x:Name="NewSubtaskTextBox"
                             PlaceholderText="添加子任务..."
                                                                 
                             Style="{StaticResource CustomSubTaskTextBoxStyle}"/>

                    <Button x:ConnectionId='4' Grid.Column="1"
                            x:Name="AddSubtaskButton"
                            Content="添加子任务"
                            Style="{StaticResource SecondaryButtonStyle}"
                            Margin="8,0,0,0"
                                                           
                            Visibility="Collapsed"/>
                </Grid>
            </ListView.Header>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Margin" Value="0,0" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate                                       >
                    <Border x:ConnectionId='6' Name="TaskItemBorder" Padding="15,0,10,0">
                        <local:TaskItemControl x:ConnectionId='7' Name="TaskItemControlItem"                  />
                    </Border>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </Grid>
</UserControl>

