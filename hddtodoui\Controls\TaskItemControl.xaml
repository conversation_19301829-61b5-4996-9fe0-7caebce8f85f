<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.TaskItemControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    mc:Ignorable="d"
    d:DesignHeight="60"
    d:DesignWidth="400">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
        <converters:TaskStatusTooltipConverter x:Key="TaskStatusTooltipConverter" />
        <converters:TaskStatusIconConverter x:Key="TaskStatusIconConverter" />
        <converters:TaskStatusColorConverter x:Key="TaskStatusColorConverter" />
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
    </UserControl.Resources>

    <Border Background="Transparent">
        <Grid Padding="3,5,0,5" x:Name="RootGrid" >
        <Grid.ContextFlyout>
            <MenuFlyout>
                <MenuFlyoutItem Text="编辑" Click="EditButton_Click">
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE70F;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
                <MenuFlyoutItem Text="复制任务标题" Click="CopyTaskTitleButton_Click">
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE8C8;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
                
                <MenuFlyoutSeparator />
                <MenuFlyoutSubItem Text="设置截止日期">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE823;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem Text="今天" Click="SetDueDateToday_Click" />
                    <MenuFlyoutItem Text="明天" Click="SetDueDateTomorrow_Click" />
                    <MenuFlyoutItem Text="3天后" Click="SetDueDate3Days_Click" />
                    <MenuFlyoutItem Text="无截止日期" Click="SetDueDateUndefined_Click" />
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="设置重要程度">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE734;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem Text="高" Click="SetPriorityHigh_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Gold" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="中" Click="SetPriorityMedium_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Black" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="低" Click="SetPriorityLow_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Gray" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="设置颜色">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE790;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem Text="粉色" Click="SetColorPink_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Pink" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="橙色" Click="SetColorOrange_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Orange" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="绿色" Click="SetColorGreen_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Green" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="蓝色" Click="SetColorBlue_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Blue" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="紫色" Click="SetColorPurple_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Purple" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="棕色" Click="SetColorBrown_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Brown" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="金色" Click="SetColorGold_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Gold" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="黑色" Click="SetColorBlack_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Black" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="橄榄色" Click="SetColorOlive_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Olive" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="蓝绿色" Click="SetColorTeal_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Teal" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="番茄色" Click="SetColorTomato_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Tomato" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="无颜色" Click="SetColorNone_Click">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE711;" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="调整任务顺序">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE8CB;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem Text="移到本类顶部" Click="SetMoveToTop">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE183;" Foreground="Red" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem Text="移到本类底部" Click="SetMoveToBotton">
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE118;" Foreground="Green" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSeparator />
                <MenuFlyoutItem Text="放入回收站" Click="DeleteButton_Click">
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE74D;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
            </MenuFlyout>
        </Grid.ContextFlyout>
        <Grid.ColumnDefinitions>
           
            <ColumnDefinition Width="30" />
           
            <ColumnDefinition Width="40" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="30" />
            <ColumnDefinition Width="10" />
        </Grid.ColumnDefinitions>

        
        <Border Grid.Column="4" Name="ColorIndicator" Width="4"></Border>
        <Border Grid.Column="3" Name="TaskIconBorder">
        </Border>
        <!-- 任务完成复选框 -->
        <CheckBox Grid.Column="0" x:Name="TaskCheckBox"
                                 VerticalAlignment="Center"
                                  />

        <!-- 任务开始按钮 -->
        <Button x:Name="StartTaskButton"
                Grid.Column="1"
                VerticalAlignment="Center"
                Click="StartTaskButton_Click"
                Background="Transparent"
                BorderThickness="0"
                Padding="0"
                Width="32"
                Height="32"
                ToolTipService.ToolTip="{x:Bind TaskVO.Status, Mode=OneWay, Converter={StaticResource TaskStatusTooltipConverter}}">
            <FontIcon Glyph="{x:Bind TaskVO.Status, Mode=OneWay, Converter={StaticResource TaskStatusIconConverter}}"
                      FontSize="16"
                      Foreground="{x:Bind TaskVO.Status, Mode=OneWay, Converter={StaticResource TaskStatusColorConverter}}" />
        </Button>

        <!-- 任务信息 -->
        <Grid Grid.Column="2" MinHeight="36">
            <StackPanel VerticalAlignment="Center" Margin="0,0,0,0"  >
                <TextBlock Name="TaskTitleTextBlock" TextWrapping="Wrap" 
                           Tapped="TaskTitleTextBlock_Tapped" 
                           Text="{x:Bind TaskVO.Title, Mode=OneWay}" 
                           Style="{StaticResource TitleTextStyle}">
                   
                </TextBlock>
                <StackPanel Orientation="Horizontal">
                    
                    <TextBlock x:Name="TaskDueDateTextBlock"  Tapped="TaskTitleTextBlock_Tapped"
                               Text="{x:Bind TaskVO.DueDate, Mode=OneWay, Converter={StaticResource DateTimeConverter}}"
                               Style="{StaticResource CaptionTextStyle}" VerticalAlignment="Center"
                               Foreground="{x:Bind TaskVO.DueDate, Mode=OneWay, Converter={StaticResource DueDateColorConverter}}"
                                />
                 
                    <TextBlock Name="SubTaskBadge" Text="{x:Bind TaskVO.GetDirectSubTaskInfo(), Mode=OneWay}" Foreground="Gray" FontSize="11" Margin="10,0,0,0" VerticalAlignment="Center"></TextBlock>
                    <!-- <InfoBadge Name="SubTaskBadge" Value="{x:Bind TaskVO.DirectSubTaskCount, Mode=OneWay}" Visibility="Collapsed" Margin="10,0,0,0"></InfoBadge> -->
                </StackPanel>
              
            </StackPanel>
        </Grid>
       
      

    </Grid>
    </Border>
</UserControl>