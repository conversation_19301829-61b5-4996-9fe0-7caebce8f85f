# DateTime 国际化和统一管理指南

## 概述

本指南介绍如何在HddtodoUI应用中实现DateTime格式的国际化和统一管理。

## 统一格式管理

### 1. 核心类介绍

#### DateTimeFormats.cs
- 定义所有DateTime格式常量
- 提供格式选择方法
- 管理向后兼容性

#### DateTimeFormatConfiguration.cs
- 支持动态配置格式
- 环境变量和配置文件支持
- 格式验证功能

### 2. 当前架构优势

✅ **单点修改**: 只需修改 `DateTimeFormats.Current` 常量即可全局生效
✅ **配置灵活**: 支持环境变量、配置文件动态切换
✅ **向后兼容**: 自动支持解析旧格式
✅ **类型安全**: 编译时检查，避免字符串错误

## 国际化建议

### 推荐格式选择

#### 1. 国际化应用 (推荐)
```csharp
// ISO 8601 标准格式，包含时区信息
DateTimeFormats.ISO8601WithTimeZone
// 输出: 2025-07-02T09:39:00.123+08:00
```

#### 2. 简化国际化格式
```csharp
// ISO 8601 简化格式，不含毫秒但有时区
DateTimeFormats.ISO8601Simple  
// 输出: 2025-07-02T09:39:00+08:00
```

#### 3. UTC标准格式
```csharp
// UTC时间，避免时区混淆
DateTimeFormats.ISO8601UTC
// 输出: 2025-07-02T01:39:00.123Z
```

### 格式切换方法

#### 方法1: 修改常量 (推荐)
```csharp
// 在 DateTimeFormats.cs 中修改
public const string Current = "yyyy-MM-dd'T'HH:mm:ss.fffzzz"; // 改为国际化格式
```

#### 方法2: 环境变量
```bash
# 设置环境变量
set HDDTODO_DATETIME_FORMAT=yyyy-MM-dd'T'HH:mm:ss.fffzzz
```

#### 方法3: 配置文件
```xml
<!-- app.config -->
<appSettings>
    <add key="DateTimeFormat" value="yyyy-MM-dd'T'HH:mm:ss.fffzzz" />
</appSettings>
```

#### 方法4: 代码覆盖 (测试用)
```csharp
// 临时覆盖格式
DateTimeFormatConfiguration.SetFormatOverride("yyyy-MM-dd'T'HH:mm:ss.fffzzz");
```

## 使用示例

### 基本使用
```csharp
// 所有现有代码无需修改，自动使用新格式
var task = new TTask 
{
    TaskCreatTime = DateTime.Now  // 自动使用统一格式序列化
};
```

### 场景化使用
```csharp
// 根据场景选择格式
var format = DateTimeFormats.GetFormatByScenario(DateTimeScenario.International);
var dateString = DateTime.Now.ToString(format);
```

### 动态切换
```csharp
// 检查当前格式
var currentFormat = DateTimeFormats.GetCurrentFormat();

// 获取国际化格式
var intlFormat = DateTimeFormats.GetInternationalFormat();

// 验证格式有效性
bool isValid = DateTimeFormatConfiguration.IsValidFormat("yyyy-MM-dd'T'HH:mm:ss.fffzzz");
```

## 迁移步骤

### 从当前格式迁移到国际化格式

1. **测试环境验证**
   ```csharp
   // 设置测试格式
   DateTimeFormatConfiguration.SetFormatOverride("yyyy-MM-dd'T'HH:mm:ss.fffzzz");
   // 运行测试，验证兼容性
   ```

2. **修改默认格式**
   ```csharp
   // 在 DateTimeFormats.cs 中修改
   public const string Current = "yyyy-MM-dd'T'HH:mm:ss.fffzzz";
   ```

3. **验证后台兼容性**
   - 确认后台服务支持新格式
   - 测试API调用是否正常

4. **部署和监控**
   - 逐步部署到生产环境
   - 监控错误日志

## 最佳实践

### 1. 格式选择原则
- **内部系统**: 使用简化格式 `yyyy-MM-dd'T'HH:mm:ss`
- **国际化应用**: 使用ISO 8601格式 `yyyy-MM-dd'T'HH:mm:ss.fffzzz`
- **API接口**: 根据对方系统要求选择
- **数据存储**: 建议使用UTC格式

### 2. 时区处理建议
```csharp
// 存储时转换为UTC
var utcTime = DateTime.Now.ToUniversalTime();

// 显示时转换为本地时间
var localTime = utcTime.ToLocalTime();
```

### 3. 测试建议
```csharp
[Test]
public void TestDateTimeFormat()
{
    var testDate = new DateTime(2025, 7, 2, 9, 39, 0);
    var json = JsonSerializer.Serialize(testDate, JsonSerializerOptionsProvider.ApiOptions);
    
    // 验证格式是否符合预期
    Assert.That(json, Contains.Substring("2025-07-02T09:39:00"));
}
```

## 常见问题

### Q: 如何确保所有地方都使用统一格式？
A: 通过 `DateTimeFormats` 类统一管理，避免硬编码格式字符串。

### Q: 如何处理时区问题？
A: 建议使用ISO 8601格式，包含时区信息，或统一使用UTC时间。

### Q: 如何测试格式兼容性？
A: 使用 `DateTimeFormatConfiguration.SetFormatOverride()` 临时切换格式进行测试。

### Q: 旧数据如何处理？
A: 转换器自动支持向后兼容，可以解析多种旧格式。
