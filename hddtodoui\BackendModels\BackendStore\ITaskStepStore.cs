using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.Threading.Tasks;


namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskStepStore
    {
        /// <summary>
        /// 获取任务的所有步骤
        /// </summary>
        List<TaskStep> GetTaskSteps(long taskId, long userId);

        /// <summary>
        /// 根据步骤ID获取任务步骤
        /// </summary>
        TaskStep getTaskStepById(long stepId, long userId);

        /// <summary>
        /// 保存任务步骤的更改
        /// </summary>
        void saveTaskStepChange(TaskStep taskStep, long userId);


        public Task<RepositionStepsResponse> RepositionStepsAsync(long taskId, long userId,
            List<RepositionStepItem> itemsToReposition);
    }

    public class RepositionStepItem
    {
        [JsonPropertyName("stepId")] public long StepId { get; set; }

        [JsonPropertyName("position")] public int Position { get; set; }
    }

    public class RepositionStepsRequest
    {
        [JsonPropertyName("items")] public List<RepositionStepItem> Items { get; set; }
    }

    public class RepositionStepsResponse
    {
        [JsonPropertyName("success")] public bool Success { get; set; }

        [JsonPropertyName("message")] public string Message { get; set; }
    }
}