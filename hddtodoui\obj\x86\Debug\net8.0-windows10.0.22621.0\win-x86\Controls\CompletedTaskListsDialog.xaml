﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.CompletedTaskListsDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    mc:Ignorable="d"
    Width="800"
    Height="600">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter"/>
    </UserControl.Resources>

    <Grid Padding="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 过滤区域 -->
            <RowDefinition Height="*"/> <!-- 任务列表区域 -->
            <RowDefinition Height="Auto"/> <!-- 分页区域 -->
            <RowDefinition Height="Auto"/> <!-- 关闭按钮 -->
        </Grid.RowDefinitions>

        <!-- 过滤区域 -->
        <Grid Grid.Row="0" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:ConnectionId='9' x:Name="FilterTextBox" 
                     PlaceholderText="输入关键字过滤任务列表" 
                     Grid.Column="0" 
                     Margin="0,0,8,0"
                                                    />
            
            <Button x:ConnectionId='10' x:Name="FilterButton" 
                    Content="过滤" 
                    Grid.Column="1"
                                              />
        </Grid>

        <!-- 任务列表区域 -->
        <ListView x:ConnectionId='2' x:Name="CompletedTaskListsView" 
                  Grid.Row="1" 
                  SelectionMode="None"
                  Margin="0,0,0,16">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{Binding Name}" 
                                       Style="{StaticResource BodyTextStyle}" 
                                       FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding CategoryCompleteTime, Converter={StaticResource DateTimeConverter}}" 
                                       Style="{StaticResource CaptionTextStyle}" 
                                       Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                       Margin="0,4,0,0"/>
                        </StackPanel>
                        
                        <Button x:ConnectionId='8' Grid.Column="2" 
                                Content="设为未完成" 
                                                                 
                                Tag="{Binding }"/>
                    </Grid>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- 分页区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,0,0,16">
            <Button x:ConnectionId='4' x:Name="PreviousPageButton" 
                    Content="上一页" 
                    Margin="0,0,8,0"
                                                    />
            <TextBlock x:ConnectionId='5' x:Name="PageInfoTextBlock" 
                       VerticalAlignment="Center" 
                       Margin="8,0"/>
            <Button x:ConnectionId='6' x:Name="NextPageButton" 
                    Content="下一页" 
                    Margin="8,0,0,0"
                                                />
        </StackPanel>

        <!-- 关闭按钮 -->
        <Button x:ConnectionId='3' Grid.Row="3" 
                Content="关闭" 
                HorizontalAlignment="Center"
                                         />
    </Grid>
</UserControl>

