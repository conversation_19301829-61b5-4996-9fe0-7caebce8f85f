using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Controls;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.UICoordinator;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Views
{
    public sealed partial class MainView : UserControl
    {
        //private const int PageSize = 10; // 每页显示的任务数

        private bool _isCompletedTasksVisible = false;
        
        public MainView()
        {
            this.InitializeComponent();
            this.Loaded += async (sender, args) =>
            {
                await InitializeData();
                // 异步刷新计划分类数量
               
            };
          
        }
        

        private ObservableCollection<TodoTaskViewObject> Tasks { get; set; }
        private ObservableCollection<TodoTaskViewObject> CompletedTasks { get; set; }

        // "计划中的分类（项目）" 面板实例
        // 采用方案A：直接引用右侧两个面板
        //private Controls.PlannedProjectsPanel _plannedProjectsPanel;

        private async Task InitializeData()
        {
            // 初始化任务集合
            Tasks = new ObservableCollection<TodoTaskViewObject>();
            CompletedTasks = new ObservableCollection<TodoTaskViewObject>();

            // 初始化系统类别
            var systemCategories = InitializeSystemCategoryUncompletedTaskList();

            // 初始化用户类别
            var userCategories = new List<TaskCategoryViewObject>();

            // 使用 ITaskCategoryStore API 获取分类列表
            var categoryStore = StoreFactoryHolder.getTaskCategoryStore();
            var categoriesWithCount = categoryStore.GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId());
            
            foreach (var categoryWithCount in categoriesWithCount)
            {
                userCategories.Add(TaskCategoryViewObject.GetFrom(categoryWithCount.Category, categoryWithCount.TaskCount, categoryWithCount.SubcategoryCount));
                LogService.Instance.Debug(categoryWithCount.Category.Name + " " + categoryWithCount.TaskCount + " " +
                                          categoryWithCount.Category.CategoryOrder);
            }

            // 添加回收站分类
            userCategories.Add(new TaskCategoryViewObject
            {
                Name = "回收站", 
                Key = SpecialTaskListConstants.Deleted, 
                TaskCount = SystemCategoryManager.getDeletedTasksCount(),
                IconName = "\uE74D",
                IsSystemCategory = true,
                HasChildren = false,
                IsChildrenLoaded = true,
                Children = new ObservableCollection<TaskCategoryViewObject>()
            });
            
            TaskCategoriesPanel.InitializeUserCategories(userCategories);

            // 初始化任务面板

            CurrentStatusHolder.getCurrentStatusHolder().setCurrentTaskList(null, SpecialTaskListConstants.Today);
            
            
            // 默认选择"今天到期的任务"类别
            var todayCategory = systemCategories.FirstOrDefault(c => c.Name == "今天到期的任务");
            if (todayCategory != null)
            {
                await TaskSystemCategorySelected(todayCategory);
            }
        }

        private TaskCategoryViewObject _currentTaskListViewObject;
        // TaskCategoriesPanel 事件处理程序
        private async void TaskCategoriesPanel_SystemCategorySelected(object sender, TaskCategoryViewObject category)
        {
            try
            {
                await TaskSystemCategorySelected(category);
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.Message,e);
            }
        }
        
        public async Task TaskSystemCategorySelected(TaskCategoryViewObject category)
        {
            if (!category.IsSystemCategory)
            {
                throw new ArgumentException("category is not a system category");
                
            }
            _currentTaskListViewObject = category;

            if (category.IsSystemCategory && category.Key == SpecialTaskListConstants.Planned)
            {
                TasksPanel.Visibility = Visibility.Collapsed;
                PlannedProjectsPanel.Visibility = Visibility.Visible;
                await PlannedProjectsPanel.LoadAsync();
                return;
            }
            else
            {
                TasksPanel.Visibility = Visibility.Visible;
                PlannedProjectsPanel.Visibility = Visibility.Collapsed;
            }

            // 其余系统分类继续使用 TasksPanel
            var uncTasks = new List<TodoTaskViewObject>();
            CurrentStatusHolder.getCurrentStatusHolder().setCurrentTaskList(null, category.Key);
            uncTasks.AddRange(SystemCategoryManager.GetSystemCategoryTasks(category));
            TasksPanel.SetCategoryTasks(category, uncTasks, uncTasks, true);
            TasksPanel.Visibility = Microsoft.UI.Xaml.Visibility.Visible;
            PlannedProjectsPanel.Visibility = Microsoft.UI.Xaml.Visibility.Collapsed;
        }

        private void TaskCategoriesPanel_OnCategoryTreeItemSelected(object sender, TaskCategoryViewObject category)
        {
           TaskUserCategorySelected(category);
        }

        public void TaskUserCategorySelected(TaskCategoryViewObject category)
        {
            _currentTaskListViewObject = category;
            // 切回任务面板
            TasksPanel.Visibility = Microsoft.UI.Xaml.Visibility.Visible;
            PlannedProjectsPanel.Visibility = Microsoft.UI.Xaml.Visibility.Collapsed;
            var uncTasks = new List<TodoTaskViewObject>();
            
            var selectedList = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(category.Key, UserInfoHolder.getUserId());
            CurrentStatusHolder.getCurrentStatusHolder().setCurrentTaskList(selectedList, null);

            var list = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(category.Key, UserInfoHolder.getUserId());
            StoreFactoryHolder.getTaskStore()
                .getAllUnCompleteTaskByBelongToTaskList(list, UserInfoHolder.getUserId())
                .ForEach(t => uncTasks.Add(TodoTaskViewObject.GetFrom(t, list)));
            
          
            TasksPanel.SetCategoryTasks(category, uncTasks, uncTasks, false);
        }
        

        public String GetUserName()
        {
            return UserInfoHolder.getUserInfo().Name+" 您好！";
        } 
        
        
        //---------------------------------------------------------------

        public void ReloadTaskPanelUncompletedTaskListView()
        {
            CurrentStatusHolder.getCurrentStatusHolder().getCurrentTaskList();
            if ( _currentTaskListViewObject != null )
                if ( _currentTaskListViewObject.IsSystemCategory )
                    TaskCategoriesPanel_SystemCategorySelected(null,_currentTaskListViewObject);
                else
                    TaskCategoriesPanel_OnCategoryTreeItemSelected(null,_currentTaskListViewObject);
        }

        

        public List<TaskCategoryViewObject> InitializeSystemCategoryUncompletedTaskList()
        {
            var systemCategories = SystemCategoryManager.makeSystemCategories();
            TaskCategoriesPanel.InitializeSystemCategories(systemCategories);
            return systemCategories.ToList();
        }

        

        private void StatisticsButton_Click(object sender, Microsoft.UI.Xaml.RoutedEventArgs e)
        {
            // 创建并显示任务时间统计对话框
            ShowTaskTimeLogStatisticsDialog();
        }

        private async void ShowTaskTimeLogStatisticsDialog()
        {
            // 创建内容对话框
            ContentDialog dialog = new ContentDialog
            {
                Title = "任务时间统计",
                //CloseButtonText = "关闭",
                // DefaultButton = ContentDialogButton.Close,
                DefaultButton = ContentDialogButton.None,
                Content = new Controls.TaskTimeLogStatisticsControl(),
                XamlRoot = this.XamlRoot
            };
            dialog.Resources["ContentDialogMaxWidth"] = 2000;
            dialog.Resources["ContentDialogMaxHeight"] = 1000;
            // 显示对话框
            await dialog.ShowAsync();
        }
        
        private double savedMinWidth = 0;
        private double columnWidth = 0;    

        private void SettingsButton_OnClick(object sender, RoutedEventArgs e)
        {
            // if (MainViewControl.ColumnDefinitions[0].MinWidth != 0)
            // {
            //     savedMinWidth = MainViewControl.ColumnDefinitions[0].MinWidth;
            //     columnWidth = MainViewControl.ColumnDefinitions[0].ActualWidth;
            //     
            //     MainViewControl.ColumnDefinitions[0].MinWidth = 0;
            //     MainViewControl.ColumnDefinitions[0].Width = new GridLength(0);
            // }
            // else
            // {
            //     MainViewControl.ColumnDefinitions[0].MinWidth = savedMinWidth;
            //     MainViewControl.ColumnDefinitions[0].Width = new GridLength(columnWidth);
            // }
            
            ShowSettingsDialog();
        }
        
        public void ToggleLeftSidePanel()
        {
            if (MainViewControl.ColumnDefinitions[0].MinWidth != 0)
            {
                savedMinWidth = MainViewControl.ColumnDefinitions[0].MinWidth;
                columnWidth = MainViewControl.ColumnDefinitions[0].ActualWidth;
                
                MainViewControl.ColumnDefinitions[0].MinWidth = 0;
                MainViewControl.ColumnDefinitions[0].Width = new GridLength(0);
            }
            else
            {
                MainViewControl.ColumnDefinitions[0].MinWidth = savedMinWidth;
                MainViewControl.ColumnDefinitions[0].Width = new GridLength(columnWidth);
            }
        }
        
        private async void ShowSettingsDialog()
        {
            // 创建内容对话框
            ContentDialog dialog = new ContentDialog
            {
                Title = "应用设置",
                CloseButtonText = "",
                DefaultButton = ContentDialogButton.None,
                Content = new Controls.SettingsDialog(),
                XamlRoot = this.XamlRoot
            };

            // 获取对话框内容
            var settingsDialog = dialog.Content as Controls.SettingsDialog;
            if (settingsDialog != null)
            {
                // 订阅事件
                settingsDialog.SettingsSaved += (s, e) =>
                {
                    // 关闭对话框
                    dialog.Hide();
                };

                settingsDialog.DialogCancelled += (s, e) =>
                {
                    // 关闭对话框
                    dialog.Hide();
                };
            }

            // 显示对话框
            await dialog.ShowAsync();
        }

        public async Task ReloadPlannedProjectsPanelAsync()
        {
            // 找到 PlannedProjectsPanel 实例并调用 LoadAsync
            if (this.FindName("PlannedProjectsPanel") is Controls.PlannedProjectsPanel panel)
            {
                await panel.LoadAsync();
            }
        }
      
    }
}