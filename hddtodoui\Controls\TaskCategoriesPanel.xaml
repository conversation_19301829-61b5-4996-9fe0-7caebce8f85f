<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.TaskCategoriesPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:models="using:HddtodoUI.Models"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter"/>
        <converters:DueDateColorConverter x:Key="DueDateColorConverter"/>
        <converters:InboxVisibilityConverter x:Key="InboxVisibilityConverter"/>
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
    </UserControl.Resources>

    <Grid Background="{ThemeResource SidebarBackgroundBrush}" Padding="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- System Lists -->
            <RowDefinition Height="Auto"/> <!-- Separator -->
            <RowDefinition Height="*"/> <!-- User Lists -->
            <RowDefinition Height="Auto"/> <!-- Separator -->
            <RowDefinition Height="Auto"/> <!-- Add List Button -->
        </Grid.RowDefinitions>

        <!-- System Lists -->
        <ListView x:Name="SystemTasksListView" SelectionMode="Single" SelectionChanged="TasksListView_SelectionChanged">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ListViewItem Style="{StaticResource TaskListItemStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="30"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <FontIcon Grid.Column="0" Glyph="{Binding IconName}" FontSize="18"></FontIcon>
                            <TextBlock Grid.Column="1" Text="{Binding Name}" Style="{StaticResource BodyTextStyle}" TextWrapping="Wrap" />
                            <TextBlock Grid.Column="2" Text="{Binding TaskCount}" Style="{StaticResource CaptionTextStyle}" Margin="12,0,0,0"/>
                        </Grid>
                    </ListViewItem>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <StackPanel Grid.Row="1" Margin="0,10,0,10">
            <FontIcon Glyph="&#xE70D;" FontSize="8" Name="TopHint" Visibility="Collapsed"></FontIcon>
        </StackPanel>

        <!-- User Lists -->
        <ScrollViewer Grid.Row="2" Name="UserListsScrollViewer" Loaded="FrameworkElement_OnLoaded" ViewChanged="ScrollViewer_OnViewChanged" VerticalScrollBarVisibility="Hidden">
            <TreeView x:Name="UserTasksTreeView" 
                      SelectionMode="Single" SelectionChanged="UserTasksTreeView_OnSelectionChanged"
                      ItemsSource="{x:Bind UserCategories}" Expanding="UserTasksTreeView_Expanding" Collapsed="UserTasksTreeView_Collapsed">
                <TreeView.ItemTemplate>
                    <DataTemplate x:DataType="models:TaskListViewObject">
                        <TreeViewItem IsExpanded="True">
                            <TreeViewItem.ContextFlyout>
                                <MenuFlyout>
                                    <MenuFlyoutItem Text="添加新任务" Icon="Add" Click="AddTaskMenuItem_Click" />
                                    <MenuFlyoutItem Text="编辑标题" Icon="Edit" Click="EditTitleMenuItem_Click"
                                                  Visibility="{Binding Name, Converter={StaticResource InboxVisibilityConverter}}" />
                                    <MenuFlyoutItem Text="完成该任务列表" Icon="Delete" Click="CompleteThisListMenuItem_Click"
                                                  Visibility="{Binding Name, Converter={StaticResource InboxVisibilityConverter}}" />
                                </MenuFlyout>
                            </TreeViewItem.ContextFlyout>
                            <Grid MinHeight="25" >
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Text="{x:Bind Name}" TextWrapping="Wrap"
                                                  Style="{StaticResource BodyTextStyle}"/>
                                        <TextBlock Grid.Column="1"
                                                  Text="{x:Bind TaskCount}"
                                                  Style="{StaticResource CaptionTextStyle}"
                                                  Margin="12,0,0,0" Padding="0,0,5,0"/>
                                    </Grid>
                                    <TextBlock Text="{x:Bind DueDate, Converter={StaticResource DateTimeConverter}}"
                                              Style="{StaticResource CaptionTextStyle}" 
                                              Visibility="{x:Bind DueDate, Converter={StaticResource HasDateVisibilityConverter}}"
                                              Foreground="{x:Bind DueDate, Converter={StaticResource DueDateColorConverter}}" 
                                              FontSize="11" Margin="0,2,0,4"/>
                                </StackPanel>
                            </Grid>
                        </TreeViewItem>
                    </DataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </ScrollViewer>

        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <FontIcon Glyph="&#xE70D;" FontSize="8" Name="BottomHint" Visibility="Collapsed"></FontIcon>
        </StackPanel>

        <!-- Add List Button -->
        <Grid Grid.Row="4" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Content="+ 列表"
                    Style="{StaticResource SecondaryButtonStyle}"
                    HorizontalAlignment="Center"
                    Click="AddListButton_Click"/>
            
            <Button Grid.Column="1"
                    x:Name="ManageCompletedListsButton"
                    ToolTipService.ToolTip="管理已完成的任务列表"
                    Click="ManageCompletedListsButton_Click"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Margin="8,0,0,0">
                <FontIcon Glyph="&#xE930;" FontSize="16"/>
            </Button>
        </Grid>

        <!-- Add List Dialog -->
        <ContentDialog x:Name="AddListDialog"
                      Title="添加新列表"
                      PrimaryButtonText="确定"
                      SecondaryButtonText="取消"
                      DefaultButton="Primary"
                      IsPrimaryButtonEnabled="{x:Bind IsAddListNameValid, Mode=OneWay}"
                      PrimaryButtonClick="AddListDialog_PrimaryButtonClick"
                      CloseButtonClick="AddListDialog_CloseButtonClick">
            <StackPanel Spacing="16">
                <TextBox x:Name="ListNameTextBox"
                        Header="列表名称"
                        PlaceholderText="请输入列表名称"
                        TextChanged="ListNameTextBox_TextChanged"/>
                
                <ComboBox x:Name="ListPriorityComboBox"
                          Header="优先级"
                          PlaceholderText="选择优先级"
                          SelectedIndex="1">
                    <ComboBoxItem Content="低"/>
                    <ComboBoxItem Content="中"/>
                    <ComboBoxItem Content="高"/>
                </ComboBox>
                
                <CalendarDatePicker x:Name="ListDueDatePicker"
                                  Header="到期时间"
                                  PlaceholderText="选择到期时间"/>
            </StackPanel>
        </ContentDialog>
    </Grid>
</UserControl>