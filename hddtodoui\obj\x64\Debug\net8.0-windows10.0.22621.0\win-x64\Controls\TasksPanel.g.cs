﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TasksPanel.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "3409C6B3317DE2D2073F0209E4D07A9D9DA2E8C62CE385EB946440A4367BB2BF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TasksPanel : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(global::Microsoft.UI.Xaml.Controls.ItemsControl obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.ItemsSource = value;
            }
            public static void Set_HddtodoUI_Controls_TaskItemControl_TaskVO(global::HddtodoUI.Controls.TaskItemControl obj, global::HddtodoUI.Models.TodoTaskViewObject value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::HddtodoUI.Models.TodoTaskViewObject) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::HddtodoUI.Models.TodoTaskViewObject), targetNullValue);
                }
                obj.TaskVO = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::HddtodoUI.BackendModels.TaskCategory value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::HddtodoUI.BackendModels.TaskCategory) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::HddtodoUI.BackendModels.TaskCategory), targetNullValue);
                }
                obj.Category = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.CategoryCount = value;
            }
            public static void Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount(global::HddtodoUI.Controls.TaskCategoryItemForPlanControl obj, global::System.Int32 value)
            {
                obj.TaskCount = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj3_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Models.TaskListItem dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj3;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj3TaskVODisabled = false;

            private TasksPanel_obj3_BindingsTracking bindingsTracking;

            public TasksPanel_obj3_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj3_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 31 && columnNumber == 44)
                {
                    isobj3TaskVODisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 3: // Controls\TasksPanel.xaml line 31
                        this.obj3 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskItemControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj3.Target as global::HddtodoUI.Controls.TaskItemControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListItem>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListItem>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TaskListItem obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_Task(obj.Task, phase);
                    }
                }
            }
            private void Update_Task(global::HddtodoUI.Models.TodoTaskViewObject obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 31
                    if (!isobj3TaskVODisabled)
                    {
                        if ((this.obj3.Target as global::HddtodoUI.Controls.TaskItemControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskItemControl_TaskVO((this.obj3.Target as global::HddtodoUI.Controls.TaskItemControl), obj, null);
                        }
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj3_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj3_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj3_BindingsTracking(TasksPanel_obj3_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj3_Bindings>(obj);
                }

                public TasksPanel_obj3_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj3_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj5_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Models.TaskListItem dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj5;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj5CategoryDisabled = false;
            private static bool isobj5CategoryCountDisabled = false;
            private static bool isobj5TaskCountDisabled = false;

            private TasksPanel_obj5_BindingsTracking bindingsTracking;

            public TasksPanel_obj5_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj5_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 37 && columnNumber == 25)
                {
                    isobj5CategoryDisabled = true;
                }
                else if (lineNumber == 38 && columnNumber == 25)
                {
                    isobj5CategoryCountDisabled = true;
                }
                else if (lineNumber == 39 && columnNumber == 25)
                {
                    isobj5TaskCountDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 5: // Controls\TasksPanel.xaml line 36
                        this.obj5 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoryItemForPlanControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListItem>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListItem>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TaskListItem obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_SubCategory(obj.SubCategory, phase);
                    }
                }
            }
            private void Update_SubCategory(global::HddtodoUI.BackendModels.BackendStore.TaskCategoryWithCount obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_SubCategory_Category(obj.Category, phase);
                        this.Update_SubCategory_SubcategoryCount(obj.SubcategoryCount, phase);
                        this.Update_SubCategory_TaskCount(obj.TaskCount, phase);
                    }
                }
            }
            private void Update_SubCategory_Category(global::HddtodoUI.BackendModels.TaskCategory obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 36
                    if (!isobj5CategoryDisabled)
                    {
                        if ((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_Category((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj, null);
                        }
                    }
                }
            }
            private void Update_SubCategory_SubcategoryCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 36
                    if (!isobj5CategoryCountDisabled)
                    {
                        if ((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_CategoryCount((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }
            private void Update_SubCategory_TaskCount(global::System.Int32 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 36
                    if (!isobj5TaskCountDisabled)
                    {
                        if ((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskCategoryItemForPlanControl_TaskCount((this.obj5.Target as global::HddtodoUI.Controls.TaskCategoryItemForPlanControl), obj);
                        }
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj5_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj5_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj5_BindingsTracking(TasksPanel_obj5_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj5_Bindings>(obj);
                }

                public TasksPanel_obj5_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj5_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj29_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Models.TodoTaskViewObject dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj29;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj29TaskVODisabled = false;

            private TasksPanel_obj29_BindingsTracking bindingsTracking;

            public TasksPanel_obj29_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj29_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 175 && columnNumber == 72)
                {
                    isobj29TaskVODisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 29: // Controls\TasksPanel.xaml line 175
                        this.obj29 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskItemControl>(target));
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj29.Target as global::HddtodoUI.Controls.TaskItemControl);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(item), 1 << phase);
            }

            public void Recycle()
            {
                this.bindingsTracking.ReleaseAllListeners();
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TodoTaskViewObject>(newDataRoot);
                    return true;
                }
                return false;
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TodoTaskViewObject obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 175
                    if (!isobj29TaskVODisabled)
                    {
                        if ((this.obj29.Target as global::HddtodoUI.Controls.TaskItemControl) != null)
                        {
                            XamlBindingSetters.Set_HddtodoUI_Controls_TaskItemControl_TaskVO((this.obj29.Target as global::HddtodoUI.Controls.TaskItemControl), obj, null);
                        }
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj29_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj29_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj29_BindingsTracking(TasksPanel_obj29_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj29_Bindings>(obj);
                }

                public TasksPanel_obj29_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj29_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                }

            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TasksPanel_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITasksPanel_Bindings
        {
            private global::HddtodoUI.Controls.TasksPanel dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.ListView obj17;
            private global::Microsoft.UI.Xaml.Controls.ListView obj26;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj17ItemsSourceDisabled = false;
            private static bool isobj26ItemsSourceDisabled = false;

            private TasksPanel_obj1_BindingsTracking bindingsTracking;

            public TasksPanel_obj1_Bindings()
            {
                this.bindingsTracking = new TasksPanel_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 249 && columnNumber == 44)
                {
                    isobj17ItemsSourceDisabled = true;
                }
                else if (lineNumber == 161 && columnNumber == 47)
                {
                    isobj26ItemsSourceDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 17: // Controls\TasksPanel.xaml line 244
                        this.obj17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                        break;
                    case 26: // Controls\TasksPanel.xaml line 156
                        this.obj26 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITasksPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TasksPanel>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TasksPanel obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_CompletedTaskCategories(obj.CompletedTaskCategories, phase);
                        this.Update_CompletedTasks(obj.CompletedTasks, phase);
                    }
                }
            }
            private void Update_CompletedTaskCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_CompletedTaskCategories(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 244
                    if (!isobj17ItemsSourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(this.obj17, obj, null);
                    }
                }
            }
            private void Update_CompletedTasks(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_CompletedTasks(obj);
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TasksPanel.xaml line 156
                    if (!isobj26ItemsSourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ItemsControl_ItemsSource(this.obj26, obj, null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TasksPanel_obj1_BindingsTracking
            {
                private global::System.WeakReference<TasksPanel_obj1_Bindings> weakRefToBindingObj; 

                public TasksPanel_obj1_BindingsTracking(TasksPanel_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TasksPanel_obj1_Bindings>(obj);
                }

                public TasksPanel_obj1_Bindings TryGetBindingObject()
                {
                    TasksPanel_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_CompletedTaskCategories(null);
                    UpdateChildListeners_CompletedTasks(null);
                }

                public void PropertyChanged_CompletedTaskCategories(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_CompletedTaskCategories(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> cache_CompletedTaskCategories = null;
                public void UpdateChildListeners_CompletedTaskCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskCategoryViewObject> obj)
                {
                    if (obj != cache_CompletedTaskCategories)
                    {
                        if (cache_CompletedTaskCategories != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_CompletedTaskCategories).PropertyChanged -= PropertyChanged_CompletedTaskCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_CompletedTaskCategories).CollectionChanged -= CollectionChanged_CompletedTaskCategories;
                            cache_CompletedTaskCategories = null;
                        }
                        if (obj != null)
                        {
                            cache_CompletedTaskCategories = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_CompletedTaskCategories;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_CompletedTaskCategories;
                        }
                    }
                }
                public void PropertyChanged_CompletedTasks(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                        }
                        else
                        {
                            switch (propName)
                            {
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void CollectionChanged_CompletedTasks(object sender, global::System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
                {
                    TasksPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj = sender as global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject>;
                    }
                }
                private global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> cache_CompletedTasks = null;
                public void UpdateChildListeners_CompletedTasks(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TodoTaskViewObject> obj)
                {
                    if (obj != cache_CompletedTasks)
                    {
                        if (cache_CompletedTasks != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)cache_CompletedTasks).PropertyChanged -= PropertyChanged_CompletedTasks;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)cache_CompletedTasks).CollectionChanged -= CollectionChanged_CompletedTasks;
                            cache_CompletedTasks = null;
                        }
                        if (obj != null)
                        {
                            cache_CompletedTasks = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_CompletedTasks;
                            ((global::System.Collections.Specialized.INotifyCollectionChanged)obj).CollectionChanged += CollectionChanged_CompletedTasks;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 6: // Controls\TasksPanel.xaml line 45
                {
                    this.mainGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 7: // Controls\TasksPanel.xaml line 70
                {
                    this.TaskInputTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.TaskInputTextBox).KeyDown += this.TaskInputTextBox_KeyDown;
                }
                break;
            case 8: // Controls\TasksPanel.xaml line 76
                {
                    this.TaskListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskListView).DragItemsStarting += this.TaskListView_DragItemsStarting;
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.TaskListView).DragItemsCompleted += this.TaskListView_DragItemsCompleted;
                }
                break;
            case 9: // Controls\TasksPanel.xaml line 113
                {
                    this.CompletedTasksButtonBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 10: // Controls\TasksPanel.xaml line 122
                {
                    this.CompletedTasksExpander = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Expander>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedTasksExpander).Expanding += this.CompletedTasksExpander_OnExpanding;
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedTasksExpander).Collapsed += this.CompletedTasksExpander_OnCollapsed;
                }
                break;
            case 11: // Controls\TasksPanel.xaml line 213
                {
                    this.CompletedCategoryExpander = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Expander>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedCategoryExpander).Expanding += this.CompletedCategoryExpander_OnExpanding;
                    ((global::Microsoft.UI.Xaml.Controls.Expander)this.CompletedCategoryExpander).Collapsed += this.CompletedCategoryExpander_OnCollapsed;
                }
                break;
            case 14: // Controls\TasksPanel.xaml line 296
                {
                    this.PreviousCategoryPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PreviousCategoryPageButton).Click += this.PreviousCategoryPage_Click;
                }
                break;
            case 15: // Controls\TasksPanel.xaml line 300
                {
                    this.CategoryPageInfoText = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 16: // Controls\TasksPanel.xaml line 303
                {
                    this.NextCategoryPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.NextCategoryPageButton).Click += this.NextCategoryPage_Click;
                }
                break;
            case 17: // Controls\TasksPanel.xaml line 244
                {
                    this.CompletedCategoryListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 20: // Controls\TasksPanel.xaml line 270
                {
                    global::Microsoft.UI.Xaml.Controls.Button element20 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element20).Click += this.SetIncompleteButton_Click;
                }
                break;
            case 23: // Controls\TasksPanel.xaml line 197
                {
                    this.PreviousPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.PreviousPageButton).Click += this.PreviousPage_Click;
                }
                break;
            case 24: // Controls\TasksPanel.xaml line 201
                {
                    this.PageInfoText = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 25: // Controls\TasksPanel.xaml line 204
                {
                    this.NextPageButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.NextPageButton).Click += this.NextPage_Click;
                }
                break;
            case 26: // Controls\TasksPanel.xaml line 156
                {
                    this.CompletedTasksListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                }
                break;
            case 31: // Controls\TasksPanel.xaml line 61
                {
                    this.CategoryTitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 32: // Controls\TasksPanel.xaml line 64
                {
                    this.ColorComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 33: // Controls\TasksPanel.xaml line 65
                {
                    this.SortButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TasksPanel.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TasksPanel_obj1_Bindings bindings = new TasksPanel_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            case 3: // Controls\TasksPanel.xaml line 31
                {                    
                    global::HddtodoUI.Controls.TaskItemControl element3 = (global::HddtodoUI.Controls.TaskItemControl)target;
                    TasksPanel_obj3_Bindings bindings = new TasksPanel_obj3_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element3.DataContext);
                    element3.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element3, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element3, bindings);
                }
                break;
            case 5: // Controls\TasksPanel.xaml line 36
                {                    
                    global::HddtodoUI.Controls.TaskCategoryItemForPlanControl element5 = (global::HddtodoUI.Controls.TaskCategoryItemForPlanControl)target;
                    TasksPanel_obj5_Bindings bindings = new TasksPanel_obj5_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element5.DataContext);
                    element5.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element5, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element5, bindings);
                }
                break;
            case 29: // Controls\TasksPanel.xaml line 175
                {                    
                    global::HddtodoUI.Controls.TaskItemControl element29 = (global::HddtodoUI.Controls.TaskItemControl)target;
                    TasksPanel_obj29_Bindings bindings = new TasksPanel_obj29_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element29.DataContext);
                    element29.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element29, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element29, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

