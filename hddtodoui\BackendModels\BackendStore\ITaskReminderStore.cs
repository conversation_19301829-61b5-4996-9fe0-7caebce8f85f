using System;
using System.Collections.Generic;
using HddtodoUI.Annotations;


namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskReminderStore
    {
        /// <summary>
        /// Create a new reminder
        /// </summary>
        TaskReminder CreateReminder(long userId, TaskReminder reminder);

        /// <summary>
        /// Update an existing reminder
        /// </summary>
        bool UpdateReminder(long userId, long reminderId, TaskReminder reminder);

        /// <summary>
        /// Delete a reminder
        /// </summary>
        bool DeleteReminder(long userId, long reminderId);

        /// <summary>
        /// Find reminder by task ID
        /// </summary>
        [CanBeNull] TaskReminder FindReminderByTaskId(long userId, long taskId);

        /// <summary>
        /// Get upcoming reminders for a user
        /// </summary>
        List<TaskReminder> GetUpcomingReminders(long userId);
    }
}
