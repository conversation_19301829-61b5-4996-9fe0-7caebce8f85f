<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.TasksPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:models="using:HddtodoUI.Models"
    mc:Ignorable="d" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
        <converters:TaskStatusTooltipConverter x:Key="TaskStatusTooltipConverter" />
        <converters:TaskStatusIconConverter x:Key="TaskStatusIconConverter" />
        <converters:TaskStatusColorConverter x:Key="TaskStatusColorConverter" />
        <converters:CategoryConverter x:Key="CategoryConverter" />
        
        <!-- 添加空集合到可见性转换器 -->
        <converters:EmptyCollectionToVisibilityConverter x:Key="EmptyCollectionToVisibilityConverter" />
    </UserControl.Resources>

    <Grid Name="mainGrid" Padding="32,24" Background="{ThemeResource WindowBackgroundBrush}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" MinWidth="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" /> <!-- List Header -->
            <RowDefinition Height="Auto" /> <!-- Task Input -->
            <RowDefinition Height="*" /> <!-- Task List -->
            <RowDefinition Height="Auto" /> <!-- Show Completed Tasks Button -->

        </Grid.RowDefinitions>

        <!-- List Header -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <TextBlock x:Name="CategoryTitleTextBlock" Text="收集箱" Style="{StaticResource SubHeaderTextStyle}" />

            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12" VerticalAlignment="Center">
                <ComboBox x:Name="ColorComboBox" PlaceholderText="选择颜色" MinWidth="120" />
                <Button x:Name="SortButton" Content="排序" Style="{StaticResource SecondaryButtonStyle}" />
            </StackPanel>
        </Grid>

        <!-- Task Input -->
        <TextBox x:Name="TaskInputTextBox" Grid.Row="1"
                 PlaceholderText="写下接下来要做得事情，按回车键保存"
                 Margin="0,24,0,16"
                 KeyDown="TaskInputTextBox_KeyDown" />

        <!-- Task List -->
        <ListView x:Name="TaskListView"
                  Grid.Row="2"
                  SelectionMode="None"
                  HorizontalAlignment="Stretch"
                  HorizontalContentAlignment="Stretch"
                
                  CanDragItems="True"
                  CanReorderItems="True"
                  AllowDrop="True"
                  DragItemsStarting="TaskListView_DragItemsStarting"
                  DragItemsCompleted="TaskListView_DragItemsCompleted">
            <ListView.GroupStyle>
                <GroupStyle>
                    <GroupStyle.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Key.CategoryName,Converter={StaticResource CategoryConverter}}"
                                       Style="{StaticResource CategoryHeaderTextStyle}"
                                       Margin="5,8,0,6" />
                        </DataTemplate>
                    </GroupStyle.HeaderTemplate>
                </GroupStyle>
            </ListView.GroupStyle>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="MinHeight" Value="0" />
                    <Setter Property="Margin" Value="0,4" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate x:DataType="models:TodoTaskViewObject">
                    <local:TaskItemControl TaskVO="{x:Bind Mode=OneWay}" />
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Show Completed Tasks Button -->
        <Border Name="CompletedTasksButtonBorder" Grid.Row="3" Background="Transparent">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <StackPanel Spacing="5">
                
                
                <Expander Grid.Column="0" x:Name="CompletedTasksExpander"  
                        
                          HorizontalAlignment="Stretch" 
                          HorizontalContentAlignment="Stretch" 
                          Padding="0" 
                          Expanding="CompletedTasksExpander_OnExpanding"
                          Collapsed="CompletedTasksExpander_OnCollapsed"
                          >
                
                    <Expander.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                <TextBlock x:Name="CompletedTasksExpanderTextBlock" Text="查看已完成任务" Style="{StaticResource CaptionTextStyle}" Opacity="0.5"/>
                            </StackPanel>
                        </DataTemplate>
                    </Expander.HeaderTemplate>
               
                    <Expander.Content >
                  
                        <!-- Completed Tasks Section -->
                        <Grid MaxHeight="400" >
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <ScrollViewer Grid.Row="0" Grid.Column="0" 
                                          VerticalScrollBarVisibility="Auto"
                                          HorizontalScrollBarVisibility="Disabled">
                                <Grid>
                                    <ListView x:Name="CompletedTasksListView" 
                                              HorizontalAlignment="Stretch"
                                              Padding="0"
                                              SelectionMode="None"
                                              Visibility="Collapsed"
                                              ItemsSource="{x:Bind CompletedTasks, Mode=OneWay}">
                                        <ListView.GroupStyle>
                                            <GroupStyle>
                                                <GroupStyle.HeaderTemplate>
                                                    <DataTemplate>
                                                        <TextBlock Text="{Binding Key}"
                                                                   Style="{StaticResource SubHeaderTextStyle}"
                                                                   Margin="0,16,0,8" />
                                                    </DataTemplate>
                                                </GroupStyle.HeaderTemplate>
                                            </GroupStyle>
                                        </ListView.GroupStyle>
                                        <ListView.ItemTemplate>
                                            <DataTemplate x:DataType="models:TodoTaskViewObject">
                                                <local:TaskItemControl TaskVO="{x:Bind Mode=OneWay}" />
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                    </ListView>
                                    <!-- 空数据提示 -->
                                    <TextBlock Text="没有已完成任务" 
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Margin="0,20"
                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                               FontSize="14"
                                               Visibility="{Binding ItemsSource, ElementName=CompletedTasksListView, Converter={StaticResource EmptyCollectionToVisibilityConverter}}" />
                                </Grid>
                            </ScrollViewer>

                            <!-- Pagination -->
                            <StackPanel Grid.Row="1"
                                        Orientation="Horizontal"
                                        HorizontalAlignment="Center"
                                        Margin="0,8,0,0"
                                        Spacing="8"
                                        Visibility="{Binding ElementName=CompletedTasksListView, Path=Visibility}">
                                <Button x:Name="PreviousPageButton"
                                        Content="上一页"
                                        Style="{StaticResource SecondaryButtonStyle}"
                                        Click="PreviousPage_Click" />
                                <TextBlock x:Name="PageInfoText"
                                           VerticalAlignment="Center"
                                           Style="{StaticResource BodyTextStyle}" />
                                <Button x:Name="NextPageButton"
                                        Content="下一页"
                                        Style="{StaticResource SecondaryButtonStyle}"
                                        Click="NextPage_Click" />
                            </StackPanel>
                        </Grid>
                   
                </Expander.Content>
            </Expander>
            <Expander Grid.Column="0" x:Name="CompletedCategoryExpander" 
                      HorizontalAlignment="Stretch" 
                      HorizontalContentAlignment="Stretch" 
                      Padding="0"
                      Expanding="CompletedCategoryExpander_OnExpanding"
                      Collapsed="CompletedCategoryExpander_OnCollapsed"
                      >
                
                <Expander.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                            <TextBlock x:Name="CompletedCategoryExpanderTextBlock" Text="查看已完成分类"  Style="{StaticResource CaptionTextStyle}" Opacity="0.5"/>
                        </StackPanel>
                    </DataTemplate>
                </Expander.HeaderTemplate>
                <Expander.Content>
                    <!-- Completed Categories Section -->
                    <Grid MaxHeight="400">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <ScrollViewer Grid.Row="0" Grid.Column="0"
                                      VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Disabled">
                            <Grid>
                                <ListView x:Name="CompletedCategoryListView"
                                           HorizontalAlignment="Stretch"
                                           Padding="0"
                                           SelectionMode="None"
                                           Visibility="Collapsed"
                                           ItemsSource="{x:Bind CompletedTaskCategories, Mode=OneWay}">
                                    <ListView.ItemTemplate>
                                        <DataTemplate x:DataType="models:TaskCategoryViewObject">
                                            
                                            <Grid Margin="0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                        
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="{Binding Name}" 
                                                               Style="{StaticResource BodyTextStyle}" 
                                                               FontWeight="SemiBold"/>
                                                    <TextBlock Text="{Binding CategoryCompleteTime, Converter={StaticResource DateTimeConverter}}" 
                                                               Style="{StaticResource CaptionTextStyle}" 
                                                               Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                                               Margin="0,4,0,0"/>
                                                </StackPanel>
                        
                                                <Button Grid.Column="2" 
                                                        Content="设为未完成" 
                                                        Click="SetIncompleteButton_Click"
                                                        Tag="{Binding }"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ListView.ItemTemplate>
                                </ListView>
                                <!-- 空数据提示 -->
                                <TextBlock Text="没有已完成分类" 
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Margin="0,20"
                                           Foreground="{ThemeResource TextFillColorSecondaryBrush}"
                                           FontSize="14"
                                           Visibility="{Binding ItemsSource, ElementName=CompletedCategoryListView, Converter={StaticResource EmptyCollectionToVisibilityConverter}}" />
                            </Grid>
                        </ScrollViewer>

                        <!-- Pagination for Categories -->
                        <StackPanel Grid.Row="1"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Center"
                                    Margin="0,8,0,0"
                                    Spacing="8"
                                    Visibility="{Binding ElementName=CompletedCategoryListView, Path=Visibility}">
                            <Button x:Name="PreviousCategoryPageButton"
                                    Content="上一页"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Click="PreviousCategoryPage_Click" />
                            <TextBlock x:Name="CategoryPageInfoText"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource BodyTextStyle}" />
                            <Button x:Name="NextCategoryPageButton"
                                    Content="下一页"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Click="NextCategoryPage_Click" />
                        </StackPanel>
                    </Grid>
                </Expander.Content>
            </Expander>

                </StackPanel>
            </Grid>
        </Border>


    </Grid>
</UserControl>