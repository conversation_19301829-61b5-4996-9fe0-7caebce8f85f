using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Data;
using System;
using System.Transactions;
using HddtodoUI.Models;

namespace HddtodoUI.Converters
{
    public class InboxVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            // 检查是否为系统类别
            if (value is bool isSystemCategory)
            {
                // 如果是系统类别，则隐藏菜单项
                if (isSystemCategory)
                {
                    return Visibility.Collapsed;
                }
                
                // 如果不是系统类别，则显示菜单项
                return Visibility.Visible;
            }
            
            // 如果传入的是 TaskCategoryViewObject 对象
            if (value is TaskCategoryViewObject category)
            {
             
                
                // 如果参数是 MenuFlyoutItem，可以根据菜单项类型和类别属性决定可见性
                if (parameter is string menuItemType)
                {
                    switch (menuItemType)
                    {
                        case "edit":
                        case "complete":
                        case "addSubCategory":
                            // 完成该分类菜单项的可见性逻辑
                            return IsUserCategory(category) ? Visibility.Visible : Visibility.Collapsed;
                        case "addTask":
                            // 添加新任务菜单项的可见性逻辑 - 所有类别都可以添加任务
                            return IsNotRecycledCategory(category) ? Visibility.Visible : Visibility.Collapsed;
                        case "clearRecycle":
                            // 清空回收站菜单项的可见性逻辑
                            return IsRecycledCategory(category) ? Visibility.Visible : Visibility.Collapsed;
                        default:
                            return Visibility.Visible;
                    }
                }
                
                // 默认情况下，如果不是系统类别，则显示菜单项
                return category.IsSystemCategory ? Visibility.Collapsed : Visibility.Visible;
            }
            
            // 向后兼容：如果列表名称是"收集箱"，则返回 Collapsed，否则返回 Visible
            return value is string name && name == "收集箱" ? Visibility.Collapsed : Visibility.Visible;
        }

        private bool IsUserCategory(TaskCategoryViewObject category)
        {
            return category.Name != "收集箱" && category.Name != "回收站";
        }
        
        private bool IsNotRecycledCategory(TaskCategoryViewObject category)
        {
            return !IsRecycledCategory(category);
        }
        
        private bool IsRecycledCategory(TaskCategoryViewObject category)
        {
            return category.Name == "回收站";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}