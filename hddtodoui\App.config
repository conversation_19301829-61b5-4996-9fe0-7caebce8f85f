﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
    </startup>
    <appSettings>
        <!--<add key ="baseurl" value ="https://hddtodo.tryhard.vip:9300/api" />--> 
        <!-- <add key ="baseurl" value ="http://************:9300/api" /> -->
        <!-- <add key ="baseurl" value ="https://todo.hddtodo.top:1543/api" /> -->
        <!-- <add key ="baseurl" value ="http://*************:9300/api" /> -->
        <add key ="baseurl" value ="http://localhost:9300/api" />
        <!-- <add key ="baseurl" value ="http://************:9300/api"/> -->
        <!-- <add key ="baseurl" value ="http://************:9300/api" /> -->
        <!-- <add key ="baseurl" value ="https://ts.r77908176.nyat.app:11480/api" /> -->
        <add key ="toolTipShowGap" value ="300" />
        <add key="username" value="hongdengdao" />
        <add key="password" value="123456" />
        <add key="IndicatorFormLeft" value="999" />
        <add key="IndicatorFormTop" value="1279" />
        <add key="IndicatorFormScreen" value="\\.\DISPLAY2" />
        <add key="IndicatorFormScreenBounds" value="0,0,2560,1440" />
        <add key="AutoHideMainForm" value="true" />
    </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
      </dependentAssembly>
        <dependentAssembly>
            <assemblyIdentity name="WinRT.Runtime" publicKeyToken="99ea127f02d97709" culture="neutral" />
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        </dependentAssembly>
    </assemblyBinding>
      
  </runtime>
</configuration>