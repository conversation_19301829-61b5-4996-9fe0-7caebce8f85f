﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HddtodoUI.TaskTomatoManager.TomatoStrategy;
using HddtodoUI.Utilities;


namespace HddtodoUI.TaskTomatoManager
{
    public class Tomato
    {

        private static ITomatoStrategy strategy = InitStrategy();
        
        private static ITomatoStrategy InitStrategy()
        {
            if ( ConfigSettingsUtils.getCountTimeStrategyConfig() )
                return new CountTimeStrategy();
            else                
                return new CountDownStrategy();
        }
        
        private static ITomatoStrategy GetStrategy()
        {
            return strategy;
        }
        
        public Tomato(int esTaskTimeSpan)
        {
            this.taskTimeSpan = esTaskTimeSpan;
            resetCountDown();
        }
        
        public int CountDown { get; set; }

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        public int PauseCount { get; set; }
        
        public int taskTimeSpan { get; set; }

        private bool _started = false;

        public bool isStarted()
        {
            return _started;
        }

        public void startTomato()
        {
            _started = true;
        }

        public void resetCountDown()
        {
            this.CountDown = GetStrategy().ResetCountDown(this);
        }
        
        public void endTomato()
        {
            this._started = false;
            resetCountDown();
        }
        
        public void pauseTomato()
        {
            _started = false;
        }
        
        public bool clockTicked()
        {
            return GetStrategy().ClockTicked(this);
        }
        
    }
}
