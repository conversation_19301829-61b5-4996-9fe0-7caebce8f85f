using System.Collections.ObjectModel;
using Microsoft.UI.Xaml;
using HddtodoUI.Models;

namespace HddtodoUI.ViewModels
{
    public class MainViewModel : DependencyObject
    {
        public ObservableCollection<TaskListViewObject> SystemLists { get; } = new ObservableCollection<TaskListViewObject>();
        public ObservableCollection<TaskListViewObject> UserLists { get; } = new ObservableCollection<TaskListViewObject>();

        public MainViewModel()
        {
            // 添加系统默认列表
            SystemLists.Add(new TaskListViewObject { Name = "今天到期的任务", TaskCount = 5, IconName = "Calendar", IsSystemList = true });
            SystemLists.Add(new TaskListViewObject { Name = "7天内到期的任务", TaskCount = 12, IconName = "CalendarWeek", IsSystemList = true });
            SystemLists.Add(new TaskListViewObject { Name = "待定任务", TaskCount = 3, IconName = "Clock", IsSystemList = true });

            // 添加用户列表
            UserLists.Add(new TaskListViewObject { Name = "收集箱", TaskCount = 8, IconName = "Inbox", IsSystemList = false });
            UserLists.Add(new TaskListViewObject { Name = "个人任务", TaskCount = 15, IconName = "Person", IsSystemList = false });
            UserLists.Add(new TaskListViewObject { Name = "工作", TaskCount = 23, IconName = "Work", IsSystemList = false });
            UserLists.Add(new TaskListViewObject { Name = "学习", TaskCount = 7, IconName = "Book", IsSystemList = false });
        }
    }
}
