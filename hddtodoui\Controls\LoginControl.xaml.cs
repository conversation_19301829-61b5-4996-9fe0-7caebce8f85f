using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Windows.System;
using Windows.Security.Credentials;
using CommunityToolkit.WinUI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;

namespace HddtodoUI.Controls
{
    public sealed partial class LoginControl : UserControl
    {
        private bool _isLoggingIn = false;
        private const string _resourceName = "HddtodoUICredentials";

        public LoginControl()
        {
            this.InitializeComponent();
            this.Loaded += LoginControl_Loaded;
        }

        public event EventHandler<UserInfo> LoginSucceeded;
        public event EventHandler ExitRequested;

        private void LoginControl_Loaded(object sender, RoutedEventArgs e)
        {
            LoadSavedCredentials();
            UsernameTextBox.Focus(FocusState.Keyboard);
        }

        private void LoadSavedCredentials()
        {
            try
            {
                var vault = new PasswordVault();
                var credentialList = vault.FindAllByResource(_resourceName);
                
                if (credentialList.Count > 0)
                {
                    // 获取第一个凭据
                    var credential = credentialList[0];
                    credential.RetrievePassword(); // 必须调用此方法才能获取密码
                    
                    // 填充用户名和密码
                    UsernameTextBox.Text = credential.UserName;
                    PasswordBox.Password = credential.Password;
                    
                    // 选中"记住密码"复选框
                    RememberPasswordCheckBox.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                // 如果没有保存的凭据，会抛出异常，这是正常的
                LogService.Instance.Debug($"No saved credentials found: {ex.Message}");
            }
        }

        private void SaveCredentials(string username, string password)
        {
            try
            {
                var vault = new PasswordVault();
                
                // 先清除之前保存的所有凭据
                try
                {
                    var oldCredentials = vault.FindAllByResource(_resourceName);
                    foreach (var cred in oldCredentials)
                    {
                        vault.Remove(cred);
                    }
                }
                catch { /* 忽略没有找到凭据的异常 */ }
                
                // 保存新凭据
                var credential = new PasswordCredential(_resourceName, username, password);
                vault.Add(credential);
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"Failed to save credentials: {ex.Message}");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                var vault = new PasswordVault();
                var credentialList = vault.FindAllByResource(_resourceName);
                
                foreach (var credential in credentialList)
                {
                    vault.Remove(credential);
                }
            }
            catch (Exception ex)
            {
                // 如果没有保存的凭据，会抛出异常，这是正常的
                LogService.Instance.Debug($"No saved credentials to clear: {ex.Message}");
            }
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            AttemptLogin();
        }

        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            ExitRequested?.Invoke(this, EventArgs.Empty);
        }

        private void UsernameTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                PasswordBox.Focus(FocusState.Keyboard);
                e.Handled = true;
            }
            else if (e.Key == VirtualKey.Escape)
            {
                ExitRequested?.Invoke(this, EventArgs.Empty);
                e.Handled = true;
            }
        }

        private void PasswordBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter)
            {
                AttemptLogin();
                e.Handled = true;
            }
            else if (e.Key == VirtualKey.Escape)
            {
                ExitRequested?.Invoke(this, EventArgs.Empty);
                e.Handled = true;
            }
        }

        private async void AttemptLogin()
        {
            if (_isLoggingIn)
                return;

            string username = UsernameTextBox.Text?.Trim();
            string password = PasswordBox.Password?.Trim();

            // 验证输入
            if (string.IsNullOrEmpty(username))
            {
                ShowError("请输入用户名");
                UsernameTextBox.Focus(FocusState.Keyboard);
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("请输入密码");
                PasswordBox.Focus(FocusState.Keyboard);
                return;
            }

            // 显示加载状态
            _isLoggingIn = true;
            SetLoadingState(true);

            try
            {
                // 在后台线程执行登录
                var userInfo = await Task.Run(() =>
                {
                    try
                    {
                        return StoreFactoryHolder.getUserStore().getUserInfoBy(username, password);
                    }
                    catch (Exception ex)
                    {
                        LogService.Instance.Debug($"Login error: {ex.Message}");
                        return null;
                    }
                });

                // 回到UI线程处理结果
                await this.DispatcherQueue.EnqueueAsync(() =>
                {
                    if (userInfo != null)
                    {
                        // 登录成功，处理"记住密码"
                        if (RememberPasswordCheckBox.IsChecked == true)
                        {
                            SaveCredentials(username, password);
                        }
                        else
                        {
                            // 如果用户取消了"记住密码"，清除之前保存的凭据
                            ClearSavedCredentials();
                        }

                        // 更新UserInfoHolder
                        UserInfoHolder.setUserInfo(userInfo);

                        // 触发登录成功事件
                        LoginSucceeded?.Invoke(this, userInfo);
                    }
                    else
                    {
                        // 登录失败
                        ShowError("用户名或密码错误");
                        PasswordBox.Password = string.Empty;
                        PasswordBox.Focus(FocusState.Keyboard);
                    }

                    SetLoadingState(false);
                    _isLoggingIn = false;
                });
            }
            catch (Exception ex)
            {
                // 处理异常
                ShowError($"登录时发生错误: {ex.Message}");
                SetLoadingState(false);
                _isLoggingIn = false;
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void SetLoadingState(bool isLoading)
        {
            LoginButton.IsEnabled = !isLoading;
            ExitButton.IsEnabled = !isLoading;
            UsernameTextBox.IsEnabled = !isLoading;
            PasswordBox.IsEnabled = !isLoading;
            RememberPasswordCheckBox.IsEnabled = !isLoading;
            LoadingProgressRing.IsActive = isLoading;
            LoadingProgressRing.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }
    }
}