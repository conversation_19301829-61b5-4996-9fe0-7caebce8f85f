﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup>
    <ActiveDebugProfile>HddtodoUI (Unpackaged)</ActiveDebugProfile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemGroup>
    <None Update="App.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="MainWindow.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Package.appxmanifest">
      <SubType>Designer</SubType>
    </None>
    <Page Update="Windows\IndicatorWindow.xaml">
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
</Project>