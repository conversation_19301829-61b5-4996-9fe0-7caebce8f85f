using System;
using System.Text.Json;
using System.Text.Json.Serialization;



namespace HddtodoUI.BackendModels.JsonConverters
{
    public class TaskListWithCountJsonConverter : JsonConverter<TaskListWithCount>
    {
        public override TaskListWithCount Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartArray)
            {
                throw new JsonException("Expected start of array");
            }

            reader.Read(); // Move to first element (TaskList object)
            
            var taskList = JsonSerializer.Deserialize<TaskList>(ref reader, options);
            
            reader.Read(); // Move to second element (count)
            var count = reader.GetInt64();
            
            reader.Read(); // Move to end of array

            return new TaskListWithCount
            {
                TaskList = taskList,
                UncompletedCount = count
            };
        }

        public override void Write(Utf8JsonWriter writer, TaskListWithCount value, JsonSerializerOptions options)
        {
            writer.WriteStartArray();
            JsonSerializer.Serialize(writer, value.TaskList, options);
            writer.WriteNumberValue(value.UncompletedCount);
            writer.WriteEndArray();
        }
    }
}
