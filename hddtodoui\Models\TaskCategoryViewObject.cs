using System;
using System.Collections.ObjectModel;
using HddtodoUI.BackendModels;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace HddtodoUI.Models
{
    public class TaskCategoryViewObject : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));

        public string Key { get; set; }
        private string _name;
        public string Name
        {
            get => _name;
            set { if (_name != value) { _name = value; OnPropertyChanged(); } }
        }
        private long _taskCount;
        public long TaskCount
        {
            get => _taskCount;
            set { if (_taskCount != value) { _taskCount = value; OnPropertyChanged(); } }
        }
        
        private long _subCategoryCount;
        public long SubCategoryCount
        {
            get => _subCategoryCount;
            set { if (_subCategoryCount != value) { _subCategoryCount = value; OnPropertyChanged(); } }
        }
        private string _iconName;
        public string IconName
        {
            get => _iconName;
            set { if (_iconName != value) { _iconName = value; OnPropertyChanged(); } }
        }
        private bool _isSystemCategory;
        public bool IsSystemCategory
        {
            get => _isSystemCategory;
            set { if (_isSystemCategory != value) { _isSystemCategory = value; OnPropertyChanged(); } }
        }
        private DateTime? _dueDate;
        public DateTime? DueDate
        {
            get => _dueDate;
            set { if (_dueDate != value) { _dueDate = value; OnPropertyChanged(); } }
        }
        private TaskPriority _priority;
        public TaskPriority Priority
        {
            get => _priority;
            set { if (_priority != value) { _priority = value; OnPropertyChanged(); } }
        }
        public string ParentCategoryKey { get; set; }
        private long _categoryOrder;
        public long CategoryOrder
        {
            get => _categoryOrder;
            set { if (_categoryOrder != value) { _categoryOrder = value; OnPropertyChanged(); } }
        }
        private DateTime? _categoryCompleteTime;
        public DateTime? CategoryCompleteTime
        {
            get => _categoryCompleteTime;
            set { if (_categoryCompleteTime != value) { _categoryCompleteTime = value; OnPropertyChanged(); } }
        }
        private bool _isDeleted;
        public bool IsDeleted
        {
            get => _isDeleted;
            set { if (_isDeleted != value) { _isDeleted = value; OnPropertyChanged(); } }
        }
        private bool _hasChildren;
        public bool HasChildren
        {
            get => _hasChildren;
            set
            {
                if (_hasChildren != value)
                {
                    _hasChildren = value;
                    // Notify property-specific change
                    OnPropertyChanged();
                    // Notify that entire object has updated for bindings that depend on the whole object
                    OnPropertyChanged(string.Empty);
                }
            }
        }
        public bool IsChildrenLoaded { get; set; }
        public ObservableCollection<TaskCategoryViewObject> Children { get; set; }
        
        public static TaskCategoryViewObject GetFrom(TaskCategory category)
        {
            var categoryView = new TaskCategoryViewObject
            {
                Name = category.Name,
                TaskCount = 0,
                IconName = "Folder",
                IsSystemCategory = false,
                DueDate = category.CategoryDueTime,
                Key = category.Key,
                ParentCategoryKey = category.ParentCategoryKey,
                CategoryOrder = category.CategoryOrder,
                CategoryCompleteTime = category.CategoryCompleteTime,
                IsDeleted = category.IsDeleted,
                HasChildren = category.HasChildren,
                IsChildrenLoaded = false,
                Children = new ObservableCollection<TaskCategoryViewObject>()
            };
            return categoryView;
        }
        
        
        public static TaskCategoryViewObject GetFrom(TaskCategory category, long uncompletedCount,long subCategoryCount)
        {
            var categoryView = new TaskCategoryViewObject
            {
                Name = category.Name,
                TaskCount = uncompletedCount,
                SubCategoryCount = subCategoryCount,
                IconName = "\uED41",
                IsSystemCategory = false,
                DueDate = category.CategoryDueTime,
                Key = category.Key,
                ParentCategoryKey = category.ParentCategoryKey,
                CategoryOrder = category.CategoryOrder,
                CategoryCompleteTime = category.CategoryCompleteTime,
                IsDeleted = category.IsDeleted,
                HasChildren = category.HasChildren,
                IsChildrenLoaded = false,
                Children = new ObservableCollection<TaskCategoryViewObject>()
            };
            return categoryView;
        }
        
        public static TaskCategoryViewObject DeepCopyCategory(TaskCategoryViewObject original)
        {
            if (original == null) return null;

            var copy = new TaskCategoryViewObject
            {
                Key = original.Key,
                Name = original.Name,
                TaskCount = original.TaskCount,
                SubCategoryCount = original.SubCategoryCount,
                IconName = original.IconName,
                IsSystemCategory = original.IsSystemCategory,
                DueDate = original.DueDate,
                Priority = original.Priority, // Assuming TaskPriority is an enum or value type
                ParentCategoryKey = original.ParentCategoryKey,
                CategoryOrder = original.CategoryOrder,
                CategoryCompleteTime = original.CategoryCompleteTime,
                IsDeleted = original.IsDeleted,
                HasChildren = original.HasChildren,
                IsChildrenLoaded = original.IsChildrenLoaded, // Or perhaps set to false for a fresh copy?
                Children = new ObservableCollection<TaskCategoryViewObject>()
            };

            if (original.Children != null)
            {
                foreach (var child in original.Children)
                {
                    copy.Children.Add(DeepCopyCategory(child));
                }
            }
            return copy;
        }
        
      
    }
}
