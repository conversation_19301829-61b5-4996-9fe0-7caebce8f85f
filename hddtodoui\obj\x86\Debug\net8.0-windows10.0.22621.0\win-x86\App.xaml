﻿<?xml version="1.0" encoding="utf-8"?>

<Application
    x:Class="HddtodoUI.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="using:HddtodoUI.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:DateToStringConverter x:Key="DateToStringConverter" />
            <converters:TaskStatusTooltipConverter x:Key="TaskStatusTooltipConverter" />
            <converters:TaskStatusIconConverter x:Key="TaskStatusIconConverter" />
            <converters:TaskStatusColorConverter x:Key="TaskStatusColorConverter" />
            <converters:NullableDateTimeToCheckedConverter x:Key="NullableDateTimeToCheckedConverter" />
            <converters:NullableDateTimeToStrikethroughConverter x:Key="NullableDateTimeToStrikethroughConverter" />

            <!-- Text Styles -->
            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="28" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
            </Style>

            <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="22" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
            </Style>

            <Style x:Key="CategoryHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
            </Style>
            
            <Style x:Key="BodyTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
            </Style>
            
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="15" />
                <Setter Property="FontFamily" Value="黑体" />
                <Setter Property="Foreground" Value="{ThemeResource TitleTextBrush}" />
            </Style>

            <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="Foreground" Value="{ThemeResource SecondaryTextBrush}" />
            </Style>
            
       

            <!-- Button Styles -->
            <Style x:Key="PrimaryButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{ThemeResource AccentBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="Padding" Value="16,8" />
            </Style>

            <Style x:Key="SecondaryButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="Foreground" Value="{ThemeResource PrimaryTextBrush}" />
                <Setter Property="BorderBrush" Value="{ThemeResource BorderBrush}" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="Padding" Value="16,8" />
            </Style>

            <!-- ListViewItem Styles -->
            <Style x:Key="TaskListItemStyle" TargetType="ListViewItem">
                <Setter Property="Padding" Value="12,8" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            </Style>

            <Style x:Key="TaskItemStyle" TargetType="ListViewItem">
                <Setter Property="Padding" Value="2,4" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            </Style>

            <!-- AutoSuggestBox Style -->
            <Style x:Key="SearchBoxStyle" TargetType="AutoSuggestBox">
                <Setter Property="Background" Value="{ThemeResource WindowBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{ThemeResource BorderBrush}" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="CornerRadius" Value="4" />
            </Style>

            <!-- Theme Colors -->
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <!-- Background Colors -->
                    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#FFFFFF" />

                    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="#66d8c7b6" />
                    <SolidColorBrush x:Key="UserInfoBackgroundBrush" Color="#1Ad8C7B6" />


                    <SolidColorBrush x:Key="TitleBarBackgroundBrush" Color="#e4d5c9" />
                    <SolidColorBrush x:Key="StatusBarBackgroundBrush" Color="#e4d5c9" />

                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#5b473e" />
                    <SolidColorBrush x:Key="TitleTextBrush" Color="#003E92" />
                    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#7d5b4f" />
                    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#a7796c" />
                    <SolidColorBrush x:Key="UserInfoTextBrush" Color="#5b473e" />

                    <!-- Accent Colors -->
                    <SolidColorBrush x:Key="AccentBrush" Color="#c6b29a" />
                    <SolidColorBrush x:Key="AccentLightBrush" Color="#e7d0c5" />

                    <!-- Border Colors -->
                    <SolidColorBrush x:Key="BorderBrush" Color="#d8c7b6" />
                    <!-- <SolidColorBrush x:Key="SeparatorBrush" Color="#f3e5d8"/> -->
                    <SolidColorBrush x:Key="SeparatorBrush" Color="#d1af9f" />
                    <!-- Status Colors -->
                    <SolidColorBrush x:Key="WarningBrush" Color="#a7796c" />
                    <SolidColorBrush x:Key="ErrorBrush" Color="#7d5b4f" />
                    <SolidColorBrush x:Key="SuccessBrush" Color="#5b473e" />
                </ResourceDictionary>

                <ResourceDictionary x:Key="Dark">
                    <!-- Background Colors -->
                    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#5b473e" />
                    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="#7d5b4f" />
                    <SolidColorBrush x:Key="TitleBarBackgroundBrush" Color="#5b473e" />

                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#e2cdc5" />
                    <SolidColorBrush x:Key="TitleTextBrush" Color="#5b473e" />
                    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#d1af9f" />
                    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#a7796c" />

                    <!-- Accent Colors -->
                    <SolidColorBrush x:Key="AccentBrush" Color="#d1af9f" />
                    <SolidColorBrush x:Key="AccentLightBrush" Color="#7d5b4f" />

                    <!-- Border Colors -->
                    <SolidColorBrush x:Key="BorderBrush" Color="#7d5b4f" />
                    <SolidColorBrush x:Key="SeparatorBrush" Color="#a7796c" />

                    <!-- Status Colors -->
                    <SolidColorBrush x:Key="WarningBrush" Color="#d1af9f" />
                    <SolidColorBrush x:Key="ErrorBrush" Color="#e2cdc5" />
                    <SolidColorBrush x:Key="SuccessBrush" Color="#a7796c" />
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>


        </ResourceDictionary>
    </Application.Resources>
</Application>
