using System;
using System.Collections.Concurrent;
using System.Runtime.Versioning;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;

namespace HddtodoUI.TaskTomatoManager
{
    public enum TomatoTaskStaus
    {
        taskCompleted,
        taskUnCompleteAndNotTomatoStart,
        taskUnCompletedAndTomatoOngoing
    }

    public static class TaskTomatoManagerFactory
    {
        private static ConcurrentDictionary<long, TomatoTaskManager> taskTomatoManagerDict =
            new ConcurrentDictionary<long, TomatoTaskManager>();


        public static TomatoTaskManager GetTomatoTaskManager(TTask task)
        {
            if (taskTomatoManagerDict.ContainsKey(task.TaskID))
            {
                return taskTomatoManagerDict[task.TaskID];
            }
            else
            {
                var taskManager = new TaskDBRecorder(task);
                var myTomatoTask = new TomatoTaskManager(taskManager);
                taskTomatoManagerDict.TryAdd(task.TaskID, myTomatoTask);
                return myTomatoTask;
            }
        }

        public static TomatoTaskManager GetTomatoTaskManager(long taskId)
        {
            if (taskTomatoManagerDict.ContainsKey(taskId))
            {
                return taskTomatoManagerDict[taskId];
            }
            else
            {
                var task = StoreFactoryHolder.getTaskStore().getTaskById(taskId, UserInfoHolder.getUserId());
                var taskManager = new TaskDBRecorder(task);
                var myTomatoTask = new TomatoTaskManager(taskManager);
                taskTomatoManagerDict.TryAdd(task.TaskID, myTomatoTask);
                return myTomatoTask;
            }
        }

        public static void RefreshTask(TTask task)
        {
            if (taskTomatoManagerDict.ContainsKey(task.TaskID))
            {
                var ttManager = taskTomatoManagerDict[task.TaskID];
                ttManager.setDBRecorder(new TaskDBRecorder(task));
            }
        }

        public static void RefreshAllTasks()
        {
            foreach (var tomatoTaskManager in taskTomatoManagerDict.Values)
            {
                tomatoTaskManager.Refresh();
            }
        }

        public static void RefreshTasksBelongToList(String ListKey)
        {
            foreach (var tomatoTaskManager in taskTomatoManagerDict.Values)
            {
                if (tomatoTaskManager.GetBelongToTaskList().Key != ListKey) continue;
                tomatoTaskManager.Refresh();
            }
        }
    }

    [SupportedOSPlatform("windows")]
    public class TomatoTaskManager
    {
        private TaskTimeLog _currentTaskTimeLog;

        private TaskDBRecorder _taskDbRecorder;
        private Tomato _tomato;

        private long _managerRandomId = 0;

        public TomatoTaskManager(TaskDBRecorder dbRecorder)
        {
            _taskDbRecorder = dbRecorder;
            _managerRandomId = new Random().Next();
        }
        
        public long ManagerRandomId => _managerRandomId;

        public int RemainCount => this.getTomatoOrEmpltyUselessTomato().CountDown;

        public int TomatoCount => 0;

        public bool IsCompleted => _taskDbRecorder.getTask().IsCompleted();

        public String GetRemainTimeString()
        {
            DateTime dt = new DateTime();
            return dt.AddSeconds(RemainCount).ToString("mm:ss");
        }

        public void setDBRecorder(TaskDBRecorder dbRecorder)
        {
            _taskDbRecorder = dbRecorder;
        }


        public void setTomato(Tomato tomato)
        {
            _tomato = tomato;
        }

        private Tomato getTomatoOrEmpltyUselessTomato()
        {
            if (_tomato == null)
                return new Tomato(_taskDbRecorder.getTask().TomatoTimeSpan);
            return _tomato;
        }

        public Tomato getTomatoMayBeNull()
        {
            return _tomato;
        }

        public TaskCategory GetBelongToTaskList()
        {
            return _taskDbRecorder.getBelongToTaskList();
        }

        public void moveTaskToOtherTaskList(TaskCategory target)
        {
            _taskDbRecorder.moveTaskToOtherTaskList(target);
            _taskDbRecorder.Refresh();
            LogService.Instance.Info("task belong to:" + _taskDbRecorder.getBelongToTaskList().Key);
        }

        public void RefreshTaskData()
        {
            _taskDbRecorder.Refresh();
            LogService.Instance.Info("task data refreshed for task:" + _taskDbRecorder.getTask().TaskID);
        }

        public TTask getTask()
        {
            return _taskDbRecorder.getTask();
        }

        public long getTaskID()
        {
            return getTask().TaskID;
        }

        public void Refresh()
        {
            _taskDbRecorder.Refresh();
        }


        // tomato related methos ----------------------------------------------------------------------------------------------------------

        public  Task StartTaskTimeLogAsync()
        {
            if (_currentTaskTimeLog == null)
            {
                return StoreFactoryHolder.getTaskTimeLogStore()
                    .CreateTaskTimeLogAsync(_taskDbRecorder.getTask().TaskID, UserInfoHolder.getUserId())
                    .ContinueWith(t => _currentTaskTimeLog = t.Result);
            
            }

            return null;
        }

        public async Task EndTaskTimeLogAsync()
        {
            if (_currentTaskTimeLog != null)
            {
                await StoreFactoryHolder.getTaskTimeLogStore()
                    .EndTaskTimeLogAsync(_currentTaskTimeLog, UserInfoHolder.getUserId(),ClientInfoProvider.ClientId);
                LogService.Instance.Info("EndTaskTimeLog set null");
                _currentTaskTimeLog = null;
            }
        }

        public  void tomatoStart()
        {
            if (_tomato == null) _tomato = new Tomato(_taskDbRecorder.getTask().TomatoTimeSpan);
            getTomatoOrEmpltyUselessTomato().startTomato();
            _ = StartTaskTimeLogAsync();
            onTomatoStarted(EventArgs.Empty);
            
        }

        public async void tomatoPause()
        {
            getTomatoOrEmpltyUselessTomato().pauseTomato();
            await EndTaskTimeLogAsync();
            onTomatoPaused(EventArgs.Empty);
        }


        public void tomatoSwitch(Tomato currentTomato = null)
        {
            if (currentTomato != null)
            {
                setTomato(currentTomato);
            }

            if (IsStarted() == false)
                tomatoStart();
            else
                tomatoPause();
        }


        public bool clockTicked()
        {
            var statusChanged = false;
            //如果是倒计时自然结束，则要通知tomato end 事件
            if (getTomatoOrEmpltyUselessTomato().clockTicked())
            {
                statusChanged = true;
                _= EndTaskTimeLogAsync();
                onTomatoEnded(EventArgs.Empty);

                if (_tomato.taskTimeSpan != _taskDbRecorder.getTask().TomatoTimeSpan)
                {
                    _tomato.taskTimeSpan = _taskDbRecorder.getTask().TomatoTimeSpan;
                    _tomato.resetCountDown();
                }

                // System.Media.SystemSounds.Exclamation.Play();
                // System.Media.SystemSounds.Hand.Play();
            }

            onClockTicked(EventArgs.Empty);
            
            TaskHeartbeatService.ClockTickedAsync(getTaskID());

            return statusChanged;
        }

        public bool IsStarted()
        {
            return getTomatoOrEmpltyUselessTomato().isStarted();
        }


        public bool IsTheSameTask(TomatoTaskManager test)
        {
            if (test == null) return false;
            if (test.getTask() == null) return false;

            return getTask().TaskID ==
                   test.getTask().TaskID;
        }

        // task related methos ----------------------------------------------------------------------------------------------------------

        public async Task completeTask()
        {
            try
            {


                // 检查是否有正在进行的 TaskTimeLog
                var currentLog = _currentTaskTimeLog;
                if (currentLog != null && !currentLog.IsCompleted)
                {
                    // 如果有正在进行的 log，结束它
                    await Task.Run(async () => { await EndTaskTimeLogAsync(); });

                }
                else
                {
                    await Task.Run(() =>
                    {
                        // 如果没有正在进行的 log，创建一个新的已完成的 log
                        StoreFactoryHolder.getTaskTimeLogStore()
                            .CreateCompletedTaskTimeLogAsync(getTask().TaskID, UserInfoHolder.getUserId());
                        LogService.Instance.Info("continue with CreateCompletedTaskTimeLogAsync");
                    });

                }

                getTomatoOrEmpltyUselessTomato().pauseTomato();


                if (_taskDbRecorder.completeTask())
                {
                    _taskDbRecorder.Refresh();
                }
                else
                {
                    throw new Exception("completeTask failed");
                }

                onTaskCompleteUIEvent(getTask());
            }
            catch (Exception ex)
            {
                LogService.Instance.Error(ex.Message);
                NotificationService.Instance.ShowNotification(ex.StackTrace, NotificationLevel.Danger);
            }
          
            LogService.Instance.Info("completeTask end");
        }

        public async Task uncompleteTask()
        {
            await Task.Run(() =>
            {
                _taskDbRecorder.unCompleteTask();
                _taskDbRecorder.Refresh();
            });
            onTaskUnCompleteUIEvent(EventArgs.Empty);
            
            LogService.Instance.Info("uncompleteTask end");
          
        }

        public void deleteTask()
        {
            _taskDbRecorder.deleteTask();
            onTaskDeleteUIEvent(EventArgs.Empty);

            // //更新当前task列表
            // TaskUICoodinator.getTaskUICoodinator().refreshCurrentTaskListUI(this);
            // //更新tasklist的计数
            // TaskUICoodinator.getTaskUICoodinator().refreshTaskListUncompleteCountUI(
            //     GetBelongToTaskList().Key, GetBelongToTaskList().Name);
        }

        public TomatoTaskStaus getTaskStatus()
        {
            if (this.IsCompleted)
                return TomatoTaskStaus.taskCompleted;

            if (getTomatoOrEmpltyUselessTomato().isStarted())
                return TomatoTaskStaus.taskUnCompletedAndTomatoOngoing;

            return TomatoTaskStaus.taskUnCompleteAndNotTomatoStart;
        }


        // tomato events ----------------------------------------------------------------------------------------------------------


        // public Notifier<ITaskTomatoListener> getTaskTomatoNotifier()
        // {
        //     return NotifierManager.Instance.GetNotifier<ITaskTomatoListener>();
        // }
        //
        // public Notifier<ITaskTickListener> getTaskTickNotifier()
        // {
        //     return NotifierManager.Instance.GetNotifier<ITaskTickListener>();
        // }


        protected virtual void onClockTicked(EventArgs e)
        {
            // getTaskTickNotifier().Notify(a => a.clockTicked());
            //clockTickedUIEvent?.Invoke(this, e);
        }

        protected async virtual void onTomatoStarted(EventArgs e)
        {
            if (ConfigSettingsUtils.getAutoHideMainFormConfig())
            {
                TheUICoordinator.Instance.HideMainWindow();
                //TaskUICoordinator.Instance.HideTaskDetailsWindow();
            }

            if (ConfigSettingsUtils.getTaskStartAutoShowTaskDetailConfig())
            {
                var task = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask()
                    .getTask();
                
                if (task == null) return;

                TaskCategory taskList = null;

                taskList = await Task.Run(() =>
                {
                    return  _taskDbRecorder.getBelongToTaskList();
                    
                });
                   
                var vo = TodoTaskViewObject.GetFrom(task,taskList);
                TheUICoordinator.Instance.ShowTaskDetailsWindow(vo);
            }
        }

        protected virtual void onTomatoPaused(EventArgs e)
        {
            if (ConfigSettingsUtils.getTaskPauseAutoSwitchToTaskSelectionConfig())
            {
                TheUICoordinator.Instance.ShowMainWindow();
            }
        }

        protected virtual void onTomatoEnded(EventArgs e)
        {
            if (ConfigSettingsUtils.getTaskPauseAutoSwitchToTaskSelectionConfig())
            {
                TheUICoordinator.Instance.ShowMainWindow();
            }
        }


        // task events ----------------------------------------------------------------------------------------------------------


        protected virtual void onTaskDataChangedEvent(EventArgs e)
        {
            //getTaskTomatoNotifier().Notify(a => a.taskDataChanged(getTaskID()));
            //taskDataChangedUIEvent?.Invoke(this, e);
        }

        protected virtual void onTaskCompleteUIEvent(TTask task)
        {
            if (ConfigSettingsUtils.GetTaskCompletedAutoSwitchToTaskSelectionConfig())
            {
                if (task.IsHasParent())
                {
                    var parentTaskId = task.GetDirectParentTaskId();
                    var parentTask = StoreFactoryHolder.getTaskStore().getTaskById(parentTaskId, UserInfoHolder.getUserId());
                    var parentTaskList = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(parentTask.BelongToListKey, UserInfoHolder.getUserId());
                    TheUICoordinator.Instance.ShowTaskDetailsWindow(TodoTaskViewObject.GetFrom(parentTask,parentTaskList));
                }
                else
                {
                    TheUICoordinator.Instance.HideTaskDetailsWindow();
                    TheUICoordinator.Instance.ShowMainWindow();
                    LogService.Instance.Info("打开主界面");
                }
            
            }
        }

        protected virtual void onTaskUnCompleteUIEvent(EventArgs e)
        {
            //getTaskTomatoNotifier().Notify(a => a.taskUnComplete(getTaskID()));
            //taskUnCompleteUIEvent?.Invoke(this, e);
        }

        protected virtual void onTaskDeleteUIEvent(EventArgs e)
        {
            //getTaskTomatoNotifier().Notify(a => a.taskDelete(getTaskID()));
            //taskDeleteEvent?.Invoke(this, e);
        }
    }
}