using System;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Windows;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Documents;


namespace HddtodoUI.UICoordinator;

public static class NotificationUiHelper
{

    private static async Task<UIElement> CreateAddTaskNotificationContentAsync(TTask task)
    {
        // 在后台线程获取 taskList
        TaskCategory taskCategory = await Task.Run(() =>
        {
            return StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());
        });

        // 创建一个包含多种控件的面板
        var panel = new StackPanel
        {
            Spacing = 10
        };

        // 添加一个超链接
        var hyperlinkTextBlock = new TextBlock
        {
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 5, 0, 5)
        };

        var hyperlink = new Hyperlink();
        hyperlink.Inlines.Add(new Run { Text = task.Title });
        hyperlink.Click += (sender, args) =>
        {
            var vo = TodoTaskViewObject.GetFrom(task, taskCategory);
            TheUICoordinator.Instance.ShowTaskDetailsWindow(vo);
        };

        hyperlinkTextBlock.Inlines.Add(hyperlink);
        panel.Children.Add(hyperlinkTextBlock);

        return panel;
    }

    public static async void ShowTaskAddNotification(TTask task, string title = "任务已经添加")
    {
        var customContent = await CreateAddTaskNotificationContentAsync(task);
        NotificationService.Instance.ShowNotification(customContent, NotificationLevel.Info, title, 20000);
    }
}