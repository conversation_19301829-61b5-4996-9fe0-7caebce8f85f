using System;
using NLog;
using System.IO;
using System.Runtime.CompilerServices;
using HddtodoUI.Utilities;

namespace HddtodoUI.Services
{
    /// <summary>
    /// u65e5u5fd7u670du52a1uff0cu63d0u4f9bu5e94u7528u7a0bu5e8fu7684u65e5u5fd7u8bb0u5f55u529fu80fd
    /// </summary>
    public class LogService
    {
        private static LogService _instance;
        private readonly Logger _logger;

        /// <summary>
        /// u83b7u53d6u65e5u5fd7u670du52a1u7684u5355u4f8bu5b9eu4f8b
        /// </summary>
        public static LogService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new LogService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// u6784u9020u51fdu6570
        /// </summary>
        private LogService()
        {
           
            string logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "HddtodoUI",
                "logs");
            
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            string configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "NLog.config");
            if (File.Exists(configFilePath))
            {
                NLog.LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration(configFilePath);
                Console.WriteLine("NLog.config 已手动加载。");
            }
            else
            {
                Console.WriteLine($"未找到 NLog.config 文件，路径：{configFilePath}");
            }

            
            // u521du59cbu5316u65e5u5fd7u5668
            _logger = LogManager.GetCurrentClassLogger();
        }

        /// <summary>
        /// </summary>
        public void Debug(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string sourceFilePath = "", [CallerLineNumber] int sourceLineNumber = 0)
        {
            _logger.Debug($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            Console.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            System.Diagnostics.Debug.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
        }

        /// <summary>
        /// u8bb0u5f55u4fe1u606f
        /// </summary>
        public void Info(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string sourceFilePath = "", [CallerLineNumber] int sourceLineNumber = 0)
        {
            _logger.Info($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            Console.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            System.Diagnostics.Debug.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
        }

        /// <summary>
        /// u8bb0u5f55u8b66u544a
        /// </summary>
        public void Warn(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string sourceFilePath = "", [CallerLineNumber] int sourceLineNumber = 0)
        {
            _logger.Warn($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            Console.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            System.Diagnostics.Debug.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
        }

        /// <summary>
        /// u8bb0u5f55u9519u8bef
        /// </summary>
        public void Error(string message, Exception ex = null, [CallerMemberName] string memberName = "", [CallerFilePath] string sourceFilePath = "", [CallerLineNumber] int sourceLineNumber = 0)
        {
            if (ex != null)
            {
                string exceptionDetails = ExceptionDisplayHelper.GetFullExceptionDetails(ex);
                Console.WriteLine(ex.StackTrace,$"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace,$"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
                _logger.Error(ex.StackTrace, $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
                
            }
            else
            {
                Console.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
                System.Diagnostics.Debug.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
                _logger.Error($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            }
        }

        /// <summary>
        /// u8bb0u5f55u81f4u547du9519u8bef
        /// </summary>
        public void Fatal(string message, Exception ex = null, [CallerMemberName] string memberName = "", [CallerFilePath] string sourceFilePath = "", [CallerLineNumber] int sourceLineNumber = 0)
        {
            if (ex != null)
            {
                string exceptionDetails = ExceptionDisplayHelper.GetFullExceptionDetails(ex);
                Console.WriteLine(ex.StackTrace,$"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace,$"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
                _logger.Fatal(ex.StackTrace, $"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {exceptionDetails}");
            }
            else
            {
                Console.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
                System.Diagnostics.Debug.WriteLine($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
                _logger.Fatal($"{Path.GetFileName(sourceFilePath)}:{memberName}:{sourceLineNumber} - {message}");
            }
        }
        
      
    }
}
