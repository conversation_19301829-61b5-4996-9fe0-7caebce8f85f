using System;
using System.Text.Json;
using System.Text.Json.Serialization;


namespace HddtodoUI.BackendModels.JsonConverters
{
    public class RemindRepeatTypeConverter : JsonConverter<RemindRepeatType>
    {
        public override RemindRepeatType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string value = reader.GetString();
            if (string.IsNullOrEmpty(value)) return RemindRepeatType.None;

            return value.ToUpper() switch
            {
                "NONE" => RemindRepeatType.None,
                "DAILY" => RemindRepeatType.Daily,
                "WEEKLY" => RemindRepeatType.Weekly,
                "MONTHLY" => RemindRepeatType.Monthly,
                "YEARLY" => RemindRepeatType.Yearly,
                "HOURLY" => RemindRepeatType.Hourly,
                _ => RemindRepeatType.None
            };
        }

        public override void Write(Utf8JsonWriter writer, RemindRepeatType value, JsonSerializerOptions options)
        {
            string stringValue = value switch
            {
                RemindRepeatType.None => "NONE",
                RemindRepeatType.Daily => "DAILY",
                RemindRepeatType.Weekly => "WEEKLY",
                RemindRepeatType.Monthly => "MONTHLY",
                RemindRepeatType.Yearly => "YEARLY",
                RemindRepeatType.Hourly => "HOURLY",
                _ => "NONE"
            };
            writer.WriteStringValue(stringValue);
        }
    }
}
