﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TaskItemControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    mc:Ignorable="d"
    d:DesignHeight="60"
    d:DesignWidth="400">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
        <converters:TaskStatusTooltipConverter x:Key="TaskStatusTooltipConverter" />
        <converters:TaskStatusIconConverter x:Key="TaskStatusIconConverter" />
        <converters:TaskStatusColorConverter x:Key="TaskStatusColorConverter" />
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
    </UserControl.Resources>

    <Border Background="Transparent">
        <Grid x:ConnectionId='2' Padding="3,5,0,5" x:Name="RootGrid" >
        <Grid.ContextFlyout>
            <MenuFlyout>
                <MenuFlyoutItem x:ConnectionId='3' Text="编辑"                         >
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE70F;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
                <MenuFlyoutItem x:ConnectionId='4' Text="复制任务标题"                                  >
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE8C8;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
                
                <MenuFlyoutSeparator />
                <MenuFlyoutSubItem Text="设置截止日期">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE823;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem x:ConnectionId='23' Text="今天"                               />
                    <MenuFlyoutItem x:ConnectionId='24' Text="明天"                                  />
                    <MenuFlyoutItem x:ConnectionId='25' Text="3天后"                               />
                    <MenuFlyoutItem x:ConnectionId='26' Text="无截止日期"                                   />
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="设置重要程度">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE734;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem x:ConnectionId='20' Text="高"                              >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Gold" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='21' Text="中"                                >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Black" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='22' Text="低"                             >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE735;" Foreground="Gray" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="设置颜色">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE790;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem x:ConnectionId='8' Text="粉色"                           >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Pink" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='9' Text="橙色"                             >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Orange" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='10' Text="绿色"                            >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Green" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='11' Text="蓝色"                           >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Blue" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='12' Text="紫色"                             >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Purple" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='13' Text="棕色"                            >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Brown" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='14' Text="金色"                           >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Gold" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='15' Text="黑色"                            >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Black" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='16' Text="橄榄色"                            >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Olive" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='17' Text="蓝绿色"                           >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Teal" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='18' Text="番茄色"                             >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xEA3A;" Foreground="Tomato" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='19' Text="无颜色"                           >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE711;" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSubItem Text="调整任务顺序">
                    <MenuFlyoutSubItem.Icon>
                        <FontIcon Glyph="&#xE8CB;" />
                    </MenuFlyoutSubItem.Icon>
                    <MenuFlyoutItem x:ConnectionId='6' Text="移到本类顶部"                     >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE183;" Foreground="Red" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                    <MenuFlyoutItem x:ConnectionId='7' Text="移到本类底部"                        >
                        <MenuFlyoutItem.Icon>
                            <FontIcon Glyph="&#xE118;" Foreground="Green" />
                        </MenuFlyoutItem.Icon>
                    </MenuFlyoutItem>
                </MenuFlyoutSubItem>
                <MenuFlyoutSeparator />
                <MenuFlyoutItem x:ConnectionId='5' Text="放入回收站"                           >
                    <MenuFlyoutItem.Icon>
                        <FontIcon Glyph="&#xE74D;" />
                    </MenuFlyoutItem.Icon>
                </MenuFlyoutItem>
            </MenuFlyout>
        </Grid.ContextFlyout>
        <Grid.ColumnDefinitions>
           
            <ColumnDefinition Width="30" />
           
            <ColumnDefinition Width="40" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="30" />
            <ColumnDefinition Width="10" />
        </Grid.ColumnDefinitions>

        
        <Border x:ConnectionId='27' Grid.Column="4" Name="ColorIndicator" Width="4"></Border>
        <Border x:ConnectionId='28' Grid.Column="3" Name="TaskIconBorder">
        </Border>
        <!-- 任务完成复选框 -->
        <CheckBox x:ConnectionId='29' Grid.Column="0" x:Name="TaskCheckBox"
                                 VerticalAlignment="Center"
                                  />

        <!-- 任务开始按钮 -->
        <Button x:ConnectionId='30' x:Name="StartTaskButton"
                Grid.Column="1"
                VerticalAlignment="Center"
                                             
                Background="Transparent"
                BorderThickness="0"
                Padding="0"
                Width="32"
                Height="32"
                                                                                                                                   >
            <FontIcon x:ConnectionId='34'                                                                                                
                      FontSize="16"
                                                                                                                            />
        </Button>

        <!-- 任务信息 -->
        <Grid Grid.Column="2" MinHeight="36">
            <StackPanel VerticalAlignment="Center" Margin="0,0,0,0"  >
                <TextBlock x:ConnectionId='31' Name="TaskTitleTextBlock" TextWrapping="Wrap" 
                                                              
                                                                     
                           Style="{StaticResource TitleTextStyle}">
                   
                </TextBlock>
                <StackPanel Orientation="Horizontal">
                    
                    <TextBlock x:ConnectionId='32' x:Name="TaskDueDateTextBlock"                                    
                                                                                                                        
                               Style="{StaticResource CaptionTextStyle}" VerticalAlignment="Center"
                                                                                                                                  
                                />
                 
                    <TextBlock x:ConnectionId='33' Name="SubTaskBadge"                                                            Foreground="Gray" FontSize="11" Margin="10,0,0,0" VerticalAlignment="Center"></TextBlock>
                    <!-- <InfoBadge Name="SubTaskBadge" Value="{x:Bind TaskVO.DirectSubTaskCount, Mode=OneWay}" Visibility="Collapsed" Margin="10,0,0,0"></InfoBadge> -->
                </StackPanel>
              
            </StackPanel>
        </Grid>
       
      

    </Grid>
    </Border>
</UserControl>

