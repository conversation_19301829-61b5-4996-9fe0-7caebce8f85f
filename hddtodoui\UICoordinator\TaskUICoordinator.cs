using System;
using System.Diagnostics;
using System.Runtime.Versioning;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.WinUI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Controls;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;

namespace HddtodoUI.UICoordinator
{
    [SupportedOSPlatform("windows")]
    public class TaskUICoordinator : TaskUIBaseCoordinator, ITaskUiCoordinator
    {
        public TaskUICoordinator()
        {
        }

        //---------------------------------------------------------------UI业务层面的操作

        public async Task StartOrPauseButtonClick(TomatoTaskManager tomatoTaskManager)
        {
            try
            {
                var previousTomatoManager = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask();
            if (previousTomatoManager != null)
            {
                if (previousTomatoManager.getTaskID() != tomatoTaskManager.getTaskID())
                {
                    previousTomatoManager.tomatoPause();
                }

                var itemControl = TheUICoordinator.Instance.GetMainWindow().GetMainView()
                    .GetTaskPanelTaskItemControlByTaskID(previousTomatoManager.getTaskID());
                if (itemControl != null)
                    itemControl.RefreshUI();

                if (previousTomatoManager.getTask().IsHasParent())
                {
                    //if previews task is a sub task and has the same parent task
                    if (IsCurrentTaskDetailWindowTask(previousTomatoManager.getTask().GetDirectParentTaskId()))
                    {
                        var preControl = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                            .GetSubTaskItemControlByTaskId(previousTomatoManager.getTask().TaskID);
                        if (preControl != null)
                            preControl.RefreshUI();
                    }
                }
            }

            if (tomatoTaskManager.IsCompleted)
            {
                await tomatoTaskManager.uncompleteTask();
                TheUICoordinator.Instance.GetIndicatorWindow().RefreshTaskUI(tomatoTaskManager);
            }
               
            
            CurrentStatusHolder.getCurrentStatusHolder().setCurrentTomatoTask(tomatoTaskManager);
            tomatoTaskManager.tomatoSwitch();

            var currentItemControl = TheUICoordinator.Instance.GetMainWindow().GetMainView()
                .GetTaskPanelTaskItemControlByTaskID(tomatoTaskManager.getTaskID());
            if (currentItemControl != null)
                currentItemControl.RefreshUI();

            TheUICoordinator.Instance.GetIndicatorWindow().RefreshTaskUI(tomatoTaskManager);
            // 根据当前任务更新 TaskStepWindow 显隐（只在任务切换/开始时调用）
            var indicatorWindow = TheUICoordinator.Instance.GetIndicatorWindow();
            if (indicatorWindow != null)
            {
                if (tomatoTaskManager.getTomatoMayBeNull().isStarted())
                {
                    TheUICoordinator.Instance.UpdateTaskStepWindow(tomatoTaskManager.getTask(), indicatorWindow.AppWindow, false);
                }
                else
                {
                    TheUICoordinator.Instance.HideTaskStepWindow();
                }
            }
            TheUICoordinator.Instance.ShowIndicatorWindow();
            }
            catch (Exception ex)
            {
                // Log the exception (replace with your actual logging mechanism)
                LogService.Instance.Error("Error in StartOrPauseButtonClick", ex);
                // Optionally, rethrow or handle specific exceptions as needed
                // Consider rethrowing if the caller needs to be aware of the failure:
                // throw;
            }
        }

        public async void OnTaskAdded(TTask task, TaskCategory belongTaskList)
        {
            TheUICoordinator.Instance.GetMainWindow().GetMainView().AddTaskToTaskPanelUnCompletedTaskListLast(task);

            int count =  StoreFactoryHolder.getTaskCategoryStore()
                .GetCategoryUncompleteTaskCount(belongTaskList.Key, UserInfoHolder.getUserId());
            
            LogService.Instance.Debug("OnTaskAdded key" + belongTaskList.Key + "count" + count);
            TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(belongTaskList.Key, count);

            if (SystemCategoryManager.IsNeedUpdateSystemCategory(task))
            {
                TheUICoordinator.Instance.UpdateSystemCategory();
            }

            NotificationUiHelper.ShowTaskAddNotification(task);

            LogService.Instance.Info("OnTaskAdded end;");
        }

        public async void TaskSwitchComplete(TTask task)
        {
            try
            {
                var tomatoTaskManager = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                await tomatoTaskManager.completeTask();

                int count = 0;
                await Task.Run( async () =>
                {
                    count =   StoreFactoryHolder.getTaskCategoryStore()
                        .GetCategoryUncompleteTaskCount(task.BelongToListKey, UserInfoHolder.getUserId());
                });

                LogService.Instance.Debug("TaskSwitchComplete key" + task.BelongToListKey + "count" + count);
                TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(task.BelongToListKey, count);

                //重新计算相关计数
                if (SystemCategoryManager.IsNeedUpdateSystemCategory(task))
                {
                    TheUICoordinator.Instance.UpdateSystemCategory();
                }

                LogService.Instance.Debug("TaskSwitchComplete1 key" + task.BelongToListKey + "count" + count);
                if (CurrentStatusHolder.getCurrentStatusHolder().isTaskInCurrentDisplayList(task))
                {
                    //如果任务是所在当前列表，则处理相关列表变化
                    TTask refreshedTask = null;
                    await Task.Run(() =>
                    {
                        refreshedTask = StoreFactoryHolder.getTaskStore()
                            .getTaskById(task.TaskID, UserInfoHolder.getUserId());
                    });

                    TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentUncompletedTaskList(task.TaskID);
                    TheUICoordinator.Instance.AddTaskToTaskPanelCurrentCompletedTaskListFirst(refreshedTask);
                }

                LogService.Instance.Debug("TaskSwitchComplete key2" + task.BelongToListKey + "count" + count);
                if (IsCurrentTomatoTask(task.TaskID))
                {
                    //要处理indicatorform的变化
                    TheUICoordinator.Instance.GetIndicatorWindow()
                        .RefreshTaskUI(CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask());
                    // 任务完成后同步处理 TaskStepWindow
                    var indWin = TheUICoordinator.Instance.GetIndicatorWindow();
                    if (indWin != null)
                    {
                        TheUICoordinator.Instance.HideTaskStepWindow();
                    }
                }

                LogService.Instance.Debug("TaskSwitchComplete key3" + task.BelongToListKey + "count" + count);
                //还要处理taskdetailform如果正好是当前编辑task，也要处理变化
                if (IsCurrentTaskDetailWindowTask(task.TaskID))
                    TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

                LogService.Instance.Debug("TaskSwitchComplete end;");
            }
            catch (Exception e)
            {
                LogService.Instance.Debug(e.StackTrace);
            }
        }

        public async void TaskSwitchUnComplete(TTask task)
        {
            try
            {
                var tomatoTaskManager = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                tomatoTaskManager.uncompleteTask();

                //重新计算相关计数
                int count = 0;
                await Task.Run(async () =>
                {
                    count =   StoreFactoryHolder.getTaskCategoryStore()
                        .GetCategoryUncompleteTaskCount(task.BelongToListKey, UserInfoHolder.getUserId());
                });

                LogService.Instance.Debug("TaskSwitchUnComplete key" + task.BelongToListKey + "count" + count);
                TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(task.BelongToListKey, count);

                if (SystemCategoryManager.IsNeedUpdateSystemCategory(task))
                {
                    TheUICoordinator.Instance.UpdateSystemCategory();
                }

                TTask refreshedTask = null;
                await Task.Run(() =>
                {
                    refreshedTask = StoreFactoryHolder.getTaskStore()
                        .getTaskById(task.TaskID, UserInfoHolder.getUserId());
                });

                TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentCompletedTaskList(refreshedTask);
                if (CurrentStatusHolder.getCurrentStatusHolder().isTaskInCurrentDisplayList(task))
                {
                    //如果任务是所在当前列表，则处理相关列表变化
                    TheUICoordinator.Instance.AddTaskToTaskPanelCurrentUncompletedTaskList(refreshedTask);
                }

                if (IsCurrentTomatoTask(task.TaskID))
                {
                    //要处理indicatorform的变化
                    TheUICoordinator.Instance.GetIndicatorWindow()
                        .RefreshTaskUI(CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask());
                }

                //还要处理taskdetailform如果正好是当前编辑task，也要处理变化
                if (IsCurrentTaskDetailWindowTask(task.TaskID))
                    TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

                LogService.Instance.Debug("TaskSwitchUnComplete end;");
            }
            catch (Exception e)
            {
                LogService.Instance.Error("TaskSwitchUnComplete exception:", e);
            }
        }

        public async void OnTaskModified(TTask task, TodoTaskViewObject previousTaskViewObject)
        {
            try
            {
                TaskTomatoManagerFactory.RefreshTask(task);

                if (previousTaskViewObject.Category != task.BelongToListKey)
                {
                    DispatcherQueue dispatcherQueue = DispatcherQueue.GetForCurrentThread();

                    await Task.Run(async () =>
                    {
                        int count = 0;
                        int previousCount = 0;

                        // 耗时数据库操作
                        count =   StoreFactoryHolder.getTaskCategoryStore()
                            .GetCategoryUncompleteTaskCount(task.BelongToListKey, UserInfoHolder.getUserId());
                        previousCount =   StoreFactoryHolder.getTaskCategoryStore()
                            .GetCategoryUncompleteTaskCount(previousTaskViewObject.Category, UserInfoHolder.getUserId());
                         
                        // 在UI线程更新计数（使用 Dispatcher）
                        await dispatcherQueue.EnqueueAsync(() =>
                        {
                            TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(task.BelongToListKey, count);
                            TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(
                                previousTaskViewObject.Category, previousCount);
                        });

                        LogService.Instance.Info("OnTaskModified key" + task.BelongToListKey + "count" + count);
                    });
                }

                if (CurrentStatusHolder.getCurrentStatusHolder().isTaskInCurrentDisplayList(task))
                {
                    //如果任务是所在当前列表，则处理任务变化,暂时只兼顾了未完成任务，以后要做已完成任务的处理
                    if (!task.IsCompleted())
                    {
                        var control = TheUICoordinator.Instance.GetMainWindow().GetMainView()
                            .GetTaskPanelTaskItemControlByTaskID(task.TaskID);
                        control.RefreshTaskViewObject();
                        control.RefreshUI();

                        TheUICoordinator.Instance.GetMainWindow().GetMainView()
                            .RefreshTaskPanelUncompletedTaskListTask(task);
                    }

                    if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
                        if (task.BelongToListKey != previousTaskViewObject.Category)
                        {
                            TheUICoordinator.Instance.GetMainWindow().GetMainView()
                                .ReloadTaskPanelUncompletedTaskListView();
                        }
                }
                else
                {
                    //如果修改后的任务不在当前列表中，而以前的任务在当前列表中，则需要在当前列表中去掉该任务
                    if (CurrentStatusHolder.getCurrentStatusHolder()
                        .isTaskInCurrentDisplayList(previousTaskViewObject))
                    {
                        TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentUncompletedTaskList(task.TaskID);
                        LogService.Instance.Info("remove from panel");
                    }
                }

                if (SystemCategoryManager.IsNeedUpdateSystemCategory(task) ||
                    SystemCategoryManager.IsNeedUpdateSystemCategory(previousTaskViewObject))
                {
                    TheUICoordinator.Instance.UpdateSystemCategory();
                }

                if (IsCurrentTomatoTask(task.TaskID))
                {
                    var tm = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                    TheUICoordinator.Instance.GetIndicatorWindow().RefreshTaskUI(tm);
                }
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);

                throw;
            }

            LogService.Instance.Info("OnTaskModified end;");
        }

        public async void OnTaskDragMoveToCategory(TodoTaskViewObject taskViewObject, TaskCategory targetList)
        {
            TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentUncompletedTaskList(taskViewObject.TaskID);

            int count = 0;
             await Task.Run(  () =>
            {
                count =  StoreFactoryHolder.getTaskCategoryStore()
                    .GetCategoryUncompleteTaskCount(taskViewObject.Category, UserInfoHolder.getUserId());
            });

            int count1 = 0;
            await Task.Run(  () =>
            {
                count1 =   StoreFactoryHolder.getTaskCategoryStore()
                    .GetCategoryUncompleteTaskCount(targetList.Key, UserInfoHolder.getUserId());
            });
            
            DispatcherQueue dispatcherQueue = DispatcherQueue.GetForCurrentThread();

            await dispatcherQueue.EnqueueAsync(() =>
            {
                    TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(taskViewObject.Category, count);
                    TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(targetList.Key, count1);
            });

            LogService.Instance.Debug("OnTaskDragMoveToCategory completed!");
        }

        public async void OnTaskRemoved(TTask task)
        {
            try
            {
                TheUICoordinator.Instance.UpdateSystemCategory();

                DispatcherQueue dispatcherQueue = DispatcherQueue.GetForCurrentThread();

                await Task.Run(async () =>
                {
                    int count = 0;

                    // 耗时数据库操作
                    count =  StoreFactoryHolder.getTaskCategoryStore()
                        .GetCategoryUncompleteTaskCount(task.BelongToListKey, UserInfoHolder.getUserId());

                    // 在UI线程更新计数（使用 Dispatcher）
                    await dispatcherQueue.EnqueueAsync(() =>
                    {
                        TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(task.BelongToListKey, count);
                    });

                    LogService.Instance.Info("OnTaskRemoved key" + task.BelongToListKey + "count" + count);
                });

                _ = Task.Run(async () =>
                {
                    int count = 0;

                    // 耗时数据库操作
                    count = (int)StoreFactoryHolder.getTaskStore()
                        .getDeletedTasksCount(UserInfoHolder.getUserId());

                    // 在UI线程更新计数（使用 Dispatcher）
                    await dispatcherQueue.EnqueueAsync(() =>
                    {
                        TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(
                            SpecialTaskListConstants.Deleted, count);
                    });

                    LogService.Instance.Info("OnTaskRemoved key" + task.BelongToListKey + "count" + count);
                });

                TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentUncompletedTaskList(task.TaskID);
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
            }

            LogService.Instance.Info("OnTaskRemoved end;");
        }

        public async void OnTaskUnRemoved(TTask task)
        {
            try
            {
                DispatcherQueue dispatcherQueue = DispatcherQueue.GetForCurrentThread();

                await Task.Run(async () =>
                {
                    int count = 0;

                    // 耗时数据库操作
                    count = (int)StoreFactoryHolder.getTaskStore()
                        .getDeletedTasksCount(UserInfoHolder.getUserId());

                    // 在UI线程更新计数（使用 Dispatcher）
                    await dispatcherQueue.EnqueueAsync(() =>
                    {
                        TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(
                            SpecialTaskListConstants.Deleted, count);
                    });

                    LogService.Instance.Info("OnTaskUnRemoved key" + task.BelongToListKey + "count" + count);
                });

                await Task.Run(async () =>
                {
                    int count = 0;

                    // 耗时数据库操作
                    count =   StoreFactoryHolder.getTaskCategoryStore()
                        .GetCategoryUncompleteTaskCount(task.BelongToListKey, UserInfoHolder.getUserId());
                    
                    // 在UI线程更新计数（使用 Dispatcher）
                    await dispatcherQueue.EnqueueAsync(() =>
                    {
                        TheUICoordinator.Instance.UpdateCateogryUncompletedTaskCount(task.BelongToListKey, count);
                    });

                    LogService.Instance.Info("OnTaskUnRemoved key" + task.BelongToListKey + "count" + count);
                });

                if (SystemCategoryManager.IsNeedUpdateSystemCategory(task))
                    TheUICoordinator.Instance.UpdateSystemCategory();


                TheUICoordinator.Instance.RemoveTaskFromTaskPanelCurrentUncompletedTaskList(task.TaskID);
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
            }

            LogService.Instance.Info("OnTaskUnRemoved end;");
        }


        public async void MoveTaskToCategoryTop(long taskId)
        {
            try
            {
                var topTask = TheUICoordinator.Instance.GetTaskFromTaskPanelTop(taskId);

                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore()
                        .dragUncompleteTask(taskId, topTask.TaskID, UserInfoHolder.getUserId());
                    LogService.Instance.Info("db synced");
                });

                //TaskPanelMoveUncompletedTaskItemToTop(taskId);
                TheUICoordinator.Instance.RefreshTaskPanelUncompletedTaskListView();
                LogService.Instance.Info("ui synced");
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
            }
        }

        public async void MoveTaskToCategoryBottom(long taskId)
        {
            try
            {
                var bottomTask = TheUICoordinator.Instance.GetTaskFromTaskPanelBottom(taskId);

                await Task.Run(() =>
                {
                    StoreFactoryHolder.getTaskStore()
                        .dragUncompleteTask(taskId, bottomTask.TaskID, UserInfoHolder.getUserId());
                    LogService.Instance.Info("db synced");
                });

                //TaskPanelMoveUncompletedTaskItemToBottom(taskId);
                TheUICoordinator.Instance.RefreshTaskPanelUncompletedTaskListView();
                LogService.Instance.Info("ui synced");
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
            }
        }
    }
}