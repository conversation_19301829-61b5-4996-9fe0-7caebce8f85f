using System;
using System.Collections.Generic;
using HddtodoUI.Annotations;

namespace HddtodoUI.BackendModels.BackendStore
{
    public interface ITaskRestartStore
    {
        /// <summary>
        /// Create a new task restart
        /// </summary>
        TaskRestart CreateTaskRestart(long userId, CreateTaskRestartRequest request);

        /// <summary>
        /// Update an existing task restart
        /// </summary>
        bool UpdateTaskRestart(long userId, long restartId, UpdateTaskRestartRequest request);

        /// <summary>
        /// Delete a task restart
        /// </summary>
        bool DeleteTaskRestart(long userId, long restartId);

        /// <summary>
        /// Get task restart by restart ID
        /// </summary>
        [CanBeNull] TaskRestart GetTaskRestart(long restartId, long userId);

        /// <summary>
        /// Find task restart by task ID
        /// </summary>
        [CanBeNull] TaskRestart FindTaskRestartByTaskId(long taskId, long userId);
    }
}
