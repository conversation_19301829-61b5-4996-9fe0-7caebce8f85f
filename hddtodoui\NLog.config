<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwExceptions="false">

  <!-- 定义日志输出目标 -->
  <targets>
    <!-- 文件日志，记录所有日志 -->
    <target xsi:type="File" name="allfile" fileName="${specialfolder:folder=LocalApplicationData}/HddtodoUI/logs/all-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />

    <!-- 错误日志，只记录错误和致命错误 -->
    <target xsi:type="File" name="errorfile" fileName="${specialfolder:folder=LocalApplicationData}/HddtodoUI/logs/error-${shortdate}.log"
            layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring,Data:maxInnerExceptionLevel=10}" />

    <!-- 控制台输出 -->
    <target xsi:type="Console" name="console"
            layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}" />
  </targets>

  <!-- 定义日志规则 -->
  <rules>
    <!-- 所有日志写入allfile -->
    <logger name="*" minlevel="Info" writeTo="allfile" />
    
    <!-- 错误和致命错误写入errorfile -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />
    
    <!-- 调试和更高级别写入控制台 -->
    <logger name="*" minlevel="Debug" writeTo="console" />
  </rules>
</nlog>
