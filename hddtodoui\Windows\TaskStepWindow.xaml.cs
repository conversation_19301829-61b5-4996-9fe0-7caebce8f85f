using System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using Microsoft.UI;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;

namespace HddtodoUI.Windows
{
    public sealed partial class TaskStepWindow : Window
    {
        public TaskStepWindow()
        {
            this.InitializeComponent();
            WindowsUtils.NoTitleBar(this);
            WindowsUtils.ResizeWindow(this, 320, 400);
            WindowsUtils.SetAlwayOnTop(this);

            // 允许整窗拖动
            var dragHelper = new DragDropMoveWindowHelper(this);
            RootGrid.PointerPressed += dragHelper.Border_PointerPressed;
            RootGrid.PointerMoved += dragHelper.Border_PointerMoved;
            RootGrid.PointerReleased += dragHelper.Border_PointerReleased;

            // 右键菜单隐藏
            RootGrid.RightTapped += RootGrid_RightTapped;
        }

        private void RootGrid_RightTapped(object sender, RightTappedRoutedEventArgs e)
        {
            MenuFlyout mf = new MenuFlyout();
            MenuFlyoutItem hideItem = new MenuFlyoutItem { Text = "隐藏" };
            hideItem.Click += (s, args) => { this.AppWindow.Hide(); };
            mf.Items.Add(hideItem);
            mf.ShowAt(sender as UIElement, e.GetPosition(sender as UIElement));
        }

        /// <summary>
        /// 设置需要展示步骤的任务
        /// </summary>
        public void SetTask(TTask task)
        {
            if (task == null) return;
            var category = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());

            TodoTaskViewObject vo = category != null ?
                TodoTaskViewObject.GetFrom(task, category) :
                new TodoTaskViewObject { TaskID = task.TaskID, Title = task.Title, Task = task };

            StepsControl.SetParentTask(vo);
        }
    }
}
