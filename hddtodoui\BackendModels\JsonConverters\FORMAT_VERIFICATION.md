# DateTime 格式验证

## 当前使用的ISO 8601格式

```json
"firstSettingRestartTime": "2025-07-02T09:39:00.123+08:00"
```

## 格式分析

### 当前ISO 8601格式特点
- 格式：`yyyy-MM-dd'T'HH:mm:ss.fffzzz`
- 包含毫秒信息 (.fff)
- 包含时区信息 (zzz)
- 符合国际标准ISO 8601

### 统一格式

当前格式：`yyyy-MM-dd'T'HH:mm:ss.fffzzz`

示例输出：`2025-07-02T09:39:00.123+08:00`

逐部分对比：
- `yyyy` → `2025` ✓
- `MM` → `07` ✓
- `dd` → `02` ✓
- `'T'` → `T` ✓
- `HH` → `09` ✓
- `mm` → `39` ✓
- `ss` → `00` ✓
- `fff` → `123` ✓ (毫秒)
- `zzz` → `+08:00` ✓ (时区)

## 更改内容

已将所有DateTime转换器更新为使用ISO 8601格式：

1. **DateTimeJsonConverter.cs** - 更新为 `yyyy-MM-dd'T'HH:mm:ss.fffzzz`
2. **NullableDateTimeJsonConverter.cs** - 更新为 `yyyy-MM-dd'T'HH:mm:ss.fffzzz`
3. **JsonDateTimeStringConverter.cs** - 更新为 `yyyy-MM-dd'T'HH:mm:ss.fffzzz`

## 向后兼容性

转换器仍然支持解析旧格式：
- `yyyy-MM-dd'T'HH:mm:ss` (简化格式)
- `yyyy-MM-ddTHH:mm:ss.fffZ` (原UTC格式)
- `yyyy-MM-ddTHH:mm:ss.fff` (原本地格式)
- `yyyy-MM-ddTHH:mm:ss`
- `yyyy-MM-dd`

## 结论

**已完成ISO 8601格式升级！**

所有DateTime属性现在将输出符合国际标准的 `yyyy-MM-dd'T'HH:mm:ss.fffzzz` 格式，包含毫秒和时区信息，支持国际化应用。

### 格式优势
- ✅ 符合ISO 8601国际标准
- ✅ 包含完整的时区信息
- ✅ 支持毫秒精度
- ✅ 现代系统广泛支持
- ✅ 避免时区混淆问题
