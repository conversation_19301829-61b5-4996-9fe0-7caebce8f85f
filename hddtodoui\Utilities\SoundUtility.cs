using System;
using System.IO;
using Windows.Media.Core;
using Windows.Media.Playback;
using HddtodoUI.Services; // For LogService, if we want to keep similar logging

namespace HddtodoUI.Utilities
{
    public static class SoundUtility
    {
        private static MediaPlayer _mediaPlayer;

        static SoundUtility()
        {
            _mediaPlayer = new MediaPlayer();
        }

        public static void PlaySoundFile(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    LogService.Instance.Warn("PlaySoundFile: fileName is null or empty.");
                    return;
                }

                string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string assetsPath = Path.Combine(baseDirectory, "Assets", fileName);

                if (!File.Exists(assetsPath))
                {
                    LogService.Instance.Error($"PlaySoundFile: Sound file not found at {assetsPath}");
                    return;
                }

                _mediaPlayer.Source = MediaSource.CreateFromUri(new Uri(assetsPath));
                _mediaPlayer.Play();
            }
            catch (Exception ex)
            {
                LogService.Instance.Error($"PlaySoundFile: Error playing sound '{fileName}'. Exception: {ex.Message}");
            }
        }

        // Optional: Add a method to play a specific, commonly used sound directly
        public static void PlayNotificationSound()
        {
            PlaySoundFile("Notify.wav");
        }
    }
}
