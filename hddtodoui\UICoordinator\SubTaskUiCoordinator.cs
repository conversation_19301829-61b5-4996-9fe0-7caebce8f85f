using System;
using System.Threading.Tasks;
using ABI.Windows.System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.UICoordinator;

public class SubTaskUiCoordinator : TaskUIBaseCoordinator, ITaskUiCoordinator
{
    
    public async Task StartOrPauseButtonClick(TomatoTaskManager tomatoTaskManager)
    {
        try
        {
            var previousTomatoManager = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask();
            if (previousTomatoManager != null)
            {
                if (previousTomatoManager.getTaskID() != tomatoTaskManager.getTaskID())
                {
                    previousTomatoManager.tomatoPause();
                }

                //if previous task is a top task
                var itemControl = TheUICoordinator.Instance.GetMainWindow().GetMainView()
                    .GetTaskPanelTaskItemControlByTaskID(previousTomatoManager.getTaskID());
                if (itemControl != null)
                    itemControl.RefreshUI();


                if (previousTomatoManager.getTask().IsHasParent())
                {
                    //if previews task is a sub task and has the same parent task
                    if (IsCurrentTaskDetailWindowTask(previousTomatoManager.getTask().GetDirectParentTaskId()))
                    {
                        var preControl = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                            .GetSubTaskItemControlByTaskId(previousTomatoManager.getTask().TaskID);
                        if (preControl != null)
                            preControl.RefreshUI();
                    }
                }

                if (IsCurrentTaskDetailWindowTask(previousTomatoManager.getTask().TaskID))
                    TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

            }
            
            if (tomatoTaskManager.IsCompleted)
            {
                await tomatoTaskManager.uncompleteTask();
                TheUICoordinator.Instance.GetIndicatorWindow().RefreshTaskUI(tomatoTaskManager);
            }
            
            CurrentStatusHolder.getCurrentStatusHolder().setCurrentTomatoTask(tomatoTaskManager);
            tomatoTaskManager.tomatoSwitch();

            if (IsCurrentTaskDetailWindowTask(tomatoTaskManager.getTask().TaskID))
                TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

            var control = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                .GetSubTaskItemControlByTaskId(tomatoTaskManager.getTask().TaskID);
            if (control != null)
                control.RefreshUI();

            TheUICoordinator.Instance.GetIndicatorWindow().RefreshTaskUI(tomatoTaskManager);
            TheUICoordinator.Instance.ShowIndicatorWindow();
        } catch (Exception ex)
        {
            // Log the exception (replace with your actual logging mechanism)
            LogService.Instance.Error("Error in StartOrPauseButtonClick", ex);
            // Optionally, rethrow or handle specific exceptions as needed
            // Consider rethrowing if the caller needs to be aware of the failure:
            // throw;
        }
    }
    
    public void OnTaskAdded(TTask task, TaskCategory belongTaskList)
    {
        var parentTaskId = task.GetDirectParentTaskId();
        StoreFactoryHolder.getTaskStore().updateSubtaskCount(UserInfoHolder.getUserId(),parentTaskId);
        
        CheckIfUpdateTopSubTaskCounts(task);
    }

    public async void TaskSwitchComplete(TTask task)
    {
        try
        {
            var tomatoTaskManager = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
            await tomatoTaskManager.completeTask();
            
            var refreshedTask =  StoreFactoryHolder.getTaskStore().getTaskById(task.TaskID, UserInfoHolder.getUserId());
            TaskTomatoManagerFactory.RefreshTask(refreshedTask);

            if (IsCurrentTomatoTask(refreshedTask.TaskID))
            {
                //要处理indicatorform的变化
                TheUICoordinator.Instance.GetIndicatorWindow()
                    .RefreshTaskUI(CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask());
            }

            if (IsCurrentTaskDetailWindowTask(refreshedTask.TaskID))
                TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

            //判断是否taskdetailwindow在上一级父任务中,要处理变化
            if (IsCurrentTaskDetailWindowTask(refreshedTask.GetDirectParentTaskId()))
            {
                var control = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                    .GetSubTaskItemControlByTaskId(refreshedTask.TaskID);
                if (control != null)
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());
                    control.TaskVO = TodoTaskViewObject.GetFrom(refreshedTask,list);
                    control.RefreshUI();
                }
            }
            
            CheckIfUpdateTopSubTaskCounts(refreshedTask);
            
        }
        catch (Exception e)
        {
            LogService.Instance.Error(e.StackTrace);
        }
    }

    public async void TaskSwitchUnComplete(TTask task)
    {
        try
        {
            var tomatoTaskManager = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
            await tomatoTaskManager.uncompleteTask();

            var refreshedTask =  StoreFactoryHolder.getTaskStore().getTaskById(task.TaskID, UserInfoHolder.getUserId());
            TaskTomatoManagerFactory.RefreshTask(refreshedTask);
            //要处理indicatorform的变化
            if (IsCurrentTomatoTask(refreshedTask.TaskID))
                    TheUICoordinator.Instance.GetIndicatorWindow()
                        .RefreshTaskUI(CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask());
                

            //还要处理taskdetailform如果正好是当前编辑task，也要处理变化
            if (IsCurrentTaskDetailWindowTask(refreshedTask.TaskID))
                    TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().UpdateTimerControls();

            //判断是否taskdetailwindow在上一级父任务中,要处理变化
            if (IsCurrentTaskDetailWindowTask(refreshedTask.GetDirectParentTaskId()))
            {
                var control = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                    .GetSubTaskItemControlByTaskId(refreshedTask.TaskID);
                if (control != null)
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(refreshedTask.BelongToListKey, UserInfoHolder.getUserId());
                    control.TaskVO = TodoTaskViewObject.GetFrom(refreshedTask,list);
                    control.RefreshUI();
                }
            }
            
            CheckIfUpdateTopSubTaskCounts(refreshedTask);
          
           
        }
        catch (Exception e)
        {
            LogService.Instance.Error(e.StackTrace);
        }
    }

    private void CheckIfUpdateTopSubTaskCounts(TTask refreshedTask)
    {
        var parentTaskId = refreshedTask.GetDirectParentTaskId();
        StoreFactoryHolder.getTaskStore().updateSubtaskCount(UserInfoHolder.getUserId(),parentTaskId);
            
        var parentTask = StoreFactoryHolder.getTaskStore().getTaskById(parentTaskId,UserInfoHolder.getUserId());
        if (!parentTask.IsHasParent())
        {
            var itemControl = TheUICoordinator.Instance.GetMainWindow().GetMainView()
                .GetTaskPanelTaskItemControlByTaskID(parentTaskId);
            var list = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(parentTask.BelongToListKey, UserInfoHolder.getUserId());
            itemControl.TaskVO = TodoTaskViewObject.GetFrom(parentTask,list);
            if (itemControl != null)
                itemControl.RefreshUI();
        }
    }
    


    public void OnTaskModified(TTask task, TodoTaskViewObject previousTaskViewObject)
    {
        //throw new System.NotImplementedException();
        try
        {
            TaskTomatoManagerFactory.RefreshTask(task);
            
            var refreshedTask =  StoreFactoryHolder.getTaskStore().getTaskById(task.TaskID, UserInfoHolder.getUserId());
            TaskTomatoManagerFactory.RefreshTask(refreshedTask);
            if (IsCurrentTomatoTask(refreshedTask.TaskID))
                TheUICoordinator.Instance.GetIndicatorWindow()
                    .RefreshTaskUI(CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTask());

            //判断是否taskdetailwindow在上一级父任务中
            if (IsCurrentTaskDetailWindowTask(refreshedTask.GetDirectParentTaskId()))
            {
                var control = TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl()
                    .GetSubTaskItemControlByTaskId(refreshedTask.TaskID);
                if (control != null)
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(refreshedTask.BelongToListKey, UserInfoHolder.getUserId());
                    control.TaskVO = TodoTaskViewObject.GetFrom(refreshedTask,list);
                    control.RefreshUI();
                }
                    
            }

            //判断是否正好是当前taskdeailControl中的task
            if (IsCurrentTaskDetailWindowTask(task.TaskID))
            {
                
            }
        }
        catch (Exception e)
        {
            LogService.Instance.Debug(e.StackTrace);
        }
    }

    public void OnTaskDragMoveToCategory(TodoTaskViewObject taskViewObject, TaskCategory targetList)
    {
        throw new System.NotImplementedException();
    }

    public void OnTaskRemoved(TTask task)
    {
        throw new System.NotImplementedException();
    }

    public void OnTaskUnRemoved(TTask task)
    {
        throw new System.NotImplementedException();
    }

    public void MoveTaskToCategoryTop(long taskId)
    {
        throw new System.NotImplementedException();
    }

    public void MoveTaskToCategoryBottom(long taskId)
    {
        throw new System.NotImplementedException();
    }
}