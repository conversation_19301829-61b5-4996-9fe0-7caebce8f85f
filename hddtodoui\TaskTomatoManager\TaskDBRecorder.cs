using System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;

namespace HddtodoUI.TaskTomatoManager
{
    public class TaskDBRecorder
    {
        private TTask task;

        private long _randomId = 0;
        
        public TaskDBRecorder(TTask task)
        {
            this.task = task;
            _randomId = new Random().NextInt64();
        }

        public TTask getTask()
        {
            return task;
        }
        public long RandomId => _randomId;

        public void Refresh()
        {
            this.task = StoreFactoryHolder.getTaskStore().getTaskById(task.TaskID, UserInfoHolder.getUserId());
        }

        public TaskCategory getBelongToTaskList()
        {
            return StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());
        }

        public bool completeTask()
        {
           return StoreFactoryHolder.getTaskStore().switchTaskToCompleteList(this.task, UserInfoHolder.getUserId());
        }

        public bool unCompleteTask()
        {
           return StoreFactoryHolder.getTaskStore().switchTaskToUncompleteList(this.task, UserInfoHolder.getUserId());
        }

        public void moveTaskToOtherTaskList(TaskCategory target)
        {
            StoreFactoryHolder.getTaskStore().moveTaskToOtherTaskList(this.task, target, UserInfoHolder.getUserId());
        }

        public void deleteTask()
        {
            this.task.DeletedStatus = true;
            StoreFactoryHolder.getTaskStore().saveTaskChange(this.task, UserInfoHolder.getUserId());
        }
    }
}