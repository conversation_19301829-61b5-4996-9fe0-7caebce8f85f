using System;
using System.Reflection;
using System.Runtime.InteropServices;
using Windows.Graphics;
using ABI.Microsoft.UI.Xaml.Controls;
using Microsoft.UI;
using Microsoft.UI.Input;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using WinRT.Interop;
using WindowId = Windows.UI.WindowId;

namespace HddtodoUI.Utilities;

public static class WindowsUtils
{
    // Win32 API constants and methods
    private const int HWND_TOPMOST = -1;
    private const int SWP_NOMOVE = 0x0002;
    private const int SWP_NOSIZE = 0x0001;
    private const int SW_SHOW = 5;
    private const int SW_RESTORE = 9;

    [DllImport("user32.dll")]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy,
        uint uFlags);

    [DllImport("user32.dll")]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

    [DllImport("user32.dll")]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    public static void CenterWindow(object target)
    {
        var appWindow = GetAppWindow(target);
        CenterWindow(appWindow);
    }
    
    public static void CenterWindow(AppWindow appWindow)
    {
        var displayArea = DisplayArea.Primary;
        if (displayArea != null)
        {
            var centerX = (displayArea.WorkArea.Width - appWindow.Size.Width) / 2;
            var centerY = (displayArea.WorkArea.Height - appWindow.Size.Height) / 2;
            appWindow.Move(new PointInt32(centerX, centerY));
        }
    }
    
    public static void CenterWindow(object target, int BottomMargin)
    {
        var appWindow = GetAppWindow(target);

        var displayArea = DisplayArea.Primary;
        if (displayArea != null)
        {
            var centerX = (displayArea.WorkArea.Width - appWindow.Size.Width) / 2;
            var centerY = displayArea.WorkArea.Height - BottomMargin;
            appWindow.Move(new PointInt32(centerX, centerY));
        }
    }

    
    public static void NoTitleBar(object target)
    {
        var appWindow = GetAppWindow(target);
        var presenter = appWindow.Presenter as OverlappedPresenter;
        if (presenter != null)
        {
            presenter.SetBorderAndTitleBar(true, false);
        }
    }

    public static OverlappedPresenter getPresenter(object target)
    {
        var appWindow = GetAppWindow(target);
        var presenter = appWindow.Presenter as OverlappedPresenter;
        return presenter;
    }

    public static void RestoreWindow(object target)
    {
        var appWindow = GetAppWindow(target);
        var presenter = appWindow.Presenter as OverlappedPresenter;
        if (presenter != null)
        {
            presenter.Restore();
        }
    }

    public static void NoResizeWindow(object target)
    {
        var appWindow = GetAppWindow(target);
        var presenter = appWindow.Presenter as OverlappedPresenter;
        if (presenter != null)
        {
            presenter.IsResizable = false;
        }
    }



    public static void ResizeWindow(object target, int width, int height)
    {
        var appWindow = GetAppWindow(target);
        appWindow.Resize(new SizeInt32(width, height));
    }
    
    public static void ResizeWindow(AppWindow appWindow, int width, int height)
    {
        appWindow.Resize(new SizeInt32(width, height));
    }

    public static  Microsoft.UI.WindowId GetAppWindowID(object target)
    {
        IntPtr _windowHandle = WindowNative.GetWindowHandle(target);
        var windowId = Win32Interop.GetWindowIdFromWindow(_windowHandle);
        return windowId;
    }

    public static AppWindow GetAppWindow(object target)
    {
        IntPtr _windowHandle = WindowNative.GetWindowHandle(target);
        var windowId = Win32Interop.GetWindowIdFromWindow(_windowHandle);
        return AppWindow.GetFromWindowId(windowId);
    }

    public static AppWindow GetAppWindowFrom(XamlRoot root)
    {
        var parentId = root?.ContentIslandEnvironment?.AppWindowId;
        if (!parentId.HasValue)
            return null;

        var hwnd = Win32Interop.GetWindowFromWindowId(parentId.Value);
        // 通过 XamlRoot 的 Content（通常是窗口的根元素）获取原生窗口句柄（HWND）
               

        // 利用句柄获取窗口的唯一标识，再转换为 AppWindow 对象
        var windowId = Win32Interop.GetWindowIdFromWindow(hwnd);
        var aw = AppWindow.GetFromWindowId(windowId);

        return aw;
    }
    
    public static void setTopMostAndLeftCorner(object target)
    {
        IntPtr _hwnd = WindowNative.GetWindowHandle(target);
        SetWindowPos(_hwnd, new IntPtr(HWND_TOPMOST), 0, 0, 0, 0, SWP_NOSIZE);
    }

    public static void SetAlwayOnTop(object target)
    {
        var appWindow = GetAppWindow(target);

        //获取 presenter 并设置窗口始终在顶部
        var presenter = appWindow.Presenter as OverlappedPresenter;
        if (presenter != null)
        {
            presenter.IsAlwaysOnTop = true;
        }
    }

    public static void ShowWindowHandle(IntPtr hwnd)
    {
        ShowWindow(hwnd, SW_RESTORE);
    }

    public static void BringToForeground(IntPtr hwnd)
    {
        SetForegroundWindow(hwnd);
    }

    public static bool SetWindowFocus(object target)
    {
        if (target == null)
            return false;
            
        IntPtr hwnd = WindowNative.GetWindowHandle(target);
        return SetForegroundWindow(hwnd);
    }
    
    public static bool SetWindowFocus(AppWindow appWindow)
    {
        if (appWindow == null)
            return false;
            
        var windowId = appWindow.Id;
        IntPtr hwnd = Win32Interop.GetWindowFromWindowId(windowId);
        return SetForegroundWindow(hwnd);
    }

    public static void SetProtectedCursorByReflection(object sender, InputSystemCursor cursor)
    {
        if (sender is UIElement element)
        {
            var propertyInfo =
                typeof(UIElement).GetProperty("ProtectedCursor", BindingFlags.Instance | BindingFlags.NonPublic);
            if (propertyInfo != null)
            {
                propertyInfo.SetValue(element, cursor);
            }
        }
    }

    public static void SetProtectedCursorByReflection(UIElement element, InputSystemCursor cursor)
    {
        var propertyInfo =
            typeof(UIElement).GetProperty("ProtectedCursor", BindingFlags.Instance | BindingFlags.NonPublic);
        if (propertyInfo != null)
        {
            propertyInfo.SetValue(element, cursor);
        }
    }
}