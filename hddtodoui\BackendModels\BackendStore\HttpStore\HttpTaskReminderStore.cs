using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;

using System.Net;
using System.Text.Json.Serialization;
using HddtodoUI.Annotations;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskReminderStore : HttpStoreBase, ITaskReminderStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;

        public HttpTaskReminderStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter(),
                new JsonStringEnumConverter(JsonNamingPolicy.SnakeCaseUpper)
            }
        })
        {
        }

        public TaskReminder CreateReminder(long userId, TaskReminder reminder)
        {
            var endpoint = $"{BaseUrl}/reminders/{userId}";
            return SendPostRequestAsync<TaskReminder>(endpoint, "Creating reminder", reminder).Result;
        }

        public bool UpdateReminder(long userId, long reminderId, TaskReminder reminder)
        {
            var endpoint = $"{BaseUrl}/reminders/{userId}/{reminderId}";
            SendPutRequestAsync(endpoint, "Updating reminder", reminder).Wait();
            return true;
        }

        public bool DeleteReminder(long userId, long reminderId)
        {
            var endpoint = $"{BaseUrl}/reminders/{userId}/{reminderId}";
            SendDeleteRequestAsync(endpoint, "Deleting reminder").Wait();
            return true;
        }

        [CanBeNull]
        public TaskReminder FindReminderByTaskId(long userId, long taskId)
        {
            try
            {
                var endpoint = $"{BaseUrl}/reminders/task/{userId}/{taskId}";
                var response = SendGetRequestAsync<TaskReminder>(endpoint, "Finding reminder by task ID").Result;
                return response;
            }
            catch (AggregateException ex) when (ex.InnerException is HttpRequestException httpEx 
                && ((HttpRequestException)ex.InnerException).StatusCode == HttpStatusCode.NotFound)
            {
                return null;
            }
        }

        public List<TaskReminder> GetUpcomingReminders(long userId)
        {
            var endpoint = $"{BaseUrl}/reminders/upcoming/{userId}";
            return SendGetRequestAsync<List<TaskReminder>>(endpoint, "Getting upcoming reminders").Result;
        }
    }
}