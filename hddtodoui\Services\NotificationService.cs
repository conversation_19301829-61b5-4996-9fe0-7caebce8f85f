using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.UI.Dispatching;
using HddtodoUI.Windows;

namespace HddtodoUI.Services
{
    /// <summary>
    /// 通知服务，管理所有通知窗口
    /// </summary>
    public class NotificationService
    {
        private static NotificationService _instance;
        private readonly List<NotificationWindow> _activeNotifications = new List<NotificationWindow>();
        private readonly Dictionary<NotificationWindow, int> _notificationYPositions = new Dictionary<NotificationWindow, int>();
        private readonly int _spacing = 10; // 通知之间的间距
        private readonly int _screenMargin = 20; // 屏幕边缘的间距
        private readonly DispatcherQueue _dispatcherQueue;

        /// <summary>
        /// 获取通知服务的单例实例
        /// </summary>
        public static NotificationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new NotificationService();
                }
                return _instance;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        private NotificationService()
        {
            _dispatcherQueue = DispatcherQueue.GetForCurrentThread();
        }

        /// <summary>
        /// 显示文本通知
        /// </summary>
        public void ShowNotification(string content, string title = null, int duration = 5000)
        {
            ShowNotification(content, HddtodoUI.Windows.NotificationLevel.Info, title, duration);
        }

        /// <summary>
        /// 显示带级别的文本通知
        /// </summary>
        public void ShowNotification(string content, HddtodoUI.Windows.NotificationLevel level, string title = null, int duration = 5000)
        {
            try
            {
                _dispatcherQueue.TryEnqueue(() =>
                {
                    var notificationWindow = new NotificationWindow(content, level, title, duration);
                    RegisterNotification(notificationWindow);
                    notificationWindow.Show();
                });
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"显示通知时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示自定义内容通知
        /// </summary>
        public void ShowNotification(Microsoft.UI.Xaml.UIElement customContent, string title = null, int duration = 5000)
        {
            ShowNotification(customContent, HddtodoUI.Windows.NotificationLevel.Info, title, duration);
        }

        /// <summary>
        /// 显示带级别的自定义内容通知
        /// </summary>
        public void ShowNotification(Microsoft.UI.Xaml.UIElement customContent, HddtodoUI.Windows.NotificationLevel level, string title = null, int duration = 5000)
        {
            try
            {
                _dispatcherQueue.TryEnqueue(() =>
                {
                    var notificationWindow = new NotificationWindow(customContent, level, title, duration);
                    RegisterNotification(notificationWindow);
                    notificationWindow.Show();
                });
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"显示自定义通知时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 当通知窗口关闭时调用
        /// </summary>
        private void NotificationWindowOnClosed(object sender, Microsoft.UI.Xaml.WindowEventArgs e)
        {
            if (sender is NotificationWindow notification)
            {
                UnregisterNotification(notification);
            }
        }

        /// <summary>
        /// 注册一个通知窗口
        /// </summary>
        private void RegisterNotification(NotificationWindow notification)
        {
            try
            {
                // 添加到活动通知列表
                _activeNotifications.Add(notification);
                
                // 计算Y位置
                int yPosition = 0;
                if (_activeNotifications.Count > 1)
                {
                    // 获取前一个通知的Y位置和高度
                    var lastNotification = _activeNotifications[_activeNotifications.Count - 2];
                    yPosition = _notificationYPositions[lastNotification] + lastNotification.Height + _spacing;
                }
                
                // 保存Y位置
                _notificationYPositions[notification] = yPosition;
                
                // 注册关闭事件
                notification.Closed += NotificationWindowOnClosed;
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"注册通知窗口时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 注销一个通知窗口
        /// </summary>
        public void UnregisterNotification(NotificationWindow notification)
        {
            _activeNotifications.Remove(notification);
            _notificationYPositions.Remove(notification);
            RecalculatePositions();
            UpdateAllNotificationPositions();
        }

        /// <summary>
        /// 重新计算所有通知窗口的位置
        /// </summary>
        public void RecalculatePositions()
        {
            try
            {
                int currentY = 0;
                
                // 清理可能已关闭但未正确注销的窗口
                var invalidNotifications = _activeNotifications.Where(n => n == null).ToList();
                foreach (var invalid in invalidNotifications)
                {
                    _activeNotifications.Remove(invalid);
                    _notificationYPositions.Remove(invalid);
                }
                
                // 按照添加的顺序从下到上排列通知
                foreach (var notification in _activeNotifications.OrderBy(n => _activeNotifications.IndexOf(n)))
                {
                    try
                    {
                        _notificationYPositions[notification] = currentY;
                        currentY += notification.Height + _spacing;
                    }
                    catch (Exception ex)
                    {
                        LogService.Instance.Debug($"计算通知位置时出错: {ex.Message}");
                        // 如果访问高度属性失败，可能窗口已关闭但未正确注销，移除它
                        _activeNotifications.Remove(notification);
                        _notificationYPositions.Remove(notification);
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"重新计算所有通知位置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取通知窗口的Y位置
        /// </summary>
        public int GetNotificationYPosition(NotificationWindow notification)
        {
            if (_notificationYPositions.TryGetValue(notification, out int position))
            {
                return position;
            }
            return 0;
        }

        /// <summary>
        /// 通知窗口高度变化时调用
        /// </summary>
        public void NotifyHeightChanged(NotificationWindow notification)
        {
            RecalculatePositions();
            UpdateAllNotificationPositions();
        }

        /// <summary>
        /// 获取屏幕边缘的间距
        /// </summary>
        public int GetScreenMargin()
        {
            return _screenMargin;
        }

        /// <summary>
        /// 更新所有通知窗口的位置
        /// </summary>
        private void UpdateAllNotificationPositions()
        {
            try
            {
                // 创建一个临时列表，以防在迭代过程中集合被修改
                var notificationsToUpdate = new List<NotificationWindow>(_activeNotifications);
                
                foreach (var notification in notificationsToUpdate)
                {
                    try
                    {
                        // 检查窗口是否仍然有效
                        if (notification != null)
                        {
                            notification.UpdatePosition();
                        }
                    }
                    catch (Exception ex)
                    {
                        LogService.Instance.Debug($"更新通知位置时出错: {ex.Message}");
                        // 如果更新位置失败，可能窗口已关闭但未正确注销，尝试移除它
                        if (_activeNotifications.Contains(notification))
                        {
                            _activeNotifications.Remove(notification);
                            _notificationYPositions.Remove(notification);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"更新所有通知位置时出错: {ex.Message}");
            }
        }
    }
}
