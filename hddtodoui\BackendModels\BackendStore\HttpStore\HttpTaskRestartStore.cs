using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Net;
using System.Text.Json.Serialization;
using HddtodoUI.Annotations;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskRestartStore : HttpStoreBase, ITaskRestartStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;

        public HttpTaskRestartStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter(),
                new JsonStringEnumConverter(JsonNamingPolicy.SnakeCaseUpper)
            }
        })
        {
        }

        public TaskRestart CreateTaskRestart(long userId, CreateTaskRestartRequest request)
        {
            var endpoint = $"{BaseUrl}/task-restarts/create/{userId}";
            return SendPostRequestAsync<TaskRestart>(endpoint, "Creating task restart", request).Result;
        }

        public bool UpdateTaskRestart(long userId, long restartId, UpdateTaskRestartRequest request)
        {
            var endpoint = $"{BaseUrl}/task-restarts/update/{restartId}/{userId}";
            SendPutRequestAsync(endpoint, "Updating task restart", request).Wait();
            return true;
        }

        public bool DeleteTaskRestart(long userId, long restartId)
        {
            var endpoint = $"{BaseUrl}/task-restarts/delete/{restartId}/{userId}";
            SendDeleteRequestAsync(endpoint, "Deleting task restart").Wait();
            return true;
        }

        [CanBeNull]
        public TaskRestart GetTaskRestart(long restartId, long userId)
        {
            try
            {
                var endpoint = $"{BaseUrl}/task-restarts/{restartId}/{userId}";
                var response = SendGetRequestAsync<TaskRestart>(endpoint, "Getting task restart").Result;
                return response;
            }
            catch (AggregateException ex) when (ex.InnerException is HttpRequestException httpEx 
                && ((HttpRequestException)ex.InnerException).StatusCode == HttpStatusCode.NotFound)
            {
                return null;
            }
        }

        [CanBeNull]
        public TaskRestart FindTaskRestartByTaskId(long taskId, long userId)
        {
            try
            {
                var endpoint = $"{BaseUrl}/task-restarts/task/{taskId}/{userId}";
                var response = SendGetRequestAsync<TaskRestart>(endpoint, "Finding task restart by task ID").Result;
                return response;
            }
            catch (AggregateException ex) when (ex.InnerException is HttpRequestException httpEx 
                && ((HttpRequestException)ex.InnerException).StatusCode == HttpStatusCode.NotFound)
            {
                return null;
            }
        }
    }
}
