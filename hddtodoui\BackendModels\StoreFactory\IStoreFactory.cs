using HddtodoUI.BackendModels.BackendStore;

namespace HddtodoUI.BackendModels.StoreFactory;

public interface IStoreFactory
{
    ITaskStore getTaskStore();
    ITaskListStore getTaskListStore();
    ITaskTimeLogStore getTaskTimeLogStore();
    ITaskStepStore getTaskStepStore();
    IUserStore getUserStore();
    
    ITaskReminderStore getTaskReminderStore();
    
    ITaskRestartStore getTaskRestartStore();
    
    ITaskCategoryStore getTaskCategoryStore();
    
    ITaskActivityStore getTaskActivityStore();
}

public static class StoreFactoryHolder
{
    private static  IStoreFactory _storeFactory;
    public static IStoreFactory getStoreFactory()
    {
        if( _storeFactory == null )
            _storeFactory = new HttpStoreFactory();
        return _storeFactory;
    }

    public static ITaskStore getTaskStore()
    {
        return getStoreFactory().getTaskStore();
    }

    public static ITaskListStore getTaskListStore()
    {
        return getStoreFactory().getTaskListStore();
        
    }

    public static ITaskCategoryStore getTaskCategoryStore()
    {
        return getStoreFactory().getTaskCategoryStore();
    }

    public static ITaskTimeLogStore getTaskTimeLogStore()
    {
        return getStoreFactory().getTaskTimeLogStore();
        
    }

    public static ITaskStepStore getTaskStepStore()
    {
        return getStoreFactory().getTaskStepStore();
        
    }
    
    public static ITaskReminderStore getTaskReminderStore()
    {
        return getStoreFactory().getTaskReminderStore();
    }
    
    public static ITaskRestartStore getTaskRestartStore()
    {
        return getStoreFactory().getTaskRestartStore();
    }
    
    public static ITaskActivityStore getTaskActivityStore()
    {
        return getStoreFactory().getTaskActivityStore();
    }

    public static IUserStore getUserStore()
    {
        return getStoreFactory().getUserStore();
    }
}