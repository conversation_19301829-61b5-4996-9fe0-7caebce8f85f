using HddtodoUI.Services;
using HddtodoUI.UICoordinator;
using Microsoft.UI.Xaml;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using HddtodoUI.Utilities;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace HddtodoUI
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        private Window m_window;

        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            this.InitializeComponent();
            
            // 注册全局异常处理
            this.UnhandledException += App_UnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// 处理应用程序中未处理的异常
        /// </summary>
        private void App_UnhandledException(object sender, Microsoft.UI.Xaml.UnhandledExceptionEventArgs e)
        {
            e.Handled = true;
            LogUnhandledException("UI线程未处理异常", e.Exception);
            ShowExceptionNotification(e.Exception);
        }

        /// <summary>
        /// 处理Task中未观察到的异常
        /// </summary>
        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            e.SetObserved();
            LogUnhandledException("Task未观察到的异常", e.Exception);
            ShowExceptionNotification(e.Exception);
        }

        /// <summary>
        /// 处理AppDomain中未处理的异常
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            LogUnhandledException("AppDomain未处理异常", e.ExceptionObject as Exception);
            if (e.ExceptionObject is Exception ex)
            {
                ShowExceptionNotification(ex);
            }
        }

        /// <summary>
        /// 记录未处理的异常到日志
        /// </summary>
        private void LogUnhandledException(string source, Exception exception)
        {
            try
            {
                var logService = LogService.Instance;
                logService.Fatal($"{source}: {exception?.Message}", exception);

                // 记录详细的异常信息
                if (exception != null)
                {
                    LogService.Instance.Error($"未处理异常: {exception.Message}");
                    LogService.Instance.Error($"异常类型: {exception.GetType().FullName}");
                    LogService.Instance.Error($"堆栈跟踪: {exception.StackTrace}");

                    // 记录内部异常
                    var innerException = exception.InnerException;
                    while (innerException != null)
                    {
                        LogService.Instance.Error($"内部异常: {innerException.Message}");
                        LogService.Instance.Error($"内部异常类型: {innerException.GetType().FullName}");
                        LogService.Instance.Error($"内部异常堆栈跟踪: {innerException.StackTrace}");
                        innerException = innerException.InnerException;
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Error($"记录未处理异常时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示异常通知
        /// </summary>
        private void ShowExceptionNotification(Exception exception)
        {
            string msg = ExceptionDisplayHelper.GetFullExceptionDetails(exception);
            try
            {
                // 使用NotificationService显示异常信息
                NotificationService.Instance.ShowNotification(
                    $"应用程序发生错误: {msg}", 
                    Windows.NotificationLevel.Danger, 
                    "应用程序错误", 
                    10000);
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"显示异常通知时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// Invoked when the application is launched.
        /// </summary>
        /// <param name="args">Details about the launch request and process.</param>
        protected override void OnLaunched(LaunchActivatedEventArgs args)
        {
            // 显示登录窗口而不是主窗口
            m_window = TheUICoordinator.Instance.GetLoginWindow();
            m_window.Activate();
        }
    }
}