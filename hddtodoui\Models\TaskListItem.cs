using HddtodoUI.BackendModels.BackendStore;

namespace HddtodoUI.Models
{
    /// <summary>
    /// 统一的任务列表项，用于在同一个ListView中显示任务和子分类
    /// </summary>
    public class TaskListItem
    {
        public enum ItemType
        {
            Task,
            SubCategory
        }

        /// <summary>
        /// 项目类型
        /// </summary>
        public ItemType Type { get; set; }

        /// <summary>
        /// 任务对象（当Type为Task时使用）
        /// </summary>
        public TodoTaskViewObject Task { get; set; }

        /// <summary>
        /// 子分类对象（当Type为SubCategory时使用）
        /// </summary>
        public TaskCategoryWithCount SubCategory { get; set; }

        /// <summary>
        /// 分组键，用于ListView分组
        /// </summary>
        public string GroupKey { get; set; }

        /// <summary>
        /// 分组显示名称
        /// </summary>
        public string GroupName { get; set; }

        /// <summary>
        /// 创建任务项
        /// </summary>
        public static TaskListItem CreateTaskItem(TodoTaskViewObject task)
        {
            return new TaskListItem
            {
                Type = ItemType.Task,
                Task = task,
                GroupKey = "Tasks",
                GroupName = "任务"
            };
        }

        /// <summary>
        /// 创建子分类项
        /// </summary>
        public static TaskListItem CreateSubCategoryItem(TaskCategoryWithCount subCategory)
        {
            return new TaskListItem
            {
                Type = ItemType.SubCategory,
                SubCategory = subCategory,
                GroupKey = "SubCategories",
                GroupName = "子分类"
            };
        }
    }
}
