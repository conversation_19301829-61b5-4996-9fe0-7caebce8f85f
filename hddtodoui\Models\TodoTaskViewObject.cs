using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using HddtodoUI.BackendModels;
using Microsoft.UI.Xaml;

namespace HddtodoUI.Models
{
    public class TodoTaskViewObject
    {
        private bool _isCompleted;

        public TodoTaskViewObject()
        {
            // 默认状态为未开始
            Status = TaskStatus.NotStarted;
        }

        public long TaskID { get; set; }
        public string Title { get; set; }
        public DateTime? DueDate { get; set; }
        public string Category { get; set; }
        
        public string CategoryName { get; set; }
        public TaskPriority Priority { get; set; }
        public string Notes { get; set; }

        // 任务开始时间
        public DateTime? StartTime { get; set; }

        // 任务状态
        public TaskStatus Status { get; set; }

        public long TaskOrder { get; set; }

        public long GroupOrder { get; set; }
        
        public string Color { get; set; }

        public TTask Task { get; set; }
        
        public bool DeletedStatus { get; set; }
        
        public String ParentTaskIds { get; set; }

        public int DirectSubTaskCount { get; set; }
        
        public bool IsCompleted
        {
            get => CompletedDate != null;
        }
        
        public String CategoryOrderPath { get; set; }

        public DateTime? CompletedDate { get; set; }

        public Visibility DueDateVisibility 
        {
            get => DueDate.HasValue ? Visibility.Visible : Visibility.Collapsed;
        }


        // public static TodoTaskViewObject GetFrom(TTask tt, TaskList belongToList)
        // {
        //     var todoTask = new TodoTaskViewObject();
        //     todoTask.TaskID = tt.TaskID;
        //     todoTask.Title = tt.Title;
        //     todoTask.DueDate = tt.TaskDueTime;
        //     todoTask.Category = tt.BelongToListKey;
        //     todoTask.Priority = tt.Priority;
        //     todoTask.Notes = tt.TaskRemark;
        //     todoTask.CompletedDate = tt.TaskCompleteTime;
        //     todoTask.StartTime = tt.TaskCreatTime;
        //     todoTask.TaskOrder = tt.TaskOrder;
        //     todoTask.GroupOrder = belongToList.ListOrder;
        //     todoTask.Color = tt.Color;
        //     todoTask.DeletedStatus = tt.DeletedStatus;
        //     todoTask.ParentTaskIds = tt.ParentTaskIDs;
        //     todoTask.DirectSubTaskCount = tt.DirectSubTaskCount;
        //     
        //     todoTask.Task = tt;
        //
        //     return todoTask;
        // }
        
        public static TodoTaskViewObject GetFrom(TTask tt, TaskCategory belongToList)
        {
            var todoTask = new TodoTaskViewObject();
            todoTask.TaskID = tt.TaskID;
            todoTask.Title = tt.Title;
            todoTask.DueDate = tt.TaskDueTime;
            todoTask.Category = tt.BelongToListKey;
            todoTask.CategoryName = belongToList.Name;
            todoTask.Priority = tt.Priority;
            todoTask.Notes = tt.TaskRemark;
            todoTask.CompletedDate = tt.TaskCompleteTime;
            todoTask.StartTime = tt.TaskCreatTime;
            todoTask.TaskOrder = tt.TaskOrder;
            todoTask.GroupOrder = belongToList.CategoryOrder;
            todoTask.Color = tt.Color;
            todoTask.DeletedStatus = tt.DeletedStatus;
            todoTask.ParentTaskIds = tt.ParentTaskIDs;
            todoTask.DirectSubTaskCount = tt.DirectSubTaskCount;
            
            todoTask.Task = tt;

            return todoTask;
        }

        public String GetDirectSubTaskInfo()
        {
            return $"有{DirectSubTaskCount} 个未完成的子任务";
        }
        
        public List<long> GetParentTaskIds()
        {
            return Task.GetParentTaskIds();
        }
        
        public String MakeMySelfAParentTaskIdsString()
        {
            return Task.MakeMySelfAParentTaskIdsString();
        }
        
        public long GetDirectParentTaskId()
        {
            return Task.GetDirectParentTaskId();
        }
    }
}
       