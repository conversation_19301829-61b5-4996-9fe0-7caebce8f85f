<?xml version="1.0" encoding="utf-8"?>
<ContentDialog
    x:Class="HddtodoUI.Controls.TaskReminderDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:HddtodoUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="设置任务提醒"
    PrimaryButtonText="保存"
    SecondaryButtonText="取消"
    DefaultButton="Primary"
    CloseButtonText="删除提醒"
    CloseButtonClick="ContentDialog_CloseButtonClick">

    <Grid RowSpacing="12" Width="400">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 提醒时间设置 -->
        <StackPanel Grid.Row="0" Spacing="8">
            <TextBlock Text="提醒时间" Style="{StaticResource BodyStrongTextBlockStyle}" />
            <DatePicker x:Name="ReminderDatePicker" HorizontalAlignment="Stretch" SelectedDateChanged="ReminderDatePicker_OnSelectedDateChanged"/>
            <TimePicker x:Name="ReminderTimePicker" HorizontalAlignment="Stretch" SelectedTimeChanged="ReminderTimePicker_OnSelectedTimeChanged"/>
        </StackPanel>

        <!-- 重复类型设置 -->
        <StackPanel Grid.Row="1" Spacing="8">
            <TextBlock Text="重复类型" Style="{StaticResource BodyStrongTextBlockStyle}"/>
            <ComboBox x:Name="RepeatTypeComboBox" HorizontalAlignment="Stretch" SelectionChanged="RepeatTypeComboBox_SelectionChanged">
                <ComboBoxItem Content="一次性" Tag="OneTime"/>
                <ComboBoxItem Content="每小时" Tag="Hourly"/>
                <ComboBoxItem Content="每天" Tag="Daily"/>
                <ComboBoxItem Content="每周" Tag="Weekly"/>
                <ComboBoxItem Content="每月" Tag="Monthly"/>
                <ComboBoxItem Content="每年" Tag="Yearly"/>
            </ComboBox>
        </StackPanel>

        <!-- 重复间隔设置 -->
        <StackPanel Grid.Row="2" Spacing="8" x:Name="RepeatIntervalPanel" Visibility="Collapsed">
            <TextBlock Text="重复间隔" Style="{StaticResource BodyStrongTextBlockStyle}"/>
            <NumberBox x:Name="RepeatIntervalNumberBox" Minimum="1" Value="1" SpinButtonPlacementMode="Inline" HorizontalAlignment="Stretch" ValueChanged="RepeatIntervalNumberBox_OnValueChanged"/>
        </StackPanel>

        <!-- 提示信息 -->
        <TextBlock Grid.Row="3" x:Name="NextReminderInfoTextBlock" TextWrapping="Wrap" Style="{StaticResource CaptionTextBlockStyle}" Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
    </Grid>
</ContentDialog>
