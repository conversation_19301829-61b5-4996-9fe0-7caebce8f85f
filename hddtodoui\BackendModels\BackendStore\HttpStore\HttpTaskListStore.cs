using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskListStore : HttpStoreBase, ITaskListStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;

        public HttpTaskListStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter()
            }
        })
        {
        }

        public List<TaskList> getUncompleteTaskListsSource(long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/uncomplete/{userId}";
            return SendGetRequestAsync<List<TaskList>>(endpoint, "Getting uncomplete task lists").Result;
        }

        public List<TaskListWithCount> getUncompleteTaskListsSourceWithCount(long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/uncomplete-with-count/{userId}";
            return SendGetRequestAsync<List<TaskListWithCount>>(endpoint, "Getting uncomplete task lists with counts").Result;
        }
        
        public List<TaskList> GetCompletedTaskListsPaged(long userId, int page, int pageSize)
        {
            var endpoint = $"{BaseUrl}/tasklists/completed/paged/{userId}?page={page}&pageSize={pageSize}";
            return SendGetRequestAsync<List<TaskList>>(endpoint, "Getting paged complete task lists").Result;
        }

        public long GetCompletedTaskListCount(long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/completed/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting completed task list count").Result;
        }

        public TaskList getTaskListByKey(string key, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/{Uri.EscapeDataString(key)}/{userId}";
            return SendGetRequestAsync<TaskList>(endpoint, "Getting task list by key").Result;
        }

        public TaskList createEmptyTaskList(string listName, string key, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/empty/{Uri.EscapeDataString(listName)}/{Uri.EscapeDataString(key)}/{userId}";
            return SendPostRequestAsync<TaskList>(endpoint, "Creating empty task list", null).Result;
        }

        public long getTaskListCount(long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting task list count").Result;
        }

        public bool hasTaskListByKey(string key, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/exists/{Uri.EscapeDataString(key)}/{userId}";
            return SendGetRequestAsync<bool>(endpoint, "Checking task list existence").Result;
        }

        public void removeTaskList(TaskList taskList, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/{userId}";
            SendDeleteRequestAsync(endpoint, "Removing task list", taskList).Wait();
        }

        public TaskList saveTaskListChange(TaskList taskList, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/{userId}";
            return SendPutRequestAsync<TaskList>(endpoint, "Saving task list changes", taskList).Result;
        }

        public void InsertTaskListTo(TaskList from, TaskList target, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklists/insert/{from.Key}/{target.Key}/{userId}";
            SendPutRequestAsync(endpoint, "Inserting task list", null).Wait();
        }

        public long getTaskListUnCompleteCount(string key, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklistuncompletecount/{Uri.EscapeDataString(key)}/uncomplete/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting uncomplete task count").Result;
        }

        public long getTaskListCompleteCount(string key, long userId)
        {
            var endpoint = $"{BaseUrl}/tasklistcompletecount/{Uri.EscapeDataString(key)}/complete/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting complete task count").Result;
        }

        public Task<List<TaskList>> GetTaskListsByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false)
        {
            var queryParams = new List<string>();
            
            if (parentKey != null)
            {
                queryParams.Add($"parentKey={HttpUtility.UrlEncode(parentKey)}");
            }
            
            if (includeDeleted)
            {
                queryParams.Add("includeDeleted=true");
            }

            var queryString = queryParams.Count > 0 ? $"?{string.Join("&", queryParams)}" : string.Empty;
            var endpoint = $"{BaseUrl}/tasklists/{userId}{queryString}";
            
            return SendGetRequestAsync<List<TaskList>>(endpoint, "Getting task lists by parent key");
        }

        public Task<List<TaskListWithCount>> GetTaskListsWithCountByParentKeyAsync(long userId, string parentKey = null, bool includeDeleted = false)
        {
            var queryParams = new List<string>();
            
            if (parentKey != null)
            {
                queryParams.Add($"parentKey={HttpUtility.UrlEncode(parentKey)}");
            }
            
            if (includeDeleted)
            {
                queryParams.Add("includeDeleted=true");
            }

            var queryString = queryParams.Count > 0 ? $"?{string.Join("&", queryParams)}" : string.Empty;
            var endpoint = $"{BaseUrl}/tasklists/with-count/{userId}{queryString}";
            
            return SendGetRequestAsync<List<TaskListWithCount>>(endpoint, "Getting task lists with count by parent key");
        }
    }
}
