using System;
using HddtodoUI.BackendModels;

namespace HddtodoUI.UICoordinator;

public class TaskUICoordinatorFactory
{
    private static readonly TaskUICoordinator _taskUIBaseCoordinator = new TaskUICoordinator();
    private static readonly SubTaskUiCoordinator SubTaskUiCoordinator = new SubTaskUiCoordinator();

    private static TaskUICoordinator GetTaskUIBaseCoordinator()
    {
        return _taskUIBaseCoordinator;
    }

    private static SubTaskUiCoordinator GetSubUICoordinator()
    {
        return SubTaskUiCoordinator;
    }

    public static ITaskUiCoordinator Instance(TTask task)
    {
        if (task.IsHasParent())
            return GetSubUICoordinator();
        else
            return GetTaskUIBaseCoordinator();
    }
    
}