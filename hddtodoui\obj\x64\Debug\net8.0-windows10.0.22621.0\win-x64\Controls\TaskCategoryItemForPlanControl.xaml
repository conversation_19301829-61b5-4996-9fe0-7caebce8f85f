﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TaskCategoryItemForPlanControl"
    x:Name="root"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:backend="using:HddtodoUI.BackendModels"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <converters:DateTimeToBrushConverter x:Key="DateTimeToBrushConverter" />
        <converters:IntToVisibilityConverter x:Key="IntToVisibilityConverter" />
        <Style x:Key="ProjectTitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextBlockStyle}">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>
    </UserControl.Resources>

    <Grid x:ConnectionId='2' Name="RootGrid" Padding="12,8" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
          CornerRadius="8"                         >
        <Grid.ContextFlyout>
            <MenuFlyout>
                <MenuFlyoutItem x:ConnectionId='3' Text="修改截至时间"                                    />
                <MenuFlyoutItem x:ConnectionId='4' Text="完成该项目"                                      />
            </MenuFlyout>
        </Grid.ContextFlyout>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
     
        <StackPanel Grid.Column="0" Orientation="Vertical" Margin="16,0,0,0" VerticalAlignment="Center" >
            <TextBlock x:ConnectionId='6' x:Name="CategoryNameTextBlock"
           TextWrapping="Wrap"
                                                     
           Style="{StaticResource TitleTextStyle}"
            />
            <TextBlock Text="{Binding ParentPath, ElementName=root}"
                       Foreground="Gray"
                       FontSize="12"
                       Margin="0,2,0,0"
                       Visibility="{Binding ParentPath, ElementName=root, Converter={StaticResource StringToVisibilityConverter}}" />
            <TextBlock Foreground="DodgerBlue"
                       FontSize="13"
                       Margin="0,4,0,0"
                       FontWeight="SemiBold"
                       HorizontalAlignment="Left">
                <Run Text="未完成任务数: "/>
                <Run x:ConnectionId='9'                                       />
            </TextBlock>
            <TextBlock x:ConnectionId='7' Foreground="Gray"
                       FontSize="13"
                       Margin="8,4,0,0"
                       FontWeight="Normal"
                       HorizontalAlignment="Left"
                                                                                                               >
                <Run x:ConnectionId='8'                                           />
                <Run Text="个子分类"/>
            </TextBlock>
        </StackPanel>
        <TextBlock x:ConnectionId='5' Grid.Column="1"
                                                                                                                      
                   Margin="16,0,0,0" VerticalAlignment="Center"
                   Foreground="{Binding Category.CategoryDueTime, ElementName=root, Converter={StaticResource DateTimeToBrushConverter}}" />
    </Grid>
</UserControl>

