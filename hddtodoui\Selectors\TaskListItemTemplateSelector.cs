using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using HddtodoUI.Models;

namespace HddtodoUI.Selectors
{
    /// <summary>
    /// 任务列表项模板选择器，根据项目类型选择不同的DataTemplate
    /// </summary>
    public class TaskListItemTemplateSelector : DataTemplateSelector
    {
        /// <summary>
        /// 任务项模板
        /// </summary>
        public DataTemplate TaskTemplate { get; set; }

        /// <summary>
        /// 子分类项模板
        /// </summary>
        public DataTemplate SubCategoryTemplate { get; set; }

        protected override DataTemplate SelectTemplateCore(object item)
        {
            if (item is TaskListItem taskListItem)
            {
                return taskListItem.Type switch
                {
                    TaskListItem.ItemType.Task => TaskTemplate,
                    TaskListItem.ItemType.SubCategory => SubCategoryTemplate,
                    _ => TaskTemplate
                };
            }

            return TaskTemplate;
        }

        protected override DataTemplate SelectTemplateCore(object item, DependencyObject container)
        {
            return SelectTemplateCore(item);
        }
    }
}
