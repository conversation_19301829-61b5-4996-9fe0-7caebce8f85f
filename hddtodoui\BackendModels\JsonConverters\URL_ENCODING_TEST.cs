using System;
using HddtodoUI.Utilities;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// URL编码测试工具类
    /// 用于验证DateTime格式的URL编码是否正确
    /// </summary>
    public static class UrlEncodingTest
    {
        /// <summary>
        /// 测试DateTime的URL编码
        /// </summary>
        public static void TestDateTimeUrlEncoding()
        {
            // 测试用例1: 标准时间
            var testDate1 = new DateTime(2025, 7, 3, 23, 59, 59, 999, DateTimeKind.Local);
            TestSingleDateTime(testDate1, "标准时间测试");

            // 测试用例2: 零点时间
            var testDate2 = new DateTime(2025, 7, 3, 0, 0, 0, 0, DateTimeKind.Local);
            TestSingleDateTime(testDate2, "零点时间测试");

            // 测试用例3: UTC时间
            var testDate3 = new DateTime(2025, 7, 3, 15, 59, 59, 999, DateTimeKind.Utc);
            TestSingleDateTime(testDate3, "UTC时间测试");
        }

        /// <summary>
        /// 测试单个DateTime的编码
        /// </summary>
        private static void TestSingleDateTime(DateTime dateTime, string testName)
        {
            Console.WriteLine($"\n=== {testName} ===");
            Console.WriteLine($"输入DateTime: {dateTime} (Kind: {dateTime.Kind})");

            // 使用JsonDateTimeStringConverter转换
            var dateString = JsonDateTimeStringConverter.Convert(dateTime);
            Console.WriteLine($"格式化字符串: {dateString}");

            // URL编码
            var encodedString = Uri.EscapeDataString(dateString);
            Console.WriteLine($"URL编码结果: {encodedString}");

            // 验证关键字符的编码
            ValidateEncoding(dateString, encodedString);

            // 生成完整的测试URL
            var testUrl = $"http://localhost:9300/api/tasks/uncomplete/due/count/1?date={encodedString}";
            Console.WriteLine($"完整URL: {testUrl}");

            // URL解码验证
            var decodedString = Uri.UnescapeDataString(encodedString);
            Console.WriteLine($"解码验证: {decodedString}");
            Console.WriteLine($"编码往返正确: {dateString == decodedString}");
        }

        /// <summary>
        /// 验证关键字符的编码是否正确
        /// </summary>
        private static void ValidateEncoding(string original, string encoded)
        {
            Console.WriteLine("关键字符编码验证:");

            // 检查冒号 :
            if (original.Contains(":"))
            {
                bool colonEncoded = encoded.Contains("%3A");
                Console.WriteLine($"  冒号(:) 编码: {(colonEncoded ? "✅ 正确(%3A)" : "❌ 错误")}");
            }

            // 检查加号 +
            if (original.Contains("+"))
            {
                bool plusEncoded = encoded.Contains("%2B");
                Console.WriteLine($"  加号(+) 编码: {(plusEncoded ? "✅ 正确(%2B)" : "❌ 错误")}");
            }

            // 检查点号 .
            if (original.Contains("."))
            {
                bool dotPreserved = encoded.Contains(".");
                Console.WriteLine($"  点号(.) 保持: {(dotPreserved ? "✅ 正确" : "❌ 错误")}");
            }
        }

        /// <summary>
        /// 测试问题场景：模拟+号丢失的情况
        /// </summary>
        public static void TestPlusSignIssue()
        {
            Console.WriteLine("\n=== 加号丢失问题测试 ===");

            var originalDate = "2025-07-03T23:59:59.999+08:00";
            Console.WriteLine($"原始字符串: {originalDate}");

            // 正确的URL编码
            var correctEncoded = Uri.EscapeDataString(originalDate);
            Console.WriteLine($"正确编码: {correctEncoded}");

            // 模拟错误：直接在URL中使用未编码的字符串
            var incorrectUrl = $"http://localhost:9300/api/tasks/uncomplete/due/count/1?date={originalDate}";
            Console.WriteLine($"错误URL: {incorrectUrl}");

            // 模拟服务器接收到的（+被解释为空格）
            var receivedByServer = originalDate.Replace("+", " ");
            Console.WriteLine($"服务器接收到: {receivedByServer}");

            Console.WriteLine("\n解决方案:");
            Console.WriteLine("1. 确保使用 Uri.EscapeDataString() 进行编码");
            Console.WriteLine("2. 检查HTTP客户端是否自动进行URL编码");
            Console.WriteLine("3. 验证完整的请求URL");
        }

        /// <summary>
        /// 生成标准的API测试URL
        /// </summary>
        public static string GenerateTestUrl(DateTime dateTime, string baseUrl = "http://localhost:9300", long userId = 1)
        {
            var dateString = JsonDateTimeStringConverter.Convert(dateTime);
            var encodedDate = Uri.EscapeDataString(dateString);
            return $"{baseUrl}/api/tasks/uncomplete/due/count/{userId}?date={encodedDate}";
        }
    }
}
