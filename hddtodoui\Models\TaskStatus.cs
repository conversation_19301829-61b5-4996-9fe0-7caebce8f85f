using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.Models
{
    public enum TaskStatus
    {
        NotStarted,
        InProgress,
        Completed,
        Paused
    }

    public static class TaskStatusExtensions
    {
        public static TaskStatus GetTaskStatus(TomatoTaskStaus value)
        {
            return value switch
            {
                TomatoTaskStaus.taskCompleted => TaskStatus.Completed,
                TomatoTaskStaus.taskUnCompletedAndTomatoOngoing => TaskStatus.InProgress,
                TomatoTaskStaus.taskUnCompleteAndNotTomatoStart => TaskStatus.NotStarted,
                _ => TaskStatus.NotStarted
            };
        }
    }
}