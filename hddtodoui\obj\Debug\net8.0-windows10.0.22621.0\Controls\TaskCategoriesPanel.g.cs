﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoui\Controls\TaskCategoriesPanel.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "43A4299C88E4EA4E3FCFDE8483C2E308D22901ACC5152D53C3C7C8D6C3B33F2D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskCategoriesPanel : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_ContentDialog_IsPrimaryButtonEnabled(global::Microsoft.UI.Xaml.Controls.ContentDialog obj, global::System.Boolean value)
            {
                obj.IsPrimaryButtonEnabled = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TreeView_ItemsSource(global::Microsoft.UI.Xaml.Controls.TreeView obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                obj.ItemsSource = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_UIElement_Visibility(global::Microsoft.UI.Xaml.UIElement obj, global::Microsoft.UI.Xaml.Visibility value)
            {
                obj.Visibility = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::Microsoft.UI.Xaml.Media.Brush value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::Microsoft.UI.Xaml.Media.Brush) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::Microsoft.UI.Xaml.Media.Brush), targetNullValue);
                }
                obj.Foreground = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskCategoriesPanel_obj13_Bindings :
            global::Microsoft.UI.Xaml.IDataTemplateExtension,
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskCategoriesPanel_Bindings
        {
            private global::HddtodoUI.Models.TaskListViewObject dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private global::Microsoft.UI.Xaml.ResourceDictionary localResources;
            private global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement> converterLookupRoot;
            private bool removedDataContextHandler = false;

            // Fields for each control that has bindings.
            private global::System.WeakReference obj13;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj17;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj18;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj19;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj17TextDisabled = false;
            private static bool isobj17VisibilityDisabled = false;
            private static bool isobj17ForegroundDisabled = false;
            private static bool isobj18TextDisabled = false;
            private static bool isobj19TextDisabled = false;

            public TaskCategoriesPanel_obj13_Bindings()
            {
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 88 && columnNumber == 48)
                {
                    isobj17TextDisabled = true;
                }
                else if (lineNumber == 90 && columnNumber == 47)
                {
                    isobj17VisibilityDisabled = true;
                }
                else if (lineNumber == 91 && columnNumber == 47)
                {
                    isobj17ForegroundDisabled = true;
                }
                else if (lineNumber == 81 && columnNumber == 52)
                {
                    isobj18TextDisabled = true;
                }
                else if (lineNumber == 84 && columnNumber == 51)
                {
                    isobj19TextDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 13: // Controls\TaskCategoriesPanel.xaml line 60
                        this.obj13 = new global::System.WeakReference(global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeViewItem>(target));
                        break;
                    case 17: // Controls\TaskCategoriesPanel.xaml line 88
                        this.obj17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 18: // Controls\TaskCategoriesPanel.xaml line 81
                        this.obj18 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 19: // Controls\TaskCategoriesPanel.xaml line 83
                        this.obj19 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            public void DataContextChangedHandler(global::Microsoft.UI.Xaml.FrameworkElement sender, global::Microsoft.UI.Xaml.DataContextChangedEventArgs args)
            {
                 if (this.SetDataRoot(args.NewValue))
                 {
                    this.Update();
                 }
            }

            // IDataTemplateExtension

            public bool ProcessBinding(uint phase)
            {
                throw new global::System.NotImplementedException();
            }

            public int ProcessBindings(global::Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs args)
            {
                int nextPhase = -1;
                ProcessBindings(args.Item, args.ItemIndex, (int)args.Phase, out nextPhase);
                return nextPhase;
            }

            public void ResetTemplate()
            {
                Recycle();
            }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
                switch(phase)
                {
                    case 0:
                        nextPhase = -1;
                        this.SetDataRoot(item);
                        if (!removedDataContextHandler)
                        {
                            removedDataContextHandler = true;
                            var rootElement = (this.obj13.Target as global::Microsoft.UI.Xaml.Controls.TreeViewItem);
                            if (rootElement != null)
                            {
                                rootElement.DataContextChanged -= this.DataContextChangedHandler;
                            }
                        }
                        this.initialized = true;
                        break;
                }
                this.Update_(global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListViewObject>(item), 1 << phase);
            }

            public void Recycle()
            {
            }

            // ITaskCategoriesPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Models.TaskListViewObject>(newDataRoot);
                    return true;
                }
                return false;
            }
            public void SetConverterLookupRoot(global::Microsoft.UI.Xaml.FrameworkElement rootElement)
            {
                this.converterLookupRoot = new global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement>(rootElement);
            }

            public global::Microsoft.UI.Xaml.Data.IValueConverter LookupConverter(string key)
            {
                if (this.localResources == null)
                {
                    global::Microsoft.UI.Xaml.FrameworkElement rootElement;
                    this.converterLookupRoot.TryGetTarget(out rootElement);
                    this.localResources = rootElement.Resources;
                    this.converterLookupRoot = null;
                }
                return (global::Microsoft.UI.Xaml.Data.IValueConverter) (this.localResources.ContainsKey(key) ? this.localResources[key] : global::Microsoft.UI.Xaml.Application.Current.Resources[key]);
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Models.TaskListViewObject obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | (1 << 0))) != 0)
                    {
                        this.Update_DueDate(obj.DueDate, phase);
                        this.Update_Name(obj.Name, phase);
                        this.Update_TaskCount(obj.TaskCount, phase);
                    }
                }
            }
            private void Update_DueDate(global::System.Nullable<global::System.DateTime> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskCategoriesPanel.xaml line 88
                    if (!isobj17TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj17, (global::System.String)this.LookupConverter("DateTimeConverter").Convert(obj, typeof(global::System.String), null, null), null);
                    }
                    // Controls\TaskCategoriesPanel.xaml line 88
                    if (!isobj17VisibilityDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_UIElement_Visibility(this.obj17, (global::Microsoft.UI.Xaml.Visibility)this.LookupConverter("HasDateVisibilityConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Visibility), null, null));
                    }
                    // Controls\TaskCategoriesPanel.xaml line 88
                    if (!isobj17ForegroundDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(this.obj17, (global::Microsoft.UI.Xaml.Media.Brush)this.LookupConverter("DueDateColorConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Media.Brush), null, null), null);
                    }
                }
            }
            private void Update_Name(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskCategoriesPanel.xaml line 81
                    if (!isobj18TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj18, obj, null);
                    }
                }
            }
            private void Update_TaskCount(global::System.Int64 obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskCategoriesPanel.xaml line 83
                    if (!isobj19TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj19, obj.ToString(), null);
                    }
                }
            }
        }

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskCategoriesPanel_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskCategoriesPanel_Bindings
        {
            private global::HddtodoUI.Controls.TaskCategoriesPanel dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.ContentDialog obj4;
            private global::Microsoft.UI.Xaml.Controls.TreeView obj11;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj4IsPrimaryButtonEnabledDisabled = false;
            private static bool isobj11ItemsSourceDisabled = false;

            private TaskCategoriesPanel_obj1_BindingsTracking bindingsTracking;

            public TaskCategoriesPanel_obj1_Bindings()
            {
                this.bindingsTracking = new TaskCategoriesPanel_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 133 && columnNumber == 23)
                {
                    isobj4IsPrimaryButtonEnabledDisabled = true;
                }
                else if (lineNumber == 57 && columnNumber == 23)
                {
                    isobj11ItemsSourceDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 4: // Controls\TaskCategoriesPanel.xaml line 128
                        this.obj4 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                        break;
                    case 11: // Controls\TaskCategoriesPanel.xaml line 55
                        this.obj11 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeView>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITaskCategoriesPanel_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskCategoriesPanel>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TaskCategoriesPanel obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_(obj);
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_IsAddListNameValid(obj.IsAddListNameValid, phase);
                    }
                    if ((phase & (NOT_PHASED | (1 << 0))) != 0)
                    {
                        this.Update_UserCategories(obj.UserCategories, phase);
                    }
                }
            }
            private void Update_IsAddListNameValid(global::System.Boolean obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskCategoriesPanel.xaml line 128
                    if (!isobj4IsPrimaryButtonEnabledDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ContentDialog_IsPrimaryButtonEnabled(this.obj4, obj);
                    }
                }
            }
            private void Update_UserCategories(global::System.Collections.ObjectModel.ObservableCollection<global::HddtodoUI.Models.TaskListViewObject> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED )) != 0)
                {
                    // Controls\TaskCategoriesPanel.xaml line 55
                    if (!isobj11ItemsSourceDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TreeView_ItemsSource(this.obj11, obj, null);
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskCategoriesPanel_obj1_BindingsTracking
            {
                private global::System.WeakReference<TaskCategoriesPanel_obj1_Bindings> weakRefToBindingObj; 

                public TaskCategoriesPanel_obj1_BindingsTracking(TaskCategoriesPanel_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskCategoriesPanel_obj1_Bindings>(obj);
                }

                public TaskCategoriesPanel_obj1_Bindings TryGetBindingObject()
                {
                    TaskCategoriesPanel_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_(null);
                }

                public void PropertyChanged_(object sender, global::System.ComponentModel.PropertyChangedEventArgs e)
                {
                    TaskCategoriesPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        string propName = e.PropertyName;
                        global::HddtodoUI.Controls.TaskCategoriesPanel obj = sender as global::HddtodoUI.Controls.TaskCategoriesPanel;
                        if (global::System.String.IsNullOrEmpty(propName))
                        {
                            if (obj != null)
                            {
                                bindings.Update_IsAddListNameValid(obj.IsAddListNameValid, DATA_CHANGED);
                            }
                        }
                        else
                        {
                            switch (propName)
                            {
                                case "IsAddListNameValid":
                                {
                                    if (obj != null)
                                    {
                                        bindings.Update_IsAddListNameValid(obj.IsAddListNameValid, DATA_CHANGED);
                                    }
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    }
                }
                public void UpdateChildListeners_(global::HddtodoUI.Controls.TaskCategoriesPanel obj)
                {
                    TaskCategoriesPanel_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        if (bindings.dataRoot != null)
                        {
                            ((global::System.ComponentModel.INotifyPropertyChanged)bindings.dataRoot).PropertyChanged -= PropertyChanged_;
                        }
                        if (obj != null)
                        {
                            bindings.dataRoot = obj;
                            ((global::System.ComponentModel.INotifyPropertyChanged)obj).PropertyChanged += PropertyChanged_;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TaskCategoriesPanel.xaml line 30
                {
                    this.SystemTasksListView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ListView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ListView)this.SystemTasksListView).SelectionChanged += this.TasksListView_SelectionChanged;
                }
                break;
            case 3: // Controls\TaskCategoriesPanel.xaml line 54
                {
                    this.UserListsScrollViewer = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ScrollViewer>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ScrollViewer)this.UserListsScrollViewer).Loaded += this.FrameworkElement_OnLoaded;
                    ((global::Microsoft.UI.Xaml.Controls.ScrollViewer)this.UserListsScrollViewer).ViewChanged += this.ScrollViewer_OnViewChanged;
                }
                break;
            case 4: // Controls\TaskCategoriesPanel.xaml line 128
                {
                    this.AddListDialog = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)this.AddListDialog).PrimaryButtonClick += this.AddListDialog_PrimaryButtonClick;
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)this.AddListDialog).CloseButtonClick += this.AddListDialog_CloseButtonClick;
                }
                break;
            case 5: // Controls\TaskCategoriesPanel.xaml line 137
                {
                    this.ListNameTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBox)this.ListNameTextBox).TextChanged += this.ListNameTextBox_TextChanged;
                }
                break;
            case 6: // Controls\TaskCategoriesPanel.xaml line 142
                {
                    this.ListPriorityComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 7: // Controls\TaskCategoriesPanel.xaml line 151
                {
                    this.ListDueDatePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CalendarDatePicker>(target);
                }
                break;
            case 8: // Controls\TaskCategoriesPanel.xaml line 112
                {
                    global::Microsoft.UI.Xaml.Controls.Button element8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)element8).Click += this.AddListButton_Click;
                }
                break;
            case 9: // Controls\TaskCategoriesPanel.xaml line 117
                {
                    this.ManageCompletedListsButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.ManageCompletedListsButton).Click += this.ManageCompletedListsButton_Click;
                }
                break;
            case 10: // Controls\TaskCategoriesPanel.xaml line 102
                {
                    this.BottomHint = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            case 11: // Controls\TaskCategoriesPanel.xaml line 55
                {
                    this.UserTasksTreeView = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TreeView>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserTasksTreeView).SelectionChanged += this.UserTasksTreeView_OnSelectionChanged;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserTasksTreeView).Expanding += this.UserTasksTreeView_Expanding;
                    ((global::Microsoft.UI.Xaml.Controls.TreeView)this.UserTasksTreeView).Collapsed += this.UserTasksTreeView_Collapsed;
                }
                break;
            case 14: // Controls\TaskCategoriesPanel.xaml line 63
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element14 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element14).Click += this.AddTaskMenuItem_Click;
                }
                break;
            case 15: // Controls\TaskCategoriesPanel.xaml line 64
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element15 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element15).Click += this.EditTitleMenuItem_Click;
                }
                break;
            case 16: // Controls\TaskCategoriesPanel.xaml line 66
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element16 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element16).Click += this.CompleteThisListMenuItem_Click;
                }
                break;
            case 20: // Controls\TaskCategoriesPanel.xaml line 50
                {
                    this.TopHint = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TaskCategoriesPanel.xaml line 2
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TaskCategoriesPanel_obj1_Bindings bindings = new TaskCategoriesPanel_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            case 13: // Controls\TaskCategoriesPanel.xaml line 60
                {                    
                    global::Microsoft.UI.Xaml.Controls.TreeViewItem element13 = (global::Microsoft.UI.Xaml.Controls.TreeViewItem)target;
                    TaskCategoriesPanel_obj13_Bindings bindings = new TaskCategoriesPanel_obj13_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(element13.DataContext);
                    bindings.SetConverterLookupRoot(this);
                    element13.DataContextChanged += bindings.DataContextChangedHandler;
                    global::Microsoft.UI.Xaml.DataTemplate.SetExtensionInstance(element13, bindings);
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element13, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

