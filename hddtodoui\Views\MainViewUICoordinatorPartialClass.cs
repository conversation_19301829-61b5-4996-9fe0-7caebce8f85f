using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using HddtodoUI.Controls;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;

namespace HddtodoUI.Views;

public partial class MainView
{
    
    public void RefreshTaskPanelUncompletedTaskListTask(TTask task)
    {
        TasksPanel.RefreshTaskListTask(task);
    }
        
    public void TaskPanelMoveUncompletedTaskToTop(long taskId)
    {
        TasksPanel.MoveUncompletedTaskToTop(taskId);
    }
        
    public void TaskPanelMoveUncompletedTaskToBottom(long taskId)
    {
        TasksPanel.MoveUncompletedTaskToBottom(taskId);
    }
        
    public void RemoveTaskFormUncompletedTaskPanel(long taskId)
    {
        TasksPanel.RemoveUncompletedTask(taskId);
    }

    public void RemoveTaskFormCompletedTaskPanel(long taskId)
    {
        TasksPanel.RemoveCompletedTask(taskId);
    }

    public void RemoveUserCategory(string key)
    {
        TaskCategoriesPanel.RemoveUserCategory(key);
    }

    public void AddTaskToTaskPanelUnCompletedTaskListLast(TTask ttask)
    {
        TasksPanel.AddTaskToUncompletedTaskListLast(ttask);
    }

    public void AddTaskToTaskPanelCurrentCompletedTaskListFirst(TTask ttask)
    {
        TasksPanel.AddTaskToCompletedTaskListFirst(ttask);
    }
        

    public void UpdateUserCategoryUncompletedTaskCount(string key, int count)
    {
        TaskCategoriesPanel.UpdateUserCategoryUncompletedTaskCount(key, count);
    }
    
    public TaskItemControl GetTaskPanelTaskItemControlByTaskID(long taskId)
    {
        return TasksPanel.GetTaskItemControlByTaskID(taskId);
    }
    
    public TodoTaskViewObject GetTaskFromTaskPanelCategoryTop(long taskId)
    {
        return TasksPanel.GetTaskFromCategoryTop(taskId);
    }
        
    public TodoTaskViewObject GetTaskFromTaskPanelCategoryBottom(long taskId)
    {
        return TasksPanel.GetTaskFromCategoryBottom(taskId);
    }

    public void SetTaskCategoryPanelListViewCanReorder()
    {
        TaskCategoriesPanel.SetTaskCategoryListViewCanReOrder();
    }

    public void SetTaskCategoryPanelListViewCanNotReorder()
    {
        TaskCategoriesPanel.SetTaskCategoryListViewCanotReOrder();
    }

    public void TaskCategoryPanelAddChildUserCategoryToParent(string parentKey, TaskCategoryViewObject chldCategory)
    {
        TaskCategoriesPanel.AddChildUserCategoryToParent(parentKey, chldCategory);
    }
    
    public void TaskPanelAddCompletedTaskCategoryToCompletedCategoriesView(TaskCategoryViewObject taskCategoryViewObject)
    {
        TasksPanel.AddCompletedTaskCategoryToCompletedCategoriesView(taskCategoryViewObject);
    }
    
    public async void SwitchToUserCategory(TaskCategoryViewObject category)
    {
        try
        {
             await TaskCategoriesPanel.SwitchToUserCategory(category);
        }catch(Exception e)
        {
            LogService.Instance.Error(e.Message,e);
        }
        
    }
    
    public bool UpdateUserCategory(String categoryKey, TaskCategoryViewObject updatedCategory)
    {
        return TaskCategoriesPanel.UpdateUserCategory(categoryKey, updatedCategory);
    }
    
    public void RemoveUserTaskCategoryFromPanel(TaskCategoryViewObject category)
    {
        TaskCategoriesPanel.RemoveUserTaskCategoryFromPanel(category);
    }
}