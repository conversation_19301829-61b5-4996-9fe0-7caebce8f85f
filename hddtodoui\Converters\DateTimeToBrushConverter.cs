using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;
using System;
using HddtodoUI.Helpers;

namespace HddtodoUI.Converters
{
    public class DateTimeToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is DateTime dd)
            {
                return TaskCategoryGroupingHelper.GetBrushByDateTime(dd);
            }
            return new SolidColorBrush(Microsoft.UI.Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
