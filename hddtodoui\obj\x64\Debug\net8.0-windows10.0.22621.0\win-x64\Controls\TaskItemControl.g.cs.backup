﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TaskItemControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "BE78616B81B1BCE80C7582397920F4FA2CBE4C08D20629B33F1F94601F45CB88"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskItemControl : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private static class XamlBindingSetters
        {
            public static void Set_Microsoft_UI_Xaml_Controls_ToolTipService_ToolTip(global::Microsoft.UI.Xaml.DependencyObject obj, global::System.Object value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::System.Object) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::System.Object), targetNullValue);
                }
                global::Microsoft.UI.Xaml.Controls.ToolTipService.SetToolTip(obj, value);
            }
            public static void Set_Microsoft_UI_Xaml_Controls_FontIcon_Glyph(global::Microsoft.UI.Xaml.Controls.FontIcon obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Glyph = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_IconElement_Foreground(global::Microsoft.UI.Xaml.Controls.IconElement obj, global::Microsoft.UI.Xaml.Media.Brush value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::Microsoft.UI.Xaml.Media.Brush) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::Microsoft.UI.Xaml.Media.Brush), targetNullValue);
                }
                obj.Foreground = value;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::System.String value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = targetNullValue;
                }
                obj.Text = value ?? global::System.String.Empty;
            }
            public static void Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(global::Microsoft.UI.Xaml.Controls.TextBlock obj, global::Microsoft.UI.Xaml.Media.Brush value, string targetNullValue)
            {
                if (value == null && targetNullValue != null)
                {
                    value = (global::Microsoft.UI.Xaml.Media.Brush) global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.ConvertValue(typeof(global::Microsoft.UI.Xaml.Media.Brush), targetNullValue);
                }
                obj.Foreground = value;
            }
        };

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private partial class TaskItemControl_obj1_Bindings :
            global::Microsoft.UI.Xaml.Markup.IDataTemplateComponent,
            global::Microsoft.UI.Xaml.Markup.IXamlBindScopeDiagnostics,
            global::Microsoft.UI.Xaml.Markup.IComponentConnector,
            ITaskItemControl_Bindings
        {
            private global::HddtodoUI.Controls.TaskItemControl dataRoot;
            private bool initialized = false;
            private const int NOT_PHASED = (1 << 31);
            private const int DATA_CHANGED = (1 << 30);
            private global::Microsoft.UI.Xaml.ResourceDictionary localResources;
            private global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement> converterLookupRoot;

            // Fields for each control that has bindings.
            private global::Microsoft.UI.Xaml.Controls.Button obj30;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj31;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj32;
            private global::Microsoft.UI.Xaml.Controls.TextBlock obj33;
            private global::Microsoft.UI.Xaml.Controls.FontIcon obj34;

            // Static fields for each binding's enabled/disabled state
            private static bool isobj30ToolTipDisabled = false;
            private static bool isobj31TextDisabled = false;
            private static bool isobj32TextDisabled = false;
            private static bool isobj32ForegroundDisabled = false;
            private static bool isobj33TextDisabled = false;
            private static bool isobj34GlyphDisabled = false;
            private static bool isobj34ForegroundDisabled = false;

            private TaskItemControl_obj1_BindingsTracking bindingsTracking;

            public TaskItemControl_obj1_Bindings()
            {
                this.bindingsTracking = new TaskItemControl_obj1_BindingsTracking(this);
            }

            public void Disable(int lineNumber, int columnNumber)
            {
                if (lineNumber == 185 && columnNumber == 17)
                {
                    isobj30ToolTipDisabled = true;
                }
                else if (lineNumber == 196 && columnNumber == 28)
                {
                    isobj31TextDisabled = true;
                }
                else if (lineNumber == 203 && columnNumber == 32)
                {
                    isobj32TextDisabled = true;
                }
                else if (lineNumber == 205 && columnNumber == 32)
                {
                    isobj32ForegroundDisabled = true;
                }
                else if (lineNumber == 208 && columnNumber == 52)
                {
                    isobj33TextDisabled = true;
                }
                else if (lineNumber == 186 && columnNumber == 23)
                {
                    isobj34GlyphDisabled = true;
                }
                else if (lineNumber == 188 && columnNumber == 23)
                {
                    isobj34ForegroundDisabled = true;
                }
            }

            // IComponentConnector

            public void Connect(int connectionId, global::System.Object target)
            {
                switch(connectionId)
                {
                    case 30: // Controls\TaskItemControl.xaml line 176
                        this.obj30 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                        break;
                    case 31: // Controls\TaskItemControl.xaml line 194
                        this.obj31 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 32: // Controls\TaskItemControl.xaml line 202
                        this.obj32 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 33: // Controls\TaskItemControl.xaml line 208
                        this.obj33 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                        break;
                    case 34: // Controls\TaskItemControl.xaml line 186
                        this.obj34 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                        break;
                    default:
                        break;
                }
            }
                        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
                        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
                        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target) 
                        {
                            return null;
                        }

            // IDataTemplateComponent

            public void ProcessBindings(global::System.Object item, int itemIndex, int phase, out int nextPhase)
            {
                nextPhase = -1;
            }

            public void Recycle()
            {
                return;
            }

            // ITaskItemControl_Bindings

            public void Initialize()
            {
                if (!this.initialized)
                {
                    this.Update();
                }
            }
            
            public void Update()
            {
                this.Update_(this.dataRoot, NOT_PHASED);
                this.initialized = true;
            }

            public void StopTracking()
            {
                this.bindingsTracking.ReleaseAllListeners();
                this.initialized = false;
            }

            public void DisconnectUnloadedObject(int connectionId)
            {
                throw new global::System.ArgumentException("No unloadable elements to disconnect.");
            }

            public bool SetDataRoot(global::System.Object newDataRoot)
            {
                this.bindingsTracking.ReleaseAllListeners();
                if (newDataRoot != null)
                {
                    this.dataRoot = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskItemControl>(newDataRoot);
                    return true;
                }
                return false;
            }

            public void Activated(object obj, global::Microsoft.UI.Xaml.WindowActivatedEventArgs data)
            {
                this.Initialize();
            }

            public void Loading(global::Microsoft.UI.Xaml.FrameworkElement src, object data)
            {
                this.Initialize();
            }
            public void SetConverterLookupRoot(global::Microsoft.UI.Xaml.FrameworkElement rootElement)
            {
                this.converterLookupRoot = new global::System.WeakReference<global::Microsoft.UI.Xaml.FrameworkElement>(rootElement);
            }

            public global::Microsoft.UI.Xaml.Data.IValueConverter LookupConverter(string key)
            {
                if (this.localResources == null)
                {
                    global::Microsoft.UI.Xaml.FrameworkElement rootElement;
                    this.converterLookupRoot.TryGetTarget(out rootElement);
                    this.localResources = rootElement.Resources;
                    this.converterLookupRoot = null;
                }
                return (global::Microsoft.UI.Xaml.Data.IValueConverter) (this.localResources.ContainsKey(key) ? this.localResources[key] : global::Microsoft.UI.Xaml.Application.Current.Resources[key]);
            }

            private delegate void InvokeFunctionDelegate(int phase);
            private global::System.Collections.Generic.Dictionary<string, InvokeFunctionDelegate> PendingFunctionBindings = new global::System.Collections.Generic.Dictionary<string, InvokeFunctionDelegate>();

            private void Invoke_TaskVO_M_GetDirectSubTaskInfo_371857150(int phase)
            {
                global::System.String result = this.dataRoot.TaskVO.GetDirectSubTaskInfo();
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskItemControl.xaml line 208
                    if (!isobj33TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj33, result, null);
                    }
                }
            }

            private void CompleteUpdate(int phase)
            {
                var functions = this.PendingFunctionBindings;
                this.PendingFunctionBindings = new global::System.Collections.Generic.Dictionary<string, InvokeFunctionDelegate>();
                foreach (var function in functions.Values)
                {
                    function.Invoke(phase);
                }
            }

            // Update methods for each path node used in binding steps.
            private void Update_(global::HddtodoUI.Controls.TaskItemControl obj, int phase)
            {
                this.bindingsTracking.UpdateChildListeners_(obj);
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_TaskVO(obj.TaskVO, phase);
                    }
                }
                this.CompleteUpdate(phase);
            }
            private void Update_TaskVO(global::HddtodoUI.Models.TodoTaskViewObject obj, int phase)
            {
                if (obj != null)
                {
                    if ((phase & (NOT_PHASED | DATA_CHANGED | (1 << 0))) != 0)
                    {
                        this.Update_TaskVO_Status(obj.Status, phase);
                        this.Update_TaskVO_Title(obj.Title, phase);
                        this.Update_TaskVO_DueDate(obj.DueDate, phase);
                        this.Update_TaskVO_M_GetDirectSubTaskInfo_371857150(phase);
                    }
                }
            }
            private void Update_TaskVO_Status(global::HddtodoUI.Models.TaskStatus obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskItemControl.xaml line 176
                    if (!isobj30ToolTipDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_ToolTipService_ToolTip(this.obj30, (global::System.Object)this.LookupConverter("TaskStatusTooltipConverter").Convert(obj, typeof(global::System.Object), null, null), null);
                    }
                    // Controls\TaskItemControl.xaml line 186
                    if (!isobj34GlyphDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_FontIcon_Glyph(this.obj34, (global::System.String)this.LookupConverter("TaskStatusIconConverter").Convert(obj, typeof(global::System.String), null, null), null);
                    }
                    // Controls\TaskItemControl.xaml line 186
                    if (!isobj34ForegroundDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_IconElement_Foreground(this.obj34, (global::Microsoft.UI.Xaml.Media.Brush)this.LookupConverter("TaskStatusColorConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Media.Brush), null, null), null);
                    }
                }
            }
            private void Update_TaskVO_Title(global::System.String obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskItemControl.xaml line 194
                    if (!isobj31TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj31, obj, null);
                    }
                }
            }
            private void Update_TaskVO_DueDate(global::System.Nullable<global::System.DateTime> obj, int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    // Controls\TaskItemControl.xaml line 202
                    if (!isobj32TextDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Text(this.obj32, (global::System.String)this.LookupConverter("DateTimeConverter").Convert(obj, typeof(global::System.String), null, null), null);
                    }
                    // Controls\TaskItemControl.xaml line 202
                    if (!isobj32ForegroundDisabled)
                    {
                        XamlBindingSetters.Set_Microsoft_UI_Xaml_Controls_TextBlock_Foreground(this.obj32, (global::Microsoft.UI.Xaml.Media.Brush)this.LookupConverter("DueDateColorConverter").Convert(obj, typeof(global::Microsoft.UI.Xaml.Media.Brush), null, null), null);
                    }
                }
            }
            private void Update_TaskVO_M_GetDirectSubTaskInfo_371857150(int phase)
            {
                if ((phase & ((1 << 0) | NOT_PHASED | DATA_CHANGED)) != 0)
                {
                    if (!isobj33TextDisabled)
                    {
                        this.PendingFunctionBindings["TaskVO_M_GetDirectSubTaskInfo_371857150"] = new InvokeFunctionDelegate(this.Invoke_TaskVO_M_GetDirectSubTaskInfo_371857150); 
                    }
                }
            }

            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            private class TaskItemControl_obj1_BindingsTracking
            {
                private global::System.WeakReference<TaskItemControl_obj1_Bindings> weakRefToBindingObj; 

                public TaskItemControl_obj1_BindingsTracking(TaskItemControl_obj1_Bindings obj)
                {
                    weakRefToBindingObj = new global::System.WeakReference<TaskItemControl_obj1_Bindings>(obj);
                }

                public TaskItemControl_obj1_Bindings TryGetBindingObject()
                {
                    TaskItemControl_obj1_Bindings bindingObject = null;
                    if (weakRefToBindingObj != null)
                    {
                        weakRefToBindingObj.TryGetTarget(out bindingObject);
                        if (bindingObject == null)
                        {
                            weakRefToBindingObj = null;
                            ReleaseAllListeners();
                        }
                    }
                    return bindingObject;
                }

                public void ReleaseAllListeners()
                {
                    UpdateChildListeners_(null);
                }

                public void DependencyPropertyChanged_TaskVO(global::Microsoft.UI.Xaml.DependencyObject sender, global::Microsoft.UI.Xaml.DependencyProperty prop)
                {
                    TaskItemControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        global::HddtodoUI.Controls.TaskItemControl obj = sender as global::HddtodoUI.Controls.TaskItemControl;
                        if (obj != null)
                        {
                            bindings.Update_TaskVO(obj.TaskVO, DATA_CHANGED);
                        }
                        bindings.CompleteUpdate(DATA_CHANGED);
                    }
                }
                private long tokenDPC_TaskVO = 0;
                public void UpdateChildListeners_(global::HddtodoUI.Controls.TaskItemControl obj)
                {
                    TaskItemControl_obj1_Bindings bindings = TryGetBindingObject();
                    if (bindings != null)
                    {
                        if (bindings.dataRoot != null)
                        {
                            bindings.dataRoot.UnregisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskItemControl.TaskVOProperty, tokenDPC_TaskVO);
                        }
                        if (obj != null)
                        {
                            bindings.dataRoot = obj;
                            tokenDPC_TaskVO = obj.RegisterPropertyChangedCallback(global::HddtodoUI.Controls.TaskItemControl.TaskVOProperty, DependencyPropertyChanged_TaskVO);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TaskItemControl.xaml line 24
                {
                    this.RootGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 3: // Controls\TaskItemControl.xaml line 27
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element3 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element3).Click += this.EditButton_Click;
                }
                break;
            case 4: // Controls\TaskItemControl.xaml line 32
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element4 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element4).Click += this.CopyTaskTitleButton_Click;
                }
                break;
            case 5: // Controls\TaskItemControl.xaml line 149
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element5 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element5).Click += this.DeleteButton_Click;
                }
                break;
            case 6: // Controls\TaskItemControl.xaml line 137
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element6 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element6).Click += this.SetMoveToTop;
                }
                break;
            case 7: // Controls\TaskItemControl.xaml line 142
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element7 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element7).Click += this.SetMoveToBotton;
                }
                break;
            case 8: // Controls\TaskItemControl.xaml line 72
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element8 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element8).Click += this.SetColorPink_Click;
                }
                break;
            case 9: // Controls\TaskItemControl.xaml line 77
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element9 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element9).Click += this.SetColorOrange_Click;
                }
                break;
            case 10: // Controls\TaskItemControl.xaml line 82
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element10 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element10).Click += this.SetColorGreen_Click;
                }
                break;
            case 11: // Controls\TaskItemControl.xaml line 87
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element11 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element11).Click += this.SetColorBlue_Click;
                }
                break;
            case 12: // Controls\TaskItemControl.xaml line 92
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element12 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element12).Click += this.SetColorPurple_Click;
                }
                break;
            case 13: // Controls\TaskItemControl.xaml line 97
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element13 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element13).Click += this.SetColorBrown_Click;
                }
                break;
            case 14: // Controls\TaskItemControl.xaml line 102
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element14 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element14).Click += this.SetColorGold_Click;
                }
                break;
            case 15: // Controls\TaskItemControl.xaml line 107
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element15 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element15).Click += this.SetColorBlack_Click;
                }
                break;
            case 16: // Controls\TaskItemControl.xaml line 112
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element16 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element16).Click += this.SetColorOlive_Click;
                }
                break;
            case 17: // Controls\TaskItemControl.xaml line 117
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element17 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element17).Click += this.SetColorTeal_Click;
                }
                break;
            case 18: // Controls\TaskItemControl.xaml line 122
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element18 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element18).Click += this.SetColorTomato_Click;
                }
                break;
            case 19: // Controls\TaskItemControl.xaml line 127
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element19 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element19).Click += this.SetColorNone_Click;
                }
                break;
            case 20: // Controls\TaskItemControl.xaml line 52
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element20 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element20).Click += this.SetPriorityHigh_Click;
                }
                break;
            case 21: // Controls\TaskItemControl.xaml line 57
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element21 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element21).Click += this.SetPriorityMedium_Click;
                }
                break;
            case 22: // Controls\TaskItemControl.xaml line 62
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element22 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element22).Click += this.SetPriorityLow_Click;
                }
                break;
            case 23: // Controls\TaskItemControl.xaml line 43
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element23 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element23).Click += this.SetDueDateToday_Click;
                }
                break;
            case 24: // Controls\TaskItemControl.xaml line 44
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element24 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element24).Click += this.SetDueDateTomorrow_Click;
                }
                break;
            case 25: // Controls\TaskItemControl.xaml line 45
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element25 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element25).Click += this.SetDueDate3Days_Click;
                }
                break;
            case 26: // Controls\TaskItemControl.xaml line 46
                {
                    global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem element26 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem>(target);
                    ((global::Microsoft.UI.Xaml.Controls.MenuFlyoutItem)element26).Click += this.SetDueDateUndefined_Click;
                }
                break;
            case 27: // Controls\TaskItemControl.xaml line 167
                {
                    this.ColorIndicator = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 28: // Controls\TaskItemControl.xaml line 168
                {
                    this.TaskIconBorder = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Border>(target);
                }
                break;
            case 29: // Controls\TaskItemControl.xaml line 171
                {
                    this.TaskCheckBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CheckBox>(target);
                }
                break;
            case 30: // Controls\TaskItemControl.xaml line 176
                {
                    this.StartTaskButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.StartTaskButton).Click += this.StartTaskButton_Click;
                }
                break;
            case 31: // Controls\TaskItemControl.xaml line 194
                {
                    this.TaskTitleTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBlock)this.TaskTitleTextBlock).Tapped += this.TaskTitleTextBlock_Tapped;
                }
                break;
            case 32: // Controls\TaskItemControl.xaml line 202
                {
                    this.TaskDueDateTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBlock)this.TaskDueDateTextBlock).Tapped += this.TaskTitleTextBlock_Tapped;
                }
                break;
            case 33: // Controls\TaskItemControl.xaml line 208
                {
                    this.SubTaskBadge = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            switch(connectionId)
            {
            case 1: // Controls\TaskItemControl.xaml line 3
                {                    
                    global::Microsoft.UI.Xaml.Controls.UserControl element1 = (global::Microsoft.UI.Xaml.Controls.UserControl)target;
                    TaskItemControl_obj1_Bindings bindings = new TaskItemControl_obj1_Bindings();
                    returnValue = bindings;
                    bindings.SetDataRoot(this);
                    bindings.SetConverterLookupRoot(this);
                    this.Bindings = bindings;
                    element1.Loading += bindings.Loading;
                    global::Microsoft.UI.Xaml.Markup.XamlBindingHelper.SetDataTemplateComponent(element1, bindings);
                }
                break;
            }
            return returnValue;
        }
    }
}

