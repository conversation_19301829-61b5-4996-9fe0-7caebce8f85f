using System;
using System.Runtime.InteropServices;
using Microsoft.UI.Xaml;
using WinRT.Interop;

namespace HddtodoUI.Utilities
{
    /// <summary>
    /// 系统托盘图标服务类，使用 Win32 API 实现
    /// </summary>
    public class TrayIconService : IDisposable
    {
        // Win32 常量
        private const int WM_USER = 0x0400;
        private const int WM_TRAYICON = WM_USER + 1;
        private const int WM_LBUTTONUP = 0x0202;
        private const int WM_RBUTTONUP = 0x0205;
        private const int NIM_ADD = 0x00000000;
        private const int NIM_MODIFY = 0x00000001;
        private const int NIM_DELETE = 0x00000002;
        private const int NIF_MESSAGE = 0x00000001;
        private const int NIF_ICON = 0x00000002;
        private const int NIF_TIP = 0x00000004;
        private const int NOTIFYICON_VERSION = 4;
        private const int TPM_LEFTALIGN = 0x0000;
        private const int TPM_RIGHTBUTTON = 0x0002;
        private const int WM_COMMAND = 0x0111;
        private const int IDM_EXIT = 1001;

        // 托盘图标数据结构
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct NOTIFYICONDATA
        {
            public int cbSize;
            public IntPtr hWnd;
            public int uID;
            public int uFlags;
            public int uCallbackMessage;
            public IntPtr hIcon;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
            public string szTip;
        }

        // Win32 API 导入
        [DllImport("shell32.dll", CharSet = CharSet.Auto)]
        private static extern bool Shell_NotifyIcon(int dwMessage, ref NOTIFYICONDATA pnid);

        [DllImport("user32.dll")]
        private static extern IntPtr CreatePopupMenu();

        [DllImport("user32.dll")]
        private static extern bool InsertMenu(IntPtr hMenu, int position, int flags, int uIDNewItem, string lpNewItem);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool TrackPopupMenu(IntPtr hMenu, int uFlags, int x, int y, int nReserved, IntPtr hWnd, IntPtr prcRect);

        [DllImport("user32.dll")]
        private static extern bool DestroyMenu(IntPtr hMenu);

        [DllImport("user32.dll")]
        private static extern bool PostMessage(IntPtr hWnd, int Msg, IntPtr wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [StructLayout(LayoutKind.Sequential)]
        private struct POINT
        {
            public int X;
            public int Y;
        }

        [DllImport("user32.dll")]
        private static extern IntPtr LoadIcon(IntPtr hInstance, IntPtr lpIconName);

        [DllImport("user32.dll")]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern IntPtr LoadImage(IntPtr hinst, string lpszName, uint uType, int cxDesired, int cyDesired, uint fuLoad);

        private const int IMAGE_ICON = 1;
        private const uint LR_LOADFROMFILE = 0x00000010;
        private const uint LR_DEFAULTSIZE = 0x00000040;

        private NOTIFYICONDATA _notifyIconData;
        private Window _window;
        private IntPtr _windowHandle;
        private bool _disposed = false;
        private WndProc _wndProcDelegate;
        private IntPtr _oldWndProc;

        private delegate IntPtr WndProc(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam);

        // 根据平台选择正确的 API
        [DllImport("user32.dll", EntryPoint = "SetWindowLong", SetLastError = true)]
        private static extern IntPtr SetWindowLongPtr32(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr", SetLastError = true)]
        private static extern IntPtr SetWindowLongPtr64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        // 根据平台选择正确的 API
        private static IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            if (IntPtr.Size == 8)
                return SetWindowLongPtr64(hWnd, nIndex, dwNewLong);
            else
                return SetWindowLongPtr32(hWnd, nIndex, dwNewLong);
        }

        [DllImport("user32.dll")]
        private static extern IntPtr CallWindowProc(IntPtr lpPrevWndFunc, IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        private const int GWLP_WNDPROC = -4;

        public TrayIconService(Window window)
        {
            _window = window;
            _windowHandle = WindowNative.GetWindowHandle(window);
            
            // 设置窗口过程
            _wndProcDelegate = new WndProc(WindowProc);
            _oldWndProc = SetWindowLongPtr(_windowHandle, GWLP_WNDPROC, Marshal.GetFunctionPointerForDelegate(_wndProcDelegate));
            
            InitializeTrayIcon();
        }

        private void InitializeTrayIcon()
        {
            // 初始化托盘图标数据
            _notifyIconData = new NOTIFYICONDATA
            {
                cbSize = Marshal.SizeOf(typeof(NOTIFYICONDATA)),
                hWnd = _windowHandle,
                uID = 1,
                uFlags = NIF_MESSAGE | NIF_ICON | NIF_TIP,
                uCallbackMessage = WM_TRAYICON,
                hIcon = GetAppIcon(),
                szTip = "HDD Todo UI"
            };

            // 添加托盘图标
            Shell_NotifyIcon(NIM_ADD, ref _notifyIconData);
        }

        private IntPtr GetAppIcon()
        {
            // 使用自定义图标文件
            string iconPath = AppDomain.CurrentDomain.BaseDirectory + "\\Assets\\HddTodoIcon.ico";
            if (System.IO.File.Exists(iconPath))
            {
                return LoadImage(IntPtr.Zero, iconPath, IMAGE_ICON, 0, 0, LR_LOADFROMFILE | LR_DEFAULTSIZE);
            }
            
            // 如果自定义图标文件不存在，则使用应用程序默认图标
            return LoadIcon(IntPtr.Zero, new IntPtr(32512)); // IDI_APPLICATION
        }

        private IntPtr WindowProc(IntPtr hWnd, uint msg, IntPtr wParam, IntPtr lParam)
        {
            if (msg == WM_TRAYICON && wParam.ToInt32() == 1)
            {
                // 托盘图标消息
                int lParamLow = lParam.ToInt32() & 0xFFFF;
                if (lParamLow == WM_RBUTTONUP)
                {
                    // 右键点击托盘图标，显示菜单
                    ShowTrayMenu();
                    return IntPtr.Zero;
                }
                else if (lParamLow == WM_LBUTTONUP)
                {
                    // 左键点击托盘图标，显示窗口
                    ShowWindow();
                    return IntPtr.Zero;
                }
            }
            else if (msg == WM_COMMAND)
            {
                // 菜单命令
                int menuId = wParam.ToInt32() & 0xFFFF;
                if (menuId == IDM_EXIT)
                {
                    // 退出应用程序
                    ExitApplication();
                    return IntPtr.Zero;
                }
            }

            // 调用原始窗口过程
            return CallWindowProc(_oldWndProc, hWnd, msg, wParam, lParam);
        }

        private void ShowTrayMenu()
        {
            // 创建弹出菜单
            IntPtr hMenu = CreatePopupMenu();
            InsertMenu(hMenu, 0, 0, IDM_EXIT, "退出程序");

            // 获取鼠标位置
            POINT cursorPos;
            GetCursorPos(out cursorPos);

            // 设置前台窗口
            SetForegroundWindow(_windowHandle);

            // 显示菜单
            TrackPopupMenu(hMenu, TPM_LEFTALIGN | TPM_RIGHTBUTTON, cursorPos.X, cursorPos.Y, 0, _windowHandle, IntPtr.Zero);

            // 销毁菜单
            DestroyMenu(hMenu);
        }

        private void ShowWindow()
        {
            WindowsUtils.ShowWindowHandle(_windowHandle);
            WindowsUtils.BringToForeground(_windowHandle);
        }

        private void ExitApplication()
        {
            // 完全退出应用程序
            if (_window is HddtodoUI.MainWindow mainWindow)
            {
                mainWindow.CloseForReal();
            }
            else
            {
                Application.Current.Exit();
                Environment.Exit(0); // 确保完全终止进程
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 移除托盘图标
                    Shell_NotifyIcon(NIM_DELETE, ref _notifyIconData);

                    // 恢复原始窗口过程
                    if (_oldWndProc != IntPtr.Zero)
                    {
                        SetWindowLongPtr(_windowHandle, GWLP_WNDPROC, _oldWndProc);
                    }
                }

                _disposed = true;
            }
        }
    }
}
