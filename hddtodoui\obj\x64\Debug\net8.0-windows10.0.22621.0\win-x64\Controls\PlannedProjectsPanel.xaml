﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.PlannedProjectsPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:backend="using:HddtodoUI.BackendModels"
    xmlns:controls="using:HddtodoUI.Controls"
    xmlns:backendStore="using:HddtodoUI.BackendModels.BackendStore"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <Style x:Key="ProjectTitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextBlockStyle}">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>
    </UserControl.Resources>

    <Grid x:ConnectionId='2' Name="mainGrid" Padding="32,24" Background="{ThemeResource WindowBackgroundBrush}"
          HorizontalAlignment="Stretch" VerticalAlignment="Stretch" MinWidth="0">
        <Grid.Resources>
            <CollectionViewSource x:ConnectionId='3' x:Name="PlannedProjectsCVS"                                                        
                                  IsSourceGrouped="True" />
        </Grid.Resources>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header 区域 -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <TextBlock x:ConnectionId='8' x:Name="HeaderText" Grid.Column="0" Margin="0,0,0,0" Style="{StaticResource SubHeaderTextStyle}" />
            <!-- 如需右侧操作按钮，可参考TasksPanel添加StackPanel -->
        </Grid>


        <!-- List 区域 -->
        <ListView x:ConnectionId='4' x:Name="ProjectsListView" Grid.Row="1"
                  HorizontalContentAlignment="Stretch"
                  ItemsSource="{Binding Source={StaticResource PlannedProjectsCVS}}" SelectionMode="None"
                  HorizontalAlignment="Stretch">
 

            <ListView.GroupStyle>
                <GroupStyle>
                    <GroupStyle.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel>
                                <Border Padding="12,6" Margin="0,16,0,0" HorizontalAlignment="Stretch">
                                    <TextBlock Text="{Binding Key}" Style="{StaticResource CategoryHeaderTextStyle}" />
                                </Border>
                              
                            </StackPanel>
                        </DataTemplate>
                    </GroupStyle.HeaderTemplate>
                 
                </GroupStyle>
            </ListView.GroupStyle>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Margin" Value="0,4,0,4" />
                    <Setter Property="MinHeight" Value="0" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.ItemTemplate>
                <DataTemplate                                                >
                    <controls:TaskCategoryItemForPlanControl x:ConnectionId='7' 
                                                    
                                                                 
                                                       />
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>
    </Grid>

</UserControl>

