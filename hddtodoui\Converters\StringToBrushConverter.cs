using Microsoft.UI;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;
using System;

namespace HddtodoUI.Converters
{
    public class StringToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is string colorString && !string.IsNullOrEmpty(colorString))
            {
                try
                {
                    // 尝试将颜色字符串转换为SolidColorBrush
                    if (colorString.StartsWith("#") && colorString.Length == 7)
                    {
                        byte r = System.Convert.ToByte(colorString.Substring(1, 2), 16);
                        byte g = System.Convert.ToByte(colorString.Substring(3, 2), 16);
                        byte b = System.Convert.ToByte(colorString.Substring(5, 2), 16);
                        
                        return new SolidColorBrush(Microsoft.UI.ColorHelper.FromArgb(255, r, g, b));
                    }
                }
                catch
                {
                    // 如果转换失败，返回透明色
                    return new SolidColorBrush(Colors.Transparent);
                }
            }
            
            // 如果值为null或空字符串，返回透明色
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            // 不需要实现从Brush转回String的逻辑
            throw new NotImplementedException();
        }
    }
}
