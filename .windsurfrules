每次回答请再第一句话告知我您是否已经读取了这个文件（要告知读取了那个文件名）


这是一个任务管理软件， 目前的模式是： 任务有任务类别，任务类别有2种，一种是系统计算出来的， 比如：今天到期的任务，7天内到期的任务， 重要的任务，回收站等， 另外一种是用户自定义的，
用户自定义的任务类别可以添加，可以修改，可以删除，系统计算的任务无法添加修改删除。  任务可以自建任务类别组织自己的任务，也可以查看完成的任务。

写代码之前要记住的：
1、项目根目录在D:\netProject\newhddtodoui\hddtodoui文件夹,所有修改或者添加的代码都要在这个文件夹里面，所有的终端命令在这个文件夹下运行
2、修改代码前确认不要修改无关的代码
3、这是一个winui3项目，不要把wpf等其他项目的属性、方法、事件等复制到winui3这个项目中

代码写完后
1、代码是否都修改成功
2、代码是否完整
3、所有需要using的有没有using
4、所修改或者新建的代码的属性是否存在并且正确的设置，
5、所有修改或者添加的方法是否存在并且已经实现
6、所有引用的类和实体名字是否存在，并且已经引用
5、界面上的元素前景色和背景色不要相同，否则就看不到内容导致判断错误
6、如果用户要求现在网上搜寻一下，就一定要按照用户的要求在网上搜索一下方案先
7、以上请一个一个告诉我是否核查成功




