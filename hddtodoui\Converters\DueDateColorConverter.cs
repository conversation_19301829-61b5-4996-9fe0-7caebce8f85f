using Microsoft.UI;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;
using System;

namespace HddtodoUI.Converters
{
    public class DueDateColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is DateTime dateTime)
            {
                var daysUntilDue = (dateTime - DateTime.Now).TotalDays;

                if (daysUntilDue < 0) // 已过期
                {
                    return new SolidColorBrush(Colors.Red);
                }
                else if (daysUntilDue <= 1) // 1天内
                {
                    return new SolidColorBrush(Colors.Orange);
                }
                else if (daysUntilDue <= 3) // 3天内
                {
                    return new SolidColorBrush(Colors.Yellow);
                }
                else // 3天以上
                {
                    return new SolidColorBrush(Colors.Green);
                }
            }

            return new SolidColorBrush(Colors.Gray); // 默认颜色
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
