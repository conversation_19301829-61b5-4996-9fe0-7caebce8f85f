using System;

namespace HddtodoUI.Utilities
{
    /// <summary>
    /// Provides globally accessible client information such as the unique clientId and the fixed clientType.
    /// The clientId is generated once on first run and persisted in <see cref="ApplicationData.LocalSettings"/> so that it
    /// survives application restarts and user log-ins. It will only be regenerated if the app is re-installed and the local
    /// settings are cleared.
    /// </summary>
    public static class ClientInfoProvider
    {
        private const string ClientIdKey = "ClientId";
        private static readonly Lazy<string> _clientId = new(InitialiseClientId);

        /// <summary>
        /// Gets the unique identifier for this client installation.
        /// </summary>
        public static string ClientId => _clientId.Value;

        /// <summary>
        /// The hard-coded client type for this application.
        /// </summary>
        public const string ClientType = "pc";

        private static string InitialiseClientId()
        {
            // Try to get existing client ID from config
            var existingId = ConfigSettingsUtils.GetConfigValue(ClientIdKey, string.Empty);
            if (!string.IsNullOrWhiteSpace(existingId))
            {
                return existingId;
            }

            // Generate new UUID and persist to config
            var newId = Guid.NewGuid().ToString();
            ConfigSettingsUtils.SetConfigValue(ClientIdKey, newId);
            return newId;
        }
    }
}
