using System;
using HddtodoUI.TaskTomatoManager;

namespace HddtodoUI.Utilities;

public static class DateUtils
{
    public static string getUserFriendlyDateString(DateTime? date)
    {
        if (date == null) return string.Empty;

        DateTime today = DateTime.Now.Date;
        if (date == today) return "今天";
        if (date == today.AddDays(-1)) return "昨天";
        if (date == today.AddDays(1)) return "明天";

        return date.Value.ToString("MM/dd");
    }

    public static bool IsDateBelongToTodayDueTask(DateTime? date)
    {
        return SystemCategoryManager.IsDateBelongToTodayDueTask(date);
    }

    public static bool IsDateBelongToWeekDueTask(DateTime? date)
    {
        return SystemCategoryManager.IsDateBelongToWeekDueTask(date);
    }
}