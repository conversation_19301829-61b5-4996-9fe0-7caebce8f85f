using System;
using System.Text.Json;
using System.Text.Json.Serialization;



namespace HddtodoUI.BackendModels.JsonConverters
{
    public class NullableEnumConverter<T> : JsonConverter<T?> where T : struct, Enum
    {
        private readonly JsonConverter<T> _converter;
        private readonly Type _type;

        public NullableEnumConverter() : this(null) { }

        public NullableEnumConverter(JsonSerializerOptions options)
        {
            _converter = (JsonConverter<T>)options?.GetConverter(typeof(T));
            _type = typeof(T);
        }

        public override T? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }

            string value = reader.GetString();
            if (string.IsNullOrEmpty(value)) return null;

            // 特殊处理TaskPeriod
            if (typeof(T) == typeof(TaskPeriod))
            {
                switch (value.ToUpper())
                {
                    case "NONE": return (T?)(object)TaskPeriod.none;
                    case "DAILY": return (T?)(object)TaskPeriod.daily;
                    case "WEEKLY": return (T?)(object)TaskPeriod.weekly;
                    case "MONTHLY": return (T?)(object)TaskPeriod.monthly;
                    case "YEARLY": return (T?)(object)TaskPeriod.yearly;
                    case "HOUR": return (T?)(object)TaskPeriod.hour;
                }
            }

          

            // 尝试常规的枚举解析
            if (Enum.TryParse<T>(value, true, out var result))
            {
                return result;
            }

            throw new JsonException($"Unable to convert \"{value}\" to enum {_type}");
        }

        public override void Write(Utf8JsonWriter writer, T? value, JsonSerializerOptions options)
        {
            if (!value.HasValue)
            {
                writer.WriteNullValue();
                return;
            }

            // 特殊处理TaskPeriod
            if (typeof(T) == typeof(TaskPeriod))
            {
                var periodValue = (TaskPeriod)(object)value.Value;
                string stringValue = periodValue switch
                {
                    TaskPeriod.none => "NONE",
                    TaskPeriod.daily => "DAILY",
                    TaskPeriod.weekly => "WEEKLY",
                    TaskPeriod.monthly => "MONTHLY",
                    TaskPeriod.yearly => "YEARLY",
                    TaskPeriod.hour => "HOUR",
                    _ => value.Value.ToString().ToUpper()
                };
                writer.WriteStringValue(stringValue);
                return;
            }

            
            writer.WriteStringValue(value.Value.ToString().ToUpper());
        }
    }
}
