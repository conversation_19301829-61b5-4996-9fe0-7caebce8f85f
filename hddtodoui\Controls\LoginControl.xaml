<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.LoginControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <UserControl.Resources>
        <Style x:Key="LoginTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>

        <Style x:Key="LoginTextBoxStyle" TargetType="TextBox">
            <Setter Property="Margin" Value="0,0,0,16" />
            <Setter Property="Height" Value="40" />
        </Style>

        <Style x:Key="LoginPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Margin" Value="0,0,0,16" />
            <Setter Property="Height" Value="40" />
        </Style>
    </UserControl.Resources>

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <StackPanel VerticalAlignment="Center" Padding="32">
            <!-- Logo and Title -->
            <Image Source="/Assets/Square44x44Logo.png"
                   Width="80" Height="80"
                   Margin="0,0,0,16" />

            <TextBlock Text="HddTodo"
                       FontSize="28"
                       FontWeight="SemiBold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,32" />

            <!-- Login Form -->
            <TextBlock Text="用户名" Style="{StaticResource LoginTextBlockStyle}" />
            <TextBox x:Name="UsernameTextBox"
                     PlaceholderText="请输入用户名"
                     Style="{StaticResource LoginTextBoxStyle}"
                     KeyDown="UsernameTextBox_KeyDown" />

            <TextBlock Text="密码" Style="{StaticResource LoginTextBlockStyle}" />
            <PasswordBox x:Name="PasswordBox"
                         PlaceholderText="请输入密码"
                         Style="{StaticResource LoginPasswordBoxStyle}"
                         KeyDown="PasswordBox_KeyDown" />

            <!-- Remember Password Checkbox -->
            <CheckBox x:Name="RememberPasswordCheckBox" 
                      Content="记住用户名和密码" 
                      Margin="0,0,0,16" />

            <!-- Error Message -->
            <TextBlock x:Name="ErrorMessageTextBlock"
                       Foreground="Red"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"
                       Margin="0,0,0,16" />

            <!-- Buttons -->
            <Grid Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!-- Exit Button -->
                <Button x:Name="ExitButton"
                        Content="退出"
                        HorizontalAlignment="Stretch"
                        Height="44"
                        Margin="0,0,4,0"
                        Click="ExitButton_Click" />

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                        Content="登录"
                        Grid.Column="1"
                        HorizontalAlignment="Stretch"
                        Height="44"
                        Margin="4,0,0,0"
                        Style="{StaticResource AccentButtonStyle}"
                        Click="LoginButton_Click" />
            </Grid>

            <!-- Loading Indicator -->
            <ProgressRing x:Name="LoadingProgressRing"
                          IsActive="False"
                          Margin="0,8,0,0"
                          Width="24" Height="24"
                          HorizontalAlignment="Center"
                          Visibility="Collapsed" />
        </StackPanel>
    </Grid>
</UserControl>